-- Initialize database for Cula Document Extractor
-- This script creates the necessary tables and indexes

-- Create database if not exists (this is handled by POSTGRES_DB env var)
\c extractor_db;

-- Create tables for document extraction system
CREATE TABLE IF NOT EXISTS documents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    filename VARCHAR(255) NOT NULL,
    file_path TEXT NOT NULL,
    cloud_storage_id VARCHAR(255),
    content_type VARCHAR(100),
    file_size BIGINT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS extractions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id UUID REFERENCES documents(id) ON DELETE CASCADE,
    method VARCHAR(50) NOT NULL,
    processing_time REAL,
    extracted_data JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_documents_cloud_storage_id ON documents(cloud_storage_id);
CREATE INDEX IF NOT EXISTS idx_documents_filename ON documents(filename);
CREATE INDEX IF NOT EXISTS idx_extractions_document_id ON extractions(document_id);
CREATE INDEX IF NOT EXISTS idx_extractions_method ON extractions(method);

-- Create tables for sync status tracking
CREATE TABLE IF NOT EXISTS sync_operations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    dataset_id VARCHAR(255) NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'syncing',
    document_count INTEGER DEFAULT 0,
    message TEXT,
    progress REAL DEFAULT 0.0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS idx_sync_operations_dataset_id ON sync_operations(dataset_id);

-- Mock tables for demo purposes (matching the query patterns in the frontend)
CREATE TABLE IF NOT EXISTS sites (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS file_references (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    cloud_storage_id VARCHAR(255) NOT NULL,
    filename VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS step_executions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    site_id UUID REFERENCES sites(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS emissions_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS deliveries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS proof_slot_configs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    object_type VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS proof_sources (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    file_reference_id UUID REFERENCES file_references(id),
    step_execution_id UUID REFERENCES step_executions(id),
    emissions_log_id UUID REFERENCES emissions_logs(id),
    delivery_id UUID REFERENCES deliveries(id),
    running_delivery_id UUID REFERENCES deliveries(id),
    proof_slot_config_id UUID REFERENCES proof_slot_configs(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Insert sample data for testing
INSERT INTO sites (name) VALUES 
    ('ÖKT Test Site'),
    ('Block Bio Osterrade'),
    ('Demo Site 1')
ON CONFLICT DO NOTHING;

INSERT INTO file_references (cloud_storage_id, filename) VALUES
    ('test-file-1.pdf', 'delivery_receipt_001.pdf'),
    ('test-file-2.pdf', 'moisture_report_001.pdf'),
    ('test-file-3.pdf', 'supplier_invoice_001.pdf')
ON CONFLICT DO NOTHING;

-- Link the data together
WITH site_data AS (SELECT id, name FROM sites),
     file_data AS (SELECT id, cloud_storage_id FROM file_references),
     step_exec AS (
         INSERT INTO step_executions (site_id) 
         SELECT id FROM site_data 
         RETURNING id, site_id
     ),
     proof_config AS (
         INSERT INTO proof_slot_configs (object_type) 
         VALUES ('delivery_receipt') 
         RETURNING id
     )
INSERT INTO proof_sources (file_reference_id, step_execution_id, proof_slot_config_id)
SELECT f.id, se.id, pc.id
FROM file_data f
CROSS JOIN step_exec se
CROSS JOIN proof_config pc
ON CONFLICT DO NOTHING;

-- Database initialization script

-- Create the main database schema
CREATE SCHEMA IF NOT EXISTS public;

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create a function to send notifications for document changes
CREATE OR REPLACE FUNCTION notify_document_change()
RETURNS TRIGGER AS $$
DECLARE
    payload JSON;
BEGIN
    -- Determine the notification type based on table and operation
    CASE TG_TABLE_NAME
        WHEN 'extractions' THEN
            payload = json_build_object(
                'document_id', COALESCE(NEW.document_id, OLD.document_id),
                'extraction_id', COALESCE(NEW.id, OLD.id),
                'operation', TG_OP,
                'extraction_config_id', COALESCE(NEW.extraction_config_id, OLD.extraction_config_id),
                'timestamp', extract(epoch from now()),
                'table', TG_TABLE_NAME
            );
            PERFORM pg_notify('document_extraction_complete', payload::text);
            
        WHEN 'ground_truth' THEN
            payload = json_build_object(
                'document_id', COALESCE(NEW.document_id, OLD.document_id),
                'field_name', COALESCE(NEW.field_name, OLD.field_name),
                'operation', TG_OP,
                'timestamp', extract(epoch from now()),
                'table', TG_TABLE_NAME
            );
            PERFORM pg_notify('document_ground_truth_updated', payload::text);
            
        -- Add other tables as needed
        ELSE
            payload = json_build_object(
                'table', TG_TABLE_NAME,
                'operation', TG_OP,
                'timestamp', extract(epoch from now())
            );
            PERFORM pg_notify('document_changed', payload::text);
    END CASE;
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Function to notify approval status changes
CREATE OR REPLACE FUNCTION notify_approval_change()
RETURNS TRIGGER AS $$
DECLARE
    payload JSON;
BEGIN
    payload = json_build_object(
        'document_id', COALESCE(NEW.document_id, OLD.document_id),
        'extraction_config_id', COALESCE(NEW.extraction_config_id, OLD.extraction_config_id),
        'approved', CASE WHEN NEW IS NOT NULL THEN NEW.approved ELSE NULL END,
        'operation', TG_OP,
        'timestamp', extract(epoch from now()),
        'table', 'document_approvals'
    );
    
    PERFORM pg_notify('document_approval_changed', payload::text);
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Function to create reactive triggers on tables
CREATE OR REPLACE FUNCTION create_reactive_triggers()
RETURNS VOID AS $$
BEGIN
    -- Check if extractions table exists and create trigger
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'extractions') THEN
        DROP TRIGGER IF EXISTS extractions_notify_trigger ON extractions;
        CREATE TRIGGER extractions_notify_trigger
            AFTER INSERT OR UPDATE OR DELETE ON extractions
            FOR EACH ROW EXECUTE FUNCTION notify_document_change();
    END IF;
    
    -- Check if ground_truth table exists and create trigger  
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'ground_truth') THEN
        DROP TRIGGER IF EXISTS ground_truth_notify_trigger ON ground_truth;
        CREATE TRIGGER ground_truth_notify_trigger
            AFTER INSERT OR UPDATE OR DELETE ON ground_truth
            FOR EACH ROW EXECUTE FUNCTION notify_document_change();
    END IF;
    
    -- Add triggers for any approval tables when they exist
    -- This will be called after tables are created by the application
END;
$$ LANGUAGE plpgsql;

-- Create a function to add approval triggers (to be called after app creates tables)
CREATE OR REPLACE FUNCTION add_approval_triggers(table_name TEXT)
RETURNS VOID AS $$
BEGIN
    EXECUTE format('DROP TRIGGER IF EXISTS %I_approval_trigger ON %I', table_name, table_name);
    EXECUTE format('CREATE TRIGGER %I_approval_trigger 
                    AFTER INSERT OR UPDATE OR DELETE ON %I
                    FOR EACH ROW EXECUTE FUNCTION notify_approval_change()', 
                   table_name, table_name);
END;
$$ LANGUAGE plpgsql;

-- Function to test notifications (development only)
CREATE OR REPLACE FUNCTION test_document_notification(
    channel TEXT,
    doc_id TEXT,
    message TEXT DEFAULT 'Test notification'
)
RETURNS VOID AS $$
DECLARE
    payload JSON;
BEGIN
    payload = json_build_object(
        'document_id', doc_id,
        'message', message,
        'timestamp', extract(epoch from now()),
        'test', true
    );
    
    PERFORM pg_notify(channel, payload::text);
END;
$$ LANGUAGE plpgsql;

-- Example usage (uncomment for testing):
-- SELECT test_document_notification('document_extraction_complete', 'test-doc-123', 'Test extraction complete');

GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO postgres;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO postgres; 
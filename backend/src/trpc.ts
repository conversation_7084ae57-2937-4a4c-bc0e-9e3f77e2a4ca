import { initTRPC } from '@trpc/server';

// Create context
export const createContext = () => ({
  // Add any context data here (user, session, etc.)
});

export type Context = Awaited<ReturnType<typeof createContext>>;

// Initialize tRPC
const t = initTRPC.context<Context>().create();

// Export reusable router and procedure helpers
export const router = t.router;
export const procedure = t.procedure;
export const middleware = t.middleware; 
import { db } from './index.js';
import { datasets, extractionConfigs } from './schema.js';
import { eq } from 'drizzle-orm';
import { config } from 'dotenv';

config({ path: '.env.local' });

async function updateExtractionConfigRelationships() {
  console.log('🔄 Updating extraction config relationships...');
  
  try {
    // Get all datasets that have extraction configs
    const datasetsWithConfigs = await db
      .select({
        datasetId: datasets.id,
        extractionConfigId: datasets.extractionConfigId,
      })
      .from(datasets)
      .where(datasets.extractionConfigId);

    console.log(`Found ${datasetsWithConfigs.length} datasets with extraction configs`);

    // Update each extraction config with its dataset ID
    for (const { datasetId, extractionConfigId } of datasetsWithConfigs) {
      if (extractionConfigId) {
        await db
          .update(extractionConfigs)
          .set({ datasetId })
          .where(eq(extractionConfigs.id, extractionConfigId));
        
        console.log(`✅ Updated extraction config ${extractionConfigId} -> dataset ${datasetId}`);
      }
    }

    console.log('✅ All extraction config relationships updated successfully');
  } catch (error) {
    console.error('❌ Failed to update extraction config relationships:', error);
    throw error;
  }
}

updateExtractionConfigRelationships()
  .then(() => process.exit(0))
  .catch(() => process.exit(1));
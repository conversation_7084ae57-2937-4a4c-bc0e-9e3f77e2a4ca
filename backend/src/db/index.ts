import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import * as schema from './schema.js';
import { config } from 'dotenv';

config({ path: '.env.local' });

// Main Database connection (our schema with datasets, documents, etc.)
const connectionString = process.env.DATABASE_CONNECTION_STRING || '';

const client = postgres(connectionString);
export const db = drizzle(client, { schema });

// Cula Database connection (for dataset queries)
const culaConnectionString = process.env.CULA_DATABASE_CONNECTION_STRING || '';
const culaClient = postgres(culaConnectionString);
export const culaDb = drizzle(culaClient);

export * from './schema.js'; 
import { pgTable, uuid, varchar, text, integer, timestamp, boolean, jsonb, decimal, unique } from 'drizzle-orm/pg-core';

// Datasets table
export const datasets = pgTable('datasets', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: varchar('name', { length: 255 }).notNull(),
  description: text('description'),
  query: text('query').notNull(),
  extractionConfigId: uuid('extraction_config_id').references(() => extractionConfigs.id),
  version: integer('version').notNull().default(1),
  documentCount: integer('document_count').notNull().default(0),
  status: varchar('status', { length: 50 }).notNull().default('ready'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// Documents table
export const documents = pgTable('documents', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: varchar('name', { length: 255 }).notNull(),
  path: varchar('path', { length: 1000 }).notNull(),
  type: varchar('type', { length: 100 }).notNull(),
  size: integer('size').notNull(),
  datasetId: uuid('dataset_id').references(() => datasets.id, { onDelete: 'cascade' }),
  gcsPath: varchar('gcs_path', { length: 1000 }),
  folderPath: varchar('folder_path', { length: 500 }),
  fileName: varchar('file_name', { length: 255 }),
  objectType: varchar('object_type', { length: 100 }),
  siteName: varchar('site_name', { length: 255 }),
  emissionsLogId: varchar('emissions_log_id', { length: 255 }),
  runningDeliveryId: varchar('running_delivery_id', { length: 255 }),
  stepExecutionId: varchar('step_execution_id', { length: 255 }),
  previewUrl: varchar('preview_url', { length: 1000 }),
  isApproved: boolean('is_approved').notNull().default(false),
  approvedAt: timestamp('approved_at'),
  approvedBy: varchar('approved_by', { length: 255 }),
  canApprove: boolean('can_approve').notNull().default(false),
  approvalReason: text('approval_reason'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// Extraction Configs table
export const extractionConfigs = pgTable('extraction_configs', {
  id: uuid('id').primaryKey().defaultRandom(),
  datasetId: uuid('dataset_id').references(() => datasets.id, { onDelete: 'cascade' }),
  data: jsonb('data').notNull(),
  version: integer('version').notNull().default(1),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// Extractions table
export const extractions = pgTable('extractions', {
  id: uuid('id').primaryKey().defaultRandom(),
  documentId: uuid('document_id').notNull().references(() => documents.id, { onDelete: 'cascade' }),
  extractionConfigId: uuid('extraction_config_id').notNull().references(() => extractionConfigs.id),
  provider: varchar('provider', { length: 100 }).notNull(),
  model: varchar('model', { length: 100 }).notNull(),
  processingTime: decimal('processing_time', { precision: 10, scale: 3 }).notNull().default('0'),
  data: jsonb('data'),
  status: varchar('status', { length: 20 }).notNull().default('pending'), // pending, running, completed, failed
  error: text('error'),
  rawResponse: jsonb('raw_response'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
},
(table) => {
    return {
        unq: unique().on(table.documentId, table.model),
    };
});

// Ground Truth table
export const groundTruths = pgTable('ground_truths', {
  id: uuid('id').primaryKey().defaultRandom(),
  documentId: uuid('document_id').notNull().references(() => documents.id, { onDelete: 'cascade' }),
  extractionConfigId: uuid('extraction_config_id').notNull().references(() => extractionConfigs.id),
  fieldName: varchar('field_name', { length: 255 }).notNull(),
  extractedValue: jsonb('extracted_value'),
  userAnnotation: jsonb('user_annotation'),
  markedNotExtractable: boolean('marked_not_extractable').notNull().default(false),
  fieldType: varchar('field_type', { length: 100 }).notNull(),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

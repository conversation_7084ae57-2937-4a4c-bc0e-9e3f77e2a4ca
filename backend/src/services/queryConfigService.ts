import { culaDb } from '../db/index.js';
import { sql } from 'drizzle-orm';
import type { Document } from '../types/resource.js';

export interface SiteOption {
  id: string;
  name: string;
  eventCounts: {
    stepExecutions: number;
    deliveries: number;
    emissionLogs: number;
  };
}

export interface EventTypeConfig {
  type: 'step_execution' | 'delivery' | 'emission_log';
  displayName: string;
  configCount: number;
  fileCount?: number;
  sampleConfigs: Array<{
    id: string;
    name: string;
    description?: string;
  }>;
}

export interface StepExecutionType {
  type: string;
  displayName: string;
  documentCount: number;
}

export interface QueryGenerationInput {
  siteId: string;
  eventType: 'step_execution' | 'delivery' | 'emission_log';
  configIds?: string[];
  stepExecutionTypes?: string[];
  fileTypes?: string[];
  deliveryConfigIds?: string[];
}

export class QueryConfigService {
  private getConnection() {
    if (!culaDb) {
      console.warn('🟨 External database connection not available. Query config will be disabled.');
      return null;
    }
    
    const culaConnectionString = process.env.CULA_DATABASE_CONNECTION_STRING || '';
    if (!culaConnectionString) {
      console.warn('🟨 CULA_DATABASE_CONNECTION_STRING environment variable not set. Query config will be disabled.');
      return null;
    }
    
    return culaDb;
  }

  async getAvailableSites(): Promise<SiteOption[]> {
    const connection = this.getConnection();
    if (!connection) return [];

    try {
      
      const query = `
        WITH file_entity_counts AS (
          SELECT
            COALESCE(
              el_site.id,
              d_site.id,
              rd_site.id,
              se_site.id
            ) as site_id,
            COALESCE(
              el_site.name,
              d_site.name,
              rd_site.name,
              se_site.name
            ) as site_name,
            CASE
              WHEN ps.emissions_log_id IS NOT NULL THEN 'emission_log'
              WHEN ps.delivery_id IS NOT NULL THEN 'delivery'
              WHEN ps.running_delivery_id IS NOT NULL THEN 'running_delivery'
              WHEN ps.step_execution_id IS NOT NULL THEN 'step_execution'
              ELSE 'orphaned'
            END as entity_type
          FROM file_references fr
          LEFT JOIN proof_sources ps ON ps.file_reference_id = fr.id
          LEFT JOIN emissions_logs el ON ps.emissions_log_id = el.id
          LEFT JOIN sites el_site ON el.site_id = el_site.id
          LEFT JOIN deliveries d ON ps.delivery_id = d.id
          LEFT JOIN sites d_site ON d.issuing_site_id = d_site.id
          LEFT JOIN running_deliveries rd ON ps.running_delivery_id = rd.id
          LEFT JOIN sites rd_site ON rd.site_id = rd_site.id
          LEFT JOIN step_executions se ON ps.step_execution_id = se.id
          LEFT JOIN sites se_site ON se.site_id = se_site.id
          WHERE COALESCE(
            el_site.id,
            d_site.id,
            rd_site.id,
            se_site.id
          ) IS NOT NULL
        ),
        site_counts AS (
          SELECT 
            site_id as id,
            site_name as name,
            COUNT(CASE WHEN entity_type = 'step_execution' THEN 1 END) as step_execution_count,
            COUNT(CASE WHEN entity_type = 'delivery' THEN 1 END) as delivery_count,
            COUNT(CASE WHEN entity_type = 'emission_log' THEN 1 END) as emission_log_count
          FROM file_entity_counts
          WHERE site_name IS NOT NULL
          GROUP BY site_id, site_name
          HAVING (
            COUNT(CASE WHEN entity_type = 'step_execution' THEN 1 END) > 0 OR
            COUNT(CASE WHEN entity_type = 'delivery' THEN 1 END) > 0 OR
            COUNT(CASE WHEN entity_type = 'emission_log' THEN 1 END) > 0
          )
        )
        SELECT id, name, step_execution_count, delivery_count, emission_log_count 
        FROM site_counts
        ORDER BY (step_execution_count + delivery_count + emission_log_count) DESC;
      `;

      const result = await connection.execute(sql.raw(query));
      
      if (result.length === 0) {
        console.log('🔍 No sites with events found, falling back to all sites...');
        const fallbackQuery = 'SELECT id, name FROM sites ORDER BY name';
        const allSites = await connection.execute(sql.raw(fallbackQuery));
        console.log('📊 Found', allSites.length, 'total sites');
        
        return allSites.map((row: any) => ({
          id: row.id,
          name: row.name,
          eventCounts: {
            stepExecutions: 0,
            deliveries: 0,
            emissionLogs: 0
          }
        }));
      }
      
      return result.map((row: any) => ({
        id: row.id,
        name: row.name,
        eventCounts: {
          stepExecutions: parseInt(row.step_execution_count) || 0,
          deliveries: parseInt(row.delivery_count) || 0,
          emissionLogs: parseInt(row.emission_log_count) || 0
        }
      }));
    } catch (error) {
      console.error('❌ Failed to get available sites:', error);
      return [];
    }
  }

  async getEventTypeConfigs(siteId: string): Promise<EventTypeConfig[]> {
    const connection = this.getConnection();
    if (!connection) return [];

    try {
      const configs: EventTypeConfig[] = [];

      // Use the same query structure as the site counting to get event type information
      const eventTypeQuery = `
        WITH file_entity_counts AS (
          SELECT
            COALESCE(
              el_site.id,
              d_site.id,
              rd_site.id, 
              se_site.id
            ) as site_id,
            CASE
              WHEN ps.emissions_log_id IS NOT NULL THEN 'emission_log'
              WHEN ps.delivery_id IS NOT NULL THEN 'delivery'
              WHEN ps.running_delivery_id IS NOT NULL THEN 'running_delivery'
              WHEN ps.step_execution_id IS NOT NULL THEN 'step_execution'
              ELSE 'orphaned'
            END as entity_type,
            COALESCE(
              ps.emissions_log_id,
              ps.delivery_id,
              ps.running_delivery_id,
              ps.step_execution_id
            ) as entity_id,
            fr.id as file_id
          FROM file_references fr
          LEFT JOIN proof_sources ps ON ps.file_reference_id = fr.id
          LEFT JOIN emissions_logs el ON ps.emissions_log_id = el.id
          LEFT JOIN sites el_site ON el.site_id = el_site.id
          LEFT JOIN deliveries d ON ps.delivery_id = d.id
          LEFT JOIN sites d_site ON (d.consumer_site_id = d_site.id OR d.issuing_site_id = d_site.id)
          LEFT JOIN running_deliveries rd ON ps.running_delivery_id = rd.id
          LEFT JOIN sites rd_site ON rd.site_id = rd_site.id
          LEFT JOIN step_executions se ON ps.step_execution_id = se.id
          LEFT JOIN sites se_site ON se.site_id = se_site.id
          WHERE COALESCE(
            el_site.id,
            d_site.id,
            rd_site.id,
            se_site.id
          ) = '${siteId}'
        )
        SELECT 
          entity_type,
          COUNT(DISTINCT entity_id) as entity_count,
          COUNT(DISTINCT file_id) as file_count
        FROM file_entity_counts
        WHERE entity_type != 'orphaned'
        AND entity_id IS NOT NULL
        GROUP BY entity_type
        ORDER BY entity_count DESC;
      `;

      const eventTypeResult = await connection.execute(sql.raw(eventTypeQuery));
      console.log(`🔍 Found event types for site ${siteId}:`, eventTypeResult);

      for (const row of eventTypeResult) {
        const entityType = row.entity_type;
        const entityCount = parseInt(row.entity_count || '0');
        const fileCount = parseInt(row.file_count || '0');

        if (entityCount > 0) {
          let displayName = '';
          let sampleConfigs: any[] = [];

          if (entityType === 'step_execution') {
            displayName = 'Step Executions';
            
            // Get sample step execution configs
            const stepConfigQuery = `
              SELECT DISTINCT
                sc.id,
                sc.name,
                COUNT(se.id) as execution_count
              FROM step_configs sc
              JOIN step_executions se ON se.step_config_id = sc.id
              WHERE se.site_id = '${siteId}'
              GROUP BY sc.id, sc.name
              ORDER BY execution_count DESC
              LIMIT 3;
            `;
            
            const stepConfigResult = await connection.execute(sql.raw(stepConfigQuery));
            sampleConfigs = stepConfigResult.map((configRow: any) => ({
              id: configRow.id,
              name: configRow.name || 'Step Config',
              description: `${configRow.execution_count} executions`
            }));
          } 
          else if (entityType === 'delivery') {
            displayName = 'Deliveries';
            
            // Get sample deliveries (deliveries table may not have name or created columns)
            const deliveryQuery = `
              SELECT DISTINCT
                d.id,
                'Delivery #' || d.id as name
              FROM deliveries d
              JOIN proof_sources ps ON ps.delivery_id = d.id
              JOIN file_references fr ON fr.id = ps.file_reference_id
              WHERE (d.consumer_site_id = '${siteId}' OR d.issuing_site_id = '${siteId}')
              ORDER BY d.id DESC
              LIMIT 3;
            `;
            
            const deliveryResult = await connection.execute(sql.raw(deliveryQuery));
            sampleConfigs = deliveryResult.map((deliveryRow: any) => ({
              id: deliveryRow.id,
              name: deliveryRow.name || 'Delivery',
              description: 'Delivery document'
            }));
          }
          else if (entityType === 'emission_log') {
            displayName = 'Emission Logs';
            
            // Get sample emission logs (emissions_logs table may not have name or created columns)
            const emissionLogQuery = `
              SELECT DISTINCT
                el.id,
                'Emission Log #' || el.id as name
              FROM emissions_logs el
              JOIN proof_sources ps ON ps.emissions_log_id = el.id
              JOIN file_references fr ON fr.id = ps.file_reference_id
              WHERE el.site_id = '${siteId}'
              ORDER BY el.id DESC
              LIMIT 3;
            `;
            
            const emissionLogResult = await connection.execute(sql.raw(emissionLogQuery));
            sampleConfigs = emissionLogResult.map((emissionRow: any) => ({
              id: emissionRow.id,
              name: emissionRow.name || 'Emission Log',
              description: 'Emission log document'
            }));
          }

          if (displayName) {
            configs.push({
              type: entityType as 'step_execution' | 'delivery' | 'emission_log',
              displayName,
              configCount: entityCount,
              fileCount: fileCount,
              sampleConfigs: sampleConfigs.length > 0 ? sampleConfigs : [{
                id: 'default',
                name: `${displayName} (${entityCount} items)`,
                description: `${fileCount} files available`
              }]
            });
          }
        }
      }

      console.log(`✅ Returning ${configs.length} event type configs for site ${siteId}`);
      return configs;
    } catch (error) {
      console.error('❌ Failed to get event type configs:', error);
      return [];
    }
  }

  async getStepExecutionTypes(siteId: string): Promise<StepExecutionType[]> {
    const connection = this.getConnection();
    if (!connection) return [];

    try {
      const query = `
        SELECT 
          se.type,
          COUNT(DISTINCT fr.id) as document_count
        FROM step_executions se
        JOIN proof_sources ps ON ps.step_execution_id = se.id
        JOIN file_references fr ON ps.file_reference_id = fr.id
        WHERE se.site_id = '${siteId}'
        AND se.type IS NOT NULL
        GROUP BY se.type
        ORDER BY document_count DESC;
      `;

      const result = await connection.execute(sql.raw(query));
      
      return result.map((row: any) => ({
        type: row.type,
        displayName: this.formatStepExecutionTypeName(row.type),
        documentCount: parseInt(row.document_count) || 0
      }));
    } catch (error) {
      console.error('❌ Failed to get step execution types:', error);
      return [];
    }
  }

  async getDeliveryConfigs(siteId: string): Promise<Array<{id: string, name: string, documentCount: number}>> {
    const connection = this.getConnection();
    if (!connection) return [];

    try {
      const query = `
        SELECT 
          dc.id,
          dc.name,
          COUNT(DISTINCT fr.id) as document_count
        FROM delivery_configs dc
        LEFT JOIN deliveries d ON d.delivery_config_id = dc.id
        LEFT JOIN proof_sources ps ON ps.delivery_id = d.id
        LEFT JOIN file_references fr ON fr.id = ps.file_reference_id
        LEFT JOIN sites s ON (d.consumer_site_id = s.id OR d.issuing_site_id = s.id)
        WHERE s.id = '${siteId}'
        AND fr.file_name IS NOT NULL
        AND fr.cloud_storage_id IS NOT NULL
        AND fr.cloud_storage_id != ''
        GROUP BY dc.id, dc.name
        HAVING COUNT(DISTINCT fr.id) > 0
        ORDER BY document_count DESC, dc.name;
      `;

      const result = await connection.execute(sql.raw(query));
      
      return result.map((row: any) => ({
        id: row.id,
        name: row.name,
        documentCount: parseInt(row.document_count) || 0
      }));
    } catch (error) {
      console.error('❌ Failed to get delivery configs:', error);
      return [];
    }
  }

  private formatStepExecutionTypeName(type: string): string {
    if (!type) return 'Unknown Type';
    
    // Convert snake_case or kebab-case to Title Case
    return type
      .replace(/[_-]/g, ' ')
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  }

  async getDocumentTypesForEventType(
    siteId: string, 
    eventType: 'step_execution' | 'delivery' | 'emission_log',
    stepExecutionTypes?: string[],
    deliveryConfigIds?: string[]
  ): Promise<Array<{extension: string, count: number}>> {
    const connection = this.getConnection();
    if (!connection) return [];

    try {
      let query = '';
      
      if (eventType === 'step_execution') {
        let stepTypeFilter = '';
        if (stepExecutionTypes && stepExecutionTypes.length > 0) {
          const stepTypeList = stepExecutionTypes.map(type => `'${type}'`).join(', ');
          stepTypeFilter = ` AND se.type IN (${stepTypeList})`;
        }

        query = `
          SELECT 
            CASE 
              WHEN fr.file_name LIKE '%.pdf' THEN 'pdf'
              WHEN fr.file_name LIKE '%.png' THEN 'png'
              WHEN fr.file_name LIKE '%.jpg' THEN 'jpg'
              WHEN fr.file_name LIKE '%.jpeg' THEN 'jpeg'
              WHEN fr.file_name LIKE '%.gif' THEN 'gif'
              WHEN fr.file_name LIKE '%.tiff' THEN 'tiff'
              WHEN fr.file_name LIKE '%.bmp' THEN 'bmp'
              WHEN fr.file_name LIKE '%.webp' THEN 'webp'
              ELSE 'unknown'
            END as file_extension,
            COUNT(*) as file_count
          FROM file_references fr
          JOIN proof_sources ps ON ps.file_reference_id = fr.id
          JOIN step_executions se ON ps.step_execution_id = se.id
          JOIN sites s ON se.site_id = s.id
          WHERE s.id = '${siteId}'
          AND fr.file_name IS NOT NULL
          ${stepTypeFilter}
          GROUP BY file_extension
          ORDER BY file_count DESC;
        `;
      } else if (eventType === 'delivery') {
        let deliveryConfigFilter = '';
        if (deliveryConfigIds && deliveryConfigIds.length > 0) {
          const configIdList = deliveryConfigIds.map(id => `'${id}'`).join(', ');
          deliveryConfigFilter = ` AND d.delivery_config_id IN (${configIdList})`;
        }
        
        query = `
          SELECT 
            CASE 
              WHEN fr.file_name LIKE '%.pdf' THEN 'pdf'
              WHEN fr.file_name LIKE '%.png' THEN 'png'
              WHEN fr.file_name LIKE '%.jpg' THEN 'jpg'
              WHEN fr.file_name LIKE '%.jpeg' THEN 'jpeg'
              WHEN fr.file_name LIKE '%.gif' THEN 'gif'
              WHEN fr.file_name LIKE '%.tiff' THEN 'tiff'
              WHEN fr.file_name LIKE '%.bmp' THEN 'bmp'
              WHEN fr.file_name LIKE '%.webp' THEN 'webp'
              ELSE 'unknown'
            END as file_extension,
            COUNT(*) as file_count
          FROM file_references fr
          JOIN proof_sources ps ON ps.file_reference_id = fr.id
          JOIN deliveries d ON ps.delivery_id = d.id
          JOIN sites s ON (d.consumer_site_id = s.id OR d.issuing_site_id = s.id)
          WHERE s.id = '${siteId}'
          AND fr.file_name IS NOT NULL
          ${deliveryConfigFilter}
          GROUP BY file_extension
          ORDER BY file_count DESC;
        `;
      } else if (eventType === 'emission_log') {
        query = `
          SELECT 
            CASE 
              WHEN fr.file_name LIKE '%.pdf' THEN 'pdf'
              WHEN fr.file_name LIKE '%.png' THEN 'png'
              WHEN fr.file_name LIKE '%.jpg' THEN 'jpg'
              WHEN fr.file_name LIKE '%.jpeg' THEN 'jpeg'
              WHEN fr.file_name LIKE '%.gif' THEN 'gif'
              WHEN fr.file_name LIKE '%.tiff' THEN 'tiff'
              WHEN fr.file_name LIKE '%.bmp' THEN 'bmp'
              WHEN fr.file_name LIKE '%.webp' THEN 'webp'
              ELSE 'unknown'
            END as file_extension,
            COUNT(*) as file_count
          FROM file_references fr
          JOIN proof_sources ps ON ps.file_reference_id = fr.id
          JOIN emissions_logs el ON ps.emissions_log_id = el.id
          JOIN sites s ON el.site_id = s.id
          WHERE s.id = '${siteId}'
          AND fr.file_name IS NOT NULL
          GROUP BY file_extension
          ORDER BY file_count DESC;
        `;
      }

      const result = await connection.execute(sql.raw(query));
      
      return result.map((row: any) => ({
        extension: row.file_extension,
        count: parseInt(row.file_count) || 0
      }));
    } catch (error) {
      console.error('❌ Failed to get document types for event type:', error);
      return [];
    }
  }


  async getDocumentsForEventType(siteId: string, eventType: 'step_execution' | 'delivery' | 'emission_log', fileTypes?: string[], limit?: number, stepExecutionTypes?: string[], deliveryConfigIds?: string[]): Promise<Partial<Document>[]> {
    const connection = this.getConnection();
    if (!connection) return [];

    try {
      let query = '';
      
      // Build file type filter
      let fileTypeFilter = '';
      if (fileTypes && fileTypes.length > 0) {
        const conditions = fileTypes.map(ext => {
          if (ext === 'unknown') {
            return `(fr.file_name NOT LIKE '%.pdf' AND fr.file_name NOT LIKE '%.png' AND fr.file_name NOT LIKE '%.jpg' AND fr.file_name NOT LIKE '%.jpeg' AND fr.file_name NOT LIKE '%.gif' AND fr.file_name NOT LIKE '%.tiff' AND fr.file_name NOT LIKE '%.bmp' AND fr.file_name NOT LIKE '%.webp')`;
          }
          return `fr.file_name LIKE '%.${ext}'`;
        }).join(' OR ');
        fileTypeFilter = ` AND (${conditions})`;
      }

      if (eventType === 'step_execution') {
        let stepTypeFilter = '';
        if (stepExecutionTypes && stepExecutionTypes.length > 0) {
          const stepTypeList = stepExecutionTypes.map(type => `'${type}'`).join(', ');
          stepTypeFilter = ` AND se.type IN (${stepTypeList})`;
        }

        query = `
          SELECT DISTINCT
            fr.id,
            fr.file_name,
            fr.cloud_storage_id,
            CASE 
              WHEN fr.file_name LIKE '%.pdf' THEN 'application/pdf'
              WHEN fr.file_name LIKE '%.png' THEN 'image/png'
              WHEN fr.file_name LIKE '%.jpg' THEN 'image/jpeg'
              WHEN fr.file_name LIKE '%.jpeg' THEN 'image/jpeg'
              ELSE 'application/octet-stream'
            END as type,
            se.id as step_execution_id,
            sc.name as step_config_name,
            se.type as execution_type
          FROM file_references fr
          JOIN proof_sources ps ON ps.file_reference_id = fr.id
          JOIN step_executions se ON ps.step_execution_id = se.id
          JOIN step_configs sc ON se.step_config_id = sc.id
          JOIN sites s ON se.site_id = s.id
          WHERE s.id = '${siteId}'
          AND fr.file_name IS NOT NULL
          AND fr.cloud_storage_id IS NOT NULL
          AND fr.cloud_storage_id != ''
          ${stepTypeFilter}
          ${fileTypeFilter}
          ORDER BY fr.id DESC${limit ? `
          LIMIT ${limit}` : ''};
        `;
      } else if (eventType === 'delivery') {
        let deliveryConfigFilter = '';
        if (deliveryConfigIds && deliveryConfigIds.length > 0) {
          const configIdList = deliveryConfigIds.map(id => `'${id}'`).join(', ');
          deliveryConfigFilter = ` AND d.delivery_config_id IN (${configIdList})`;
        }

        query = `
          SELECT DISTINCT
            fr.id,
            fr.file_name,
            fr.cloud_storage_id,
            CASE 
              WHEN fr.file_name LIKE '%.pdf' THEN 'application/pdf'
              WHEN fr.file_name LIKE '%.png' THEN 'image/png'
              WHEN fr.file_name LIKE '%.jpg' THEN 'image/jpeg'
              WHEN fr.file_name LIKE '%.jpeg' THEN 'image/jpeg'
              ELSE 'application/octet-stream'
            END as type,
            d.id as delivery_id,
            dc.name as delivery_config_name
          FROM file_references fr
          JOIN proof_sources ps ON ps.file_reference_id = fr.id
          JOIN deliveries d ON ps.delivery_id = d.id
          JOIN delivery_configs dc ON d.delivery_config_id = dc.id
          JOIN sites s ON (d.consumer_site_id = s.id OR d.issuing_site_id = s.id)
          WHERE s.id = '${siteId}'
          AND fr.file_name IS NOT NULL
          AND fr.cloud_storage_id IS NOT NULL
          AND fr.cloud_storage_id != ''
          ${deliveryConfigFilter}
          ${fileTypeFilter}
          ORDER BY fr.id DESC${limit ? `
          LIMIT ${limit}` : ''};
        `;
      } else if (eventType === 'emission_log') {
        query = `
          SELECT DISTINCT
            fr.id,
            fr.file_name,
            fr.cloud_storage_id,
            CASE 
              WHEN fr.file_name LIKE '%.pdf' THEN 'application/pdf'
              WHEN fr.file_name LIKE '%.png' THEN 'image/png'
              WHEN fr.file_name LIKE '%.jpg' THEN 'image/jpeg'
              WHEN fr.file_name LIKE '%.jpeg' THEN 'image/jpeg'
              ELSE 'application/octet-stream'
            END as type,
            el.id as emission_log_id
          FROM file_references fr
          JOIN proof_sources ps ON ps.file_reference_id = fr.id
          JOIN emissions_logs el ON ps.emissions_log_id = el.id
          JOIN sites s ON el.site_id = s.id
          WHERE s.id = '${siteId}'
          AND fr.file_name IS NOT NULL
          AND fr.cloud_storage_id IS NOT NULL
          AND fr.cloud_storage_id != ''
          ${fileTypeFilter}
          ORDER BY fr.id DESC${limit ? `
          LIMIT ${limit}` : ''};
        `;
      }

      const result = await connection.execute(sql.raw(query));
      console.log(`📄 Found ${result.length} documents for ${eventType} in site ${siteId}`);
      console.log(`🔍 Query parameters: stepExecutionTypes=${JSON.stringify(stepExecutionTypes)}, fileTypes=${JSON.stringify(fileTypes)}, limit=${limit}`);
      
      // Log first few documents to see their structure
      if (result.length > 0) {
        console.log(`📄 First 3 documents from query:`, JSON.stringify(result.slice(0, 3), null, 2));
      }
      
      return result.map((row: any) => ({
        id: row.id,
        name: row.file_name,
        fileName: row.file_name,
        gcsPath: row.cloud_storage_id,
        type: row.type,
        size: 0, // Size not needed for streaming
        path: row.cloud_storage_id || '', // Use cloud_storage_id as path fallback
        // These fields are required by Document type but not available in external DB
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        // Include additional context based on event type
        ...(eventType === 'step_execution' && {
          stepExecutionId: row.step_execution_id,
          stepConfigName: row.step_config_name,
          executionType: row.execution_type
        }),
        ...(eventType === 'delivery' && {
          deliveryId: row.delivery_id
        }),
        ...(eventType === 'emission_log' && {
          emissionLogId: row.emission_log_id
        })
      } as Partial<Document>));
    } catch (error) {
      console.error('❌ Failed to get documents for event type:', error);
      return [];
    }
  }

  async getDocumentById(documentId: string): Promise<Partial<Document> | null> {
    const connection = this.getConnection();
    if (!connection) return null;

    try {
      const query = `
        SELECT 
          fr.id,
          fr.file_name,
          fr.cloud_storage_id,
          CASE 
            WHEN fr.file_name LIKE '%.pdf' THEN 'application/pdf'
            WHEN fr.file_name LIKE '%.png' THEN 'image/png'
            WHEN fr.file_name LIKE '%.jpg' THEN 'image/jpeg'
            WHEN fr.file_name LIKE '%.jpeg' THEN 'image/jpeg'
            ELSE 'application/octet-stream'
          END as type
        FROM file_references fr
        WHERE fr.id = '${documentId}'
        LIMIT 1;
      `;

      const result = await connection.execute(sql.raw(query));
      console.log(`🔍 getDocumentById(${documentId}): found ${result.length} results`);
      
      if (result.length === 0) {
        return null;
      }

      const row = result[0];
      console.log(`🔍 getDocumentById RAW ROW:`, JSON.stringify(row, null, 2));
      
      const documentResult = {
        id: row.id,
        name: row.file_name,
        fileName: row.file_name,
        gcsPath: row.cloud_storage_id,
        type: row.type,
        size: 0, // Size not needed for streaming
        path: row.cloud_storage_id || '', // Use cloud_storage_id as path fallback
        // These fields are required by Document type but not available in external DB
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      } as Partial<Document>;
      
      console.log(`🔍 getDocumentById RESULT:`, JSON.stringify(documentResult, null, 2));
      return documentResult;
    } catch (error) {
      console.error('❌ Failed to get document by ID:', error);
      return null;
    }
  }

  generateQuery(input: QueryGenerationInput): string {
    const { siteId, eventType, configIds, stepExecutionTypes, fileTypes, deliveryConfigIds } = input;
    
    let query = '';

    // Build file type filter
    let fileTypeFilter = '';
    if (fileTypes && fileTypes.length > 0) {
      const conditions = fileTypes.map(ext => {
        if (ext === 'unknown') {
          return `(fr.file_name NOT LIKE '%.pdf' AND fr.file_name NOT LIKE '%.png' AND fr.file_name NOT LIKE '%.jpg' AND fr.file_name NOT LIKE '%.jpeg' AND fr.file_name NOT LIKE '%.gif' AND fr.file_name NOT LIKE '%.tiff' AND fr.file_name NOT LIKE '%.bmp' AND fr.file_name NOT LIKE '%.webp')`;
        }
        return `fr.file_name LIKE '%.${ext}'`;
      }).join(' OR ');
      fileTypeFilter = ` AND (${conditions})`;
    }

    if (eventType === 'step_execution') {
      let configFilter = '';
      if (configIds && configIds.length > 0) {
        const configIdList = configIds.map(id => `'${id}'`).join(', ');
        configFilter = ` AND sc.id IN (${configIdList})`;
      }

      let stepTypeFilter = '';
      if (stepExecutionTypes && stepExecutionTypes.length > 0) {
        const stepTypeList = stepExecutionTypes.map(type => `'${type}'`).join(', ');
        stepTypeFilter = ` AND se.type IN (${stepTypeList})`;
      }

      query = `
        -- Query for step_execution events
        SELECT DISTINCT
          fr.id as document_id,
          fr.file_name,
          fr.cloud_storage_id,
          fr.size,
          se.id as event_id,
          'step_execution' as event_type,
          s.name as site_name
        FROM file_references fr
        JOIN proof_sources ps ON ps.file_reference_id = fr.id
        JOIN step_executions se ON ps.step_execution_id = se.id
        JOIN sites s ON se.site_id = s.id
        LEFT JOIN step_configs sc ON se.step_config_id = sc.id
        WHERE s.id = '${siteId}'
        AND fr.file_name IS NOT NULL
        ${configFilter}
        ${stepTypeFilter}
        ${fileTypeFilter}
      `;
    } else if (eventType === 'delivery') {
      let deliveryConfigFilter = '';
      if (deliveryConfigIds && deliveryConfigIds.length > 0) {
        const configIdList = deliveryConfigIds.map(id => `'${id}'`).join(', ');
        deliveryConfigFilter = ` AND d.delivery_config_id IN (${configIdList})`;
      }
      query = `
        -- Query for delivery events
        SELECT DISTINCT
          fr.id as document_id,
          fr.file_name,
          fr.cloud_storage_id,
          fr.size,
          d.id as event_id,
          'delivery' as event_type,
          s.name as site_name
        FROM file_references fr
        JOIN proof_sources ps ON ps.file_reference_id = fr.id
        JOIN deliveries d ON ps.delivery_id = d.id
        JOIN sites s ON (d.consumer_site_id = s.id OR d.issuing_site_id = s.id)
        WHERE s.id = '${siteId}'
        AND fr.file_name IS NOT NULL
        ${deliveryConfigFilter}
        ${fileTypeFilter}
      `;
    } else if (eventType === 'emission_log') {
      query = `
        -- Query for emission_log events
        SELECT DISTINCT
          fr.id as document_id,
          fr.file_name,
          fr.cloud_storage_id,
          fr.size,
          el.id as event_id,
          'emission_log' as event_type,
          s.name as site_name
        FROM file_references fr
        JOIN proof_sources ps ON ps.file_reference_id = fr.id
        JOIN emissions_logs el ON ps.emissions_log_id = el.id
        JOIN sites s ON el.site_id = s.id
        WHERE s.id = '${siteId}'
        AND fr.file_name IS NOT NULL
        ${fileTypeFilter}
      `;
    } else {
      throw new Error(`Unsupported event type: ${eventType}`);
    }

    return query.trim();
  }

  async previewQuery(query: string): Promise<{ count: number, documents: Partial<Document>[] }> {
    const connection = this.getConnection();
    if (!connection) return { count: 0, documents: [] };

    try {
      const cleanQuery = query.trim().replace(/;$/, '');
      // First, get the total count
      const countQuery = `SELECT COUNT(*) as total FROM (${cleanQuery}) as subquery;`;
      const countResult = await connection.execute(sql.raw(countQuery));
      const count = parseInt(countResult[0]?.total || '0');

      // Then, get a preview of the documents
      const previewQuery = `${cleanQuery};`;
      const documentsResult = await connection.execute(sql.raw(previewQuery));

      const documents = documentsResult.map((row: any) => ({
        id: row.document_id,
        name: row.file_name,
        fileName: row.file_name,
        gcsPath: row.cloud_storage_id,
        type: 'unknown', // Type detection can be added if needed
        createdAt: row.event_date || new Date().toISOString(),
        updatedAt: row.event_date || new Date().toISOString(),
      }));

      return { count, documents };
    } catch (error) {
      console.error('❌ Failed to preview query:', error);
      // If the query fails, return empty results
      return { count: 0, documents: [] };
    }
  }
}

export const queryConfigService = new QueryConfigService();
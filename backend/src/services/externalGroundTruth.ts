import { sql } from 'drizzle-orm';
import { culaDb } from '../db/index.js';

// This interface defines the structure of the data returned by our complex ground truth query.
interface GroundTruthQueryResult {
  type: string;
  name: string;
  value: any;
  inputs: any;
  data_point_config_id: string;
  input_label: string;
  input_value: any;
  document_id: string; // Added to group results by document
}

// This interface defines the structure of the detailed ground truth we'll return.
interface DetailedGroundTruth {
  extractedValue: any;
  userAnnotation?: any; // Annotations are not part of the new query, so this is optional.
  fieldType: string;    // We can infer this from the data type.
}

class ExternalGroundTruthService {
  private getConnection() {
    if (!culaDb) {
      console.warn('🟨 External database connection not available. Ground truth population will be disabled.');
      return null;
    }
    return culaDb;
  }

  // A helper to infer field type from its value.
  private inferFieldType(value: any): string {
    if (typeof value === 'boolean') return 'boolean';
    if (typeof value === 'number') return 'number';
    if (typeof value === 'string') {
      if (!isNaN(Date.parse(value))) return 'date';
      return 'text';
    }
    return 'text';
  }

  // The base SQL query to extract ground truth data from step executions.
  private getBaseQuery(): string {
    return `
      SELECT
          'step_execution' as source_type,
          se.type,
          dp.name,
          dp.type,
          dp.value,
          dp.inputs,
          dp.data_point_config_id,
          config_inputs_unnested.input_label,
          dp_inputs_unnested.input_value,
          fr.id as document_id
      FROM
          proof_sources ps
          LEFT JOIN file_references fr ON ps.file_reference_id = fr.id
          LEFT JOIN step_executions se ON ps.step_execution_id = se.id
          LEFT JOIN material_containers mc ON mc.producer_step_exe_id = se.id
          LEFT JOIN material_container_data_points mcdp ON mcdp.material_container_id = mc.id
          LEFT JOIN data_points dp ON mcdp.data_point_id = dp.id
          LEFT JOIN data_point_configs dpc ON dpc.id = dp.data_point_config_id
          LEFT JOIN LATERAL (
              SELECT
                  dp_input_elem -> 'input' ->> 'value' AS input_value,
                  dp_input_elem ->> 'inputConfigRef' AS input_config_ref
              FROM
                  jsonb_array_elements(dp.inputs::jsonb) AS dp_input_elem
          ) AS dp_inputs_unnested ON dp.inputs IS NOT NULL
          LEFT JOIN LATERAL (
              SELECT
                  cfg_input_elem ->> 'label' AS input_label,
                  cfg_input_elem ->> 'id' AS input_config_ref
              FROM
                  jsonb_array_elements(dpc.inputs::jsonb) AS cfg_input_elem
              WHERE
                  cfg_input_elem ->> 'id' = dp_inputs_unnested.input_config_ref
          ) AS config_inputs_unnested ON TRUE
    `;
  }

  // Query specifically for delivery data
  private getDeliveryQuery(): string {
    return `
      SELECT
          'delivery' as source_type,
          'delivery' as type,
          dp.name,
          dp.type,
          dp.value,
          dp.inputs,
          dp.data_point_config_id,
          dp.name as input_label,
          dp_inputs_unnested.input_value,
          fr.id as document_id
      FROM
          proof_sources ps
          LEFT JOIN file_references fr ON ps.file_reference_id = fr.id
          LEFT JOIN deliveries d ON ps.delivery_id = d.id
          LEFT JOIN delivery_data_points ddp ON ddp.delivery_id = d.id
          LEFT JOIN data_points dp ON ddp.data_point_id = dp.id
          LEFT JOIN data_point_configs dpc ON dpc.id = dp.data_point_config_id
          LEFT JOIN LATERAL (
              SELECT
                  dp_input_elem -> 'input' ->> 'value' AS input_value
              FROM
                  jsonb_array_elements(dp.inputs::jsonb) AS dp_input_elem
              LIMIT 1
          ) AS dp_inputs_unnested ON dp.inputs IS NOT NULL
      WHERE dp.inputs != '[]'
      
      UNION ALL
      
      SELECT
          'delivery' as source_type,
          'delivery' as type,
          'announce_timestamp' as name,
          'TIMESTAMP' as type,
          d.announce_timestamp as value,
          null::jsonb as inputs,
          null::uuid as data_point_config_id,
          'announce_timestamp' as input_label,
          d.announce_timestamp::text as input_value,
          fr.id as document_id
      FROM
          proof_sources ps
          LEFT JOIN file_references fr ON ps.file_reference_id = fr.id
          LEFT JOIN deliveries d ON ps.delivery_id = d.id
      WHERE d.announce_timestamp IS NOT NULL
    `;
  }


  /**
   * Get detailed ground truth data for a single document.
   */
  async getDetailedGroundTruthForDocument(documentId: string): Promise<Record<string, DetailedGroundTruth>> {
    const connection = this.getConnection();
    if (!connection) return {};

    try {
      // Combine step execution and delivery queries with proper UNION structure
      const stepExecutionQuery = `
        SELECT
            dp.type,
            dp.name,
            dp.value::text,
            dp.inputs,
            dp.data_point_config_id,
            config_inputs_unnested.input_label,
            dp_inputs_unnested.input_value,
            fr.id as document_id
        FROM
            proof_sources ps
            LEFT JOIN file_references fr ON ps.file_reference_id = fr.id
            LEFT JOIN step_executions se ON ps.step_execution_id = se.id
            LEFT JOIN material_containers mc ON mc.producer_step_exe_id = se.id
            LEFT JOIN material_container_data_points mcdp ON mcdp.material_container_id = mc.id
            LEFT JOIN data_points dp ON mcdp.data_point_id = dp.id
            LEFT JOIN data_point_configs dpc ON dpc.id = dp.data_point_config_id
            LEFT JOIN LATERAL (
                SELECT
                    dp_input_elem -> 'input' ->> 'value' AS input_value,
                    dp_input_elem ->> 'inputConfigRef' AS input_config_ref
                FROM
                    jsonb_array_elements(dp.inputs::jsonb) AS dp_input_elem
            ) AS dp_inputs_unnested ON dp.inputs IS NOT NULL
            LEFT JOIN LATERAL (
                SELECT
                    cfg_input_elem ->> 'label' AS input_label,
                    cfg_input_elem ->> 'id' AS input_config_ref
                FROM
                    jsonb_array_elements(dpc.inputs::jsonb) AS cfg_input_elem
                WHERE
                    cfg_input_elem ->> 'id' = dp_inputs_unnested.input_config_ref
            ) AS config_inputs_unnested ON TRUE
        WHERE fr.id = '${documentId}' AND dp.inputs != '[]'
      `;

      const deliveryInputsQuery = `
        SELECT
            dp.type,
            dp.name,
            dp.value::text,
            dp.inputs,
            dp.data_point_config_id,
            dp.name as input_label,
            dp_inputs_unnested.input_value,
            fr.id as document_id
        FROM
            proof_sources ps
            LEFT JOIN file_references fr ON ps.file_reference_id = fr.id
            LEFT JOIN deliveries d ON ps.delivery_id = d.id
            LEFT JOIN delivery_data_points ddp ON ddp.delivery_id = d.id
            LEFT JOIN data_points dp ON ddp.data_point_id = dp.id
            LEFT JOIN data_point_configs dpc ON dpc.id = dp.data_point_config_id
            LEFT JOIN LATERAL (
                SELECT
                    dp_input_elem -> 'input' ->> 'value' AS input_value
                FROM
                    jsonb_array_elements(dp.inputs::jsonb) AS dp_input_elem
                LIMIT 1
            ) AS dp_inputs_unnested ON dp.inputs IS NOT NULL
        WHERE fr.id = '${documentId}' AND dp.inputs != '[]'
      `;

      const deliveryMetadataQuery = `
        SELECT
            'STRING' as type,
            'announce_timestamp' as name,
            d.announce_timestamp::text as value,
            null::jsonb as inputs,
            null::uuid as data_point_config_id,
            'announce_timestamp' as input_label,
            d.announce_timestamp::text as input_value,
            fr.id as document_id
        FROM
            proof_sources ps
            LEFT JOIN file_references fr ON ps.file_reference_id = fr.id
            LEFT JOIN deliveries d ON ps.delivery_id = d.id
        WHERE fr.id = '${documentId}' AND d.announce_timestamp IS NOT NULL
      `;

      const stepExecutionMetadataQuery = `
        SELECT
            'STRING' as type,
            'type' as name,
            se.type::text as value,
            null::jsonb as inputs,
            null::uuid as data_point_config_id,
            'type' as input_label,
            se.type::text as input_value,
            fr.id as document_id
        FROM
            proof_sources ps
            LEFT JOIN file_references fr ON ps.file_reference_id = fr.id
            LEFT JOIN step_executions se ON ps.step_execution_id = se.id
        WHERE fr.id = '${documentId}' AND se.type IS NOT NULL
        
        UNION ALL
        
        SELECT
            'STRING' as type,
            'created' as name,
            se.created::text as value,
            null::jsonb as inputs,
            null::uuid as data_point_config_id,
            'created' as input_label,
            se.created::text as input_value,
            fr.id as document_id
        FROM
            proof_sources ps
            LEFT JOIN file_references fr ON ps.file_reference_id = fr.id
            LEFT JOIN step_executions se ON ps.step_execution_id = se.id
        WHERE fr.id = '${documentId}' AND se.created IS NOT NULL
      `;

      const emissionLogQuery = `
        SELECT
            dp.type,
            dp.name,
            dp.value::text,
            dp.inputs,
            dp.data_point_config_id,
            dp.name as input_label,
            dp_inputs_unnested.input_value,
            fr.id as document_id
        FROM
            proof_sources ps
            LEFT JOIN file_references fr ON ps.file_reference_id = fr.id
            LEFT JOIN emission_logs el ON ps.emission_log_id = el.id
            LEFT JOIN emission_log_data_points eldp ON eldp.emission_log_id = el.id
            LEFT JOIN data_points dp ON eldp.data_point_id = dp.id
            LEFT JOIN data_point_configs dpc ON dpc.id = dp.data_point_config_id
            LEFT JOIN LATERAL (
                SELECT
                    dp_input_elem -> 'input' ->> 'value' AS input_value
                FROM
                    jsonb_array_elements(dp.inputs::jsonb) AS dp_input_elem
                LIMIT 1
            ) AS dp_inputs_unnested ON dp.inputs IS NOT NULL
        WHERE fr.id = '${documentId}' AND dp.inputs != '[]'
      `;

      const emissionLogMetadataQuery = `
        SELECT
            'STRING' as type,
            'created' as name,
            el.created::text as value,
            null::jsonb as inputs,
            null::uuid as data_point_config_id,
            'created' as input_label,
            el.created::text as input_value,
            fr.id as document_id
        FROM
            proof_sources ps
            LEFT JOIN file_references fr ON ps.file_reference_id = fr.id
            LEFT JOIN emission_logs el ON ps.emission_log_id = el.id
        WHERE fr.id = '${documentId}' AND el.created IS NOT NULL
      `;

      const query = `
        ${stepExecutionQuery}
        UNION ALL
        ${stepExecutionMetadataQuery}
        UNION ALL
        ${deliveryInputsQuery}
        UNION ALL
        ${deliveryMetadataQuery}
        ORDER BY input_label
      `;
      
      console.log(`🔍 Executing ground truth query for document ${documentId}:`);
      console.log(query);
      
      const result = await connection.execute(sql.raw(query));
      console.log(`🔍 Query returned ${result.length} rows:`, JSON.stringify(result.slice(0, 5), null, 2));
      
      // Write debug info to file for debugging
      const fs = await import('fs');
      await fs.promises.writeFile('/tmp/ground_truth_debug.json', JSON.stringify({
        documentId,
        query,
        resultCount: result.length,
        results: result.slice(0, 10)
      }, null, 2));
      const detailedData: Record<string, DetailedGroundTruth> = {};

      for (const row of result) {
        const fieldName = row.input_label as string;
        const fieldValue = row.input_value;

        if (fieldName && fieldValue !== null) {
          detailedData[fieldName] = {
            extractedValue: fieldValue,
            fieldType: this.inferFieldType(fieldValue),
          };
        }
      }

      console.info(`🔍 Retrieved ${Object.keys(detailedData).length} ground truth records for document ${documentId}`);
      return detailedData;
    } catch (error) {
      console.error(`❌ Failed to fetch ground truth for document ${documentId}:`, error);
      return {};
    }
  }

  /**
   * Get ground truth data for multiple documents in a single query.
   */
  async getGroundTruthForDocuments(documentIds: string[]): Promise<Record<string, Record<string, DetailedGroundTruth>>> {
    if (documentIds.length === 0) return {};
    const connection = this.getConnection();
    if (!connection) return {};

    try {
      // Combine all queries: step executions and deliveries for multiple documents
      const documentIdList = documentIds.map(id => `'${id}'`).join(',');
      
      const stepExecutionQuery = `
        SELECT
            dp.type,
            dp.name,
            dp.value::text,
            dp.inputs,
            dp.data_point_config_id,
            config_inputs_unnested.input_label,
            dp_inputs_unnested.input_value,
            fr.id as document_id
        FROM
            proof_sources ps
            LEFT JOIN file_references fr ON ps.file_reference_id = fr.id
            LEFT JOIN step_executions se ON ps.step_execution_id = se.id
            LEFT JOIN material_containers mc ON mc.producer_step_exe_id = se.id
            LEFT JOIN material_container_data_points mcdp ON mcdp.material_container_id = mc.id
            LEFT JOIN data_points dp ON mcdp.data_point_id = dp.id
            LEFT JOIN data_point_configs dpc ON dpc.id = dp.data_point_config_id
            LEFT JOIN LATERAL (
                SELECT
                    dp_input_elem -> 'input' ->> 'value' AS input_value,
                    dp_input_elem ->> 'inputConfigRef' AS input_config_ref
                FROM
                    jsonb_array_elements(dp.inputs::jsonb) AS dp_input_elem
            ) AS dp_inputs_unnested ON dp.inputs IS NOT NULL
            LEFT JOIN LATERAL (
                SELECT
                    cfg_input_elem ->> 'label' AS input_label,
                    cfg_input_elem ->> 'id' AS input_config_ref
                FROM
                    jsonb_array_elements(dpc.inputs::jsonb) AS cfg_input_elem
                WHERE
                    cfg_input_elem ->> 'id' = dp_inputs_unnested.input_config_ref
            ) AS config_inputs_unnested ON TRUE
        WHERE fr.id IN (${documentIdList}) AND dp.inputs != '[]'
      `;

      const deliveryInputsQuery = `
        SELECT
            dp.type,
            dp.name,
            dp.value::text,
            dp.inputs,
            dp.data_point_config_id,
            dp.name as input_label,
            dp_inputs_unnested.input_value,
            fr.id as document_id
        FROM
            proof_sources ps
            LEFT JOIN file_references fr ON ps.file_reference_id = fr.id
            LEFT JOIN deliveries d ON ps.delivery_id = d.id
            LEFT JOIN delivery_data_points ddp ON ddp.delivery_id = d.id
            LEFT JOIN data_points dp ON ddp.data_point_id = dp.id
            LEFT JOIN data_point_configs dpc ON dpc.id = dp.data_point_config_id
            LEFT JOIN LATERAL (
                SELECT
                    dp_input_elem -> 'input' ->> 'value' AS input_value
                FROM
                    jsonb_array_elements(dp.inputs::jsonb) AS dp_input_elem
                LIMIT 1
            ) AS dp_inputs_unnested ON dp.inputs IS NOT NULL
        WHERE fr.id IN (${documentIdList}) AND dp.inputs != '[]'
      `;

      const deliveryMetadataQuery = `
        SELECT
            'STRING' as type,
            'announce_timestamp' as name,
            d.announce_timestamp::text as value,
            null::jsonb as inputs,
            null::uuid as data_point_config_id,
            'announce_timestamp' as input_label,
            d.announce_timestamp::text as input_value,
            fr.id as document_id
        FROM
            proof_sources ps
            LEFT JOIN file_references fr ON ps.file_reference_id = fr.id
            LEFT JOIN deliveries d ON ps.delivery_id = d.id
        WHERE fr.id IN (${documentIdList}) AND d.announce_timestamp IS NOT NULL
      `;

      const stepExecutionMetadataQuery = `
        SELECT
            'STRING' as type,
            'type' as name,
            se.type::text as value,
            null::jsonb as inputs,
            null::uuid as data_point_config_id,
            'type' as input_label,
            se.type::text as input_value,
            fr.id as document_id
        FROM
            proof_sources ps
            LEFT JOIN file_references fr ON ps.file_reference_id = fr.id
            LEFT JOIN step_executions se ON ps.step_execution_id = se.id
        WHERE fr.id IN (${documentIdList}) AND se.type IS NOT NULL
        
        UNION ALL
        
        SELECT
            'STRING' as type,
            'created' as name,
            se.created::text as value,
            null::jsonb as inputs,
            null::uuid as data_point_config_id,
            'created' as input_label,
            se.created::text as input_value,
            fr.id as document_id
        FROM
            proof_sources ps
            LEFT JOIN file_references fr ON ps.file_reference_id = fr.id
            LEFT JOIN step_executions se ON ps.step_execution_id = se.id
        WHERE fr.id IN (${documentIdList}) AND se.created IS NOT NULL
      `;

      const emissionLogQuery = `
        SELECT
            dp.type,
            dp.name,
            dp.value::text,
            dp.inputs,
            dp.data_point_config_id,
            dp.name as input_label,
            dp_inputs_unnested.input_value,
            fr.id as document_id
        FROM
            proof_sources ps
            LEFT JOIN file_references fr ON ps.file_reference_id = fr.id
            LEFT JOIN emission_logs el ON ps.emission_log_id = el.id
            LEFT JOIN emission_log_data_points eldp ON eldp.emission_log_id = el.id
            LEFT JOIN data_points dp ON eldp.data_point_id = dp.id
            LEFT JOIN data_point_configs dpc ON dpc.id = dp.data_point_config_id
            LEFT JOIN LATERAL (
                SELECT
                    dp_input_elem -> 'input' ->> 'value' AS input_value
                FROM
                    jsonb_array_elements(dp.inputs::jsonb) AS dp_input_elem
                LIMIT 1
            ) AS dp_inputs_unnested ON dp.inputs IS NOT NULL
        WHERE fr.id IN (${documentIdList}) AND dp.inputs != '[]'
      `;

      const emissionLogMetadataQuery = `
        SELECT
            'STRING' as type,
            'created' as name,
            el.created::text as value,
            null::jsonb as inputs,
            null::uuid as data_point_config_id,
            'created' as input_label,
            el.created::text as input_value,
            fr.id as document_id
        FROM
            proof_sources ps
            LEFT JOIN file_references fr ON ps.file_reference_id = fr.id
            LEFT JOIN emission_logs el ON ps.emission_log_id = el.id
        WHERE fr.id IN (${documentIdList}) AND el.created IS NOT NULL
      `;

      const query = `
        ${stepExecutionQuery}
        UNION ALL
        ${stepExecutionMetadataQuery}
        UNION ALL
        ${deliveryInputsQuery}  
        UNION ALL
        ${deliveryMetadataQuery}
        ORDER BY document_id, input_label
      `;

      const result = await connection.execute(sql.raw(query));
      const groupedData: Record<string, Record<string, DetailedGroundTruth>> = {};

      for (const row of result) {
        const docId = row.document_id as string;
        const fieldName = row.input_label as string;
        const fieldValue = row.input_value;

        if (docId && fieldName && fieldValue !== null) {
          if (!groupedData[docId]) {
            groupedData[docId] = {};
          }
          groupedData[docId][fieldName] = {
            extractedValue: fieldValue,
            fieldType: this.inferFieldType(fieldValue),
          };
        }
      }

      console.info(`🔍 Retrieved ground truth for ${Object.keys(groupedData).length} documents from external DB`);
      return groupedData;
    } catch (error) {
      console.error('❌ Failed to fetch ground truth from external database:', error);
      return {};
    }
  }

  /**
   * Get ground truth preview for specific fields on a document
   */
  async getFieldPreview(documentId: string, fieldNames: string[]): Promise<Record<string, any>> {
    console.log(`🔍 getFieldPreview called with documentId: ${documentId}, fieldNames: ${JSON.stringify(fieldNames)}`);
    
    const connection = this.getConnection();
    if (!connection) {
      console.log(`❌ No database connection available`);
      return {};
    }

    try {
      console.log(`🔍 Getting detailed ground truth for document ${documentId}`);
      // Get all ground truth for the document
      const allGroundTruth = await this.getDetailedGroundTruthForDocument(documentId);
      console.log(`🔍 Retrieved ground truth keys: ${Object.keys(allGroundTruth)}`);
      
      // Filter to only the requested fields
      const preview: Record<string, any> = {};
      for (const fieldName of fieldNames) {
        if (allGroundTruth[fieldName]) {
          preview[fieldName] = allGroundTruth[fieldName].extractedValue;
        } else {
          preview[fieldName] = null;
        }
      }

      console.info(`🔍 Retrieved field preview for ${fieldNames.length} fields on document ${documentId}`);
      return preview;
    } catch (error) {
      console.error(`❌ Failed to get field preview for document ${documentId}:`, error);
      return {};
    }
  }

  /**
   * Check if the external database is available.
   */
  async isAvailable(): Promise<boolean> {
    const connection = this.getConnection();
    if (!connection) return false;

    try {
      await connection.execute(sql`SELECT 1`);
      return true;
    } catch (error) {
      console.error('❌ External database availability check failed:', error);
      return false;
    }
  }
}

export const externalGroundTruthService = new ExternalGroundTruthService();

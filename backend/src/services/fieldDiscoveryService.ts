import { culaDb } from '../db/index.js';
import { sql } from 'drizzle-orm';

export interface FieldOption {
  name: string;
  description: string;
  fieldType: 'text' | 'number' | 'date' | 'boolean' | 'percentage' | 'currency' | 'email' | 'phone' | 'address';
  category: 'metadata' | 'input';
  frequency: number; // how often this field appears
}

export class FieldDiscoveryService {
  private getConnection() {
    if (!culaDb) {
      console.warn('🟨 External database connection not available. Field discovery will be disabled.');
      return null;
    }
    return culaDb;
  }

  private buildFileTypeFilter(fileNameColumn: string, fileTypes?: string[]): string {
    if (!fileTypes || fileTypes.length === 0) return '';
    
    const conditions = fileTypes.map(ext => {
      if (ext === 'unknown') {
        return `(${fileNameColumn} NOT LIKE '%.pdf' AND ${fileNameColumn} NOT LIKE '%.png' AND ${fileNameColumn} NOT LIKE '%.jpg' AND ${fileNameColumn} NOT LIKE '%.jpeg' AND ${fileNameColumn} NOT LIKE '%.gif' AND ${fileNameColumn} NOT LIKE '%.tiff' AND ${fileNameColumn} NOT LIKE '%.bmp' AND ${fileNameColumn} NOT LIKE '%.webp')`;
      }
      return `${fileNameColumn} LIKE '%.${ext}'`;
    }).join(' OR ');
    
    return ` AND (${conditions})`;
  }

  async discoverAvailableFields(options?: {
    siteId?: string;
    eventType?: 'step_execution' | 'delivery' | 'emission_log';
    configIds?: string[];
    stepExecutionTypes?: string[];
    deliveryConfigIds?: string[];
    fileTypes?: string[];
  }): Promise<{ metadata: FieldOption[]; inputs: FieldOption[]; }> {
    const connection = this.getConnection();
    if (!connection) return { metadata: [], inputs: [] };

    let query = '';
    const { eventType, siteId, stepExecutionTypes, deliveryConfigIds, fileTypes } = options || {};

    if (!eventType || !siteId) {
        return { metadata: [], inputs: [] };
    }

    switch (eventType) {
        case 'step_execution': {
            let stepTypeFilter = '';
            if (stepExecutionTypes?.length) {
                const stepTypeList = stepExecutionTypes.map(type => `'${type}'`).join(', ');
                stepTypeFilter = ` AND se_filter.type IN (${stepTypeList})`;
            }

            const documentFilter = `
              AND EXISTS (
                SELECT 1 FROM file_references fr
                JOIN proof_sources ps ON ps.file_reference_id = fr.id
                JOIN step_executions se_filter ON ps.step_execution_id = se_filter.id
                WHERE se_filter.id = se.id
                AND se_filter.site_id = '${siteId}'
                ${stepTypeFilter}
                ${this.buildFileTypeFilter('fr.file_name', fileTypes)}
              )
            `;

            query = `
              WITH field_analysis AS (
                -- Step Execution Metadata
                SELECT
                  'created' as field_name,
                  'Step execution creation timestamp' as description,
                  'date' as field_type,
                  'metadata' as category,
                  COUNT(*) as frequency
                FROM step_executions se
                WHERE se.created IS NOT NULL
                ${documentFilter}
                
                UNION ALL
                
                SELECT
                  'type' as field_name,
                  'Step execution type' as description,
                  'text' as field_type,
                  'metadata' as category,
                  COUNT(*) as frequency
                FROM step_executions se
                WHERE se.type IS NOT NULL
                ${documentFilter}
                
                UNION ALL
                
                -- Step Execution Input Fields
                SELECT
                  COALESCE(
                    TRIM(BOTH '"' FROM (config_inputs_unnested.input_element->>'label')),
                    dp.name,
                    'input_' || dp.id
                  ) as field_name,
                  'Input field' as description,
                  CASE   
                    WHEN (config_inputs_unnested.input_element->>'type')::int = 0 THEN 'text'
                    WHEN (config_inputs_unnested.input_element->>'type')::int = 1 THEN 'text' 
                    WHEN (config_inputs_unnested.input_element->>'type')::int = 2 THEN 'number'
                    WHEN (config_inputs_unnested.input_element->>'type')::int = 3 THEN 'date'
                    ELSE 'text'
                  END as field_type,
                  'input' as category,
                  COUNT(*) as frequency
                FROM step_execution_data_points sedp
                JOIN data_points dp ON dp.id = sedp.data_point_id
                JOIN data_point_configs dpc ON dpc.id = dp.data_point_config_id
                JOIN step_executions se ON se.id = sedp.step_execution_id
                LEFT JOIN LATERAL (
                  SELECT input_element
                  FROM jsonb_array_elements(dpc.inputs) AS input_element
                ) AS config_inputs_unnested ON TRUE
                WHERE jsonb_array_length(dp.inputs) > 0
                ${documentFilter}
                GROUP BY field_name, field_type
                
                UNION ALL
                
                -- Step Execution Producer Container Input Fields
                SELECT
                  COALESCE(
                    TRIM(BOTH '"' FROM (config_inputs_unnested.input_element->>'label')),
                    dp.name,
                    'input_' || dp.id
                  ) as field_name,
                  'Producer container input field' as description,
                  CASE   
                    WHEN (config_inputs_unnested.input_element->>'type')::int = 0 THEN 'text'
                    WHEN (config_inputs_unnested.input_element->>'type')::int = 1 THEN 'text' 
                    WHEN (config_inputs_unnested.input_element->>'type')::int = 2 THEN 'number'
                    WHEN (config_inputs_unnested.input_element->>'type')::int = 3 THEN 'date'
                    ELSE 'text'
                  END as field_type,
                  'input' as category,
                  COUNT(*) as frequency
                FROM material_containers mc
                JOIN step_executions se ON se.id = mc.producer_step_exe_id
                JOIN material_container_data_points mcdp ON mcdp.material_container_id = mc.id
                JOIN data_points dp ON dp.id = mcdp.data_point_id
                JOIN data_point_configs dpc ON dpc.id = dp.data_point_config_id
                LEFT JOIN LATERAL (
                  SELECT input_element
                  FROM jsonb_array_elements(dpc.inputs) AS input_element
                ) AS config_inputs_unnested ON TRUE
                WHERE jsonb_array_length(dpc.inputs) > 0
                ${documentFilter}
                GROUP BY field_name, field_type
                
                UNION ALL
                
                -- Step Execution Consumer Container Input Fields
                SELECT
                  COALESCE(
                    TRIM(BOTH '"' FROM (config_inputs_unnested.input_element->>'label')),
                    dp.name,
                    'input_' || dp.id
                  ) as field_name,
                  'Consumer container input field' as description,
                  CASE   
                    WHEN (config_inputs_unnested.input_element->>'type')::int = 0 THEN 'text'
                    WHEN (config_inputs_unnested.input_element->>'type')::int = 1 THEN 'text' 
                    WHEN (config_inputs_unnested.input_element->>'type')::int = 2 THEN 'number'
                    WHEN (config_inputs_unnested.input_element->>'type')::int = 3 THEN 'date'
                    ELSE 'text'
                  END as field_type,
                  'input' as category,
                  COUNT(*) as frequency
                FROM material_containers mc
                JOIN step_executions se ON se.id = mc.consumer_step_exe_id
                JOIN material_container_data_points mcdp ON mcdp.material_container_id = mc.id
                JOIN data_points dp ON dp.id = mcdp.data_point_id
                JOIN data_point_configs dpc ON dpc.id = dp.data_point_config_id
                LEFT JOIN LATERAL (
                  SELECT input_element
                  FROM jsonb_array_elements(dpc.inputs) AS input_element
                ) AS config_inputs_unnested ON TRUE
                WHERE jsonb_array_length(dpc.inputs) > 0
                ${documentFilter}
                GROUP BY field_name, field_type
              )
              SELECT
                field_name as name,
                description,
                field_type as "fieldType",
                category,
                frequency
              FROM field_analysis
              WHERE frequency >= 1
              ORDER BY category, frequency DESC, name;
            `;
            break;
        }
        case 'delivery': {
            let deliveryConfigFilter = '';
            if (deliveryConfigIds && deliveryConfigIds.length > 0) {
                const configIdList = deliveryConfigIds.map(id => `'${id}'`).join(', ');
                deliveryConfigFilter = ` AND d_filter.delivery_config_id IN (${configIdList})`;
            }

            const documentFilter = `
              AND EXISTS (
                SELECT 1 FROM file_references fr
                JOIN proof_sources ps ON ps.file_reference_id = fr.id
                JOIN deliveries d_filter ON ps.delivery_id = d_filter.id
                WHERE d_filter.id = d.id
                AND (d_filter.consumer_site_id = '${siteId}' OR d_filter.issuing_site_id = '${siteId}')
                ${deliveryConfigFilter}
                ${this.buildFileTypeFilter('fr.file_name', fileTypes)}
              )
            `;

            query = `
              WITH field_analysis AS (
                -- Delivery Metadata
                SELECT
                  'announce_timestamp' as field_name,
                  'Delivery announcement timestamp' as description,
                  'date' as field_type,
                  'metadata' as category,
                  COUNT(*) as frequency
                FROM deliveries d
                WHERE d.announce_timestamp IS NOT NULL
                ${documentFilter}
                
                UNION ALL
                
                -- Delivery Input Fields
                SELECT
                  COALESCE(
                    TRIM(BOTH '"' FROM (config_inputs_unnested.input_element->>'label')),
                    dp.name,
                    'input_' || dp.id
                  ) as field_name,
                  'Delivery input field' as description,
                  CASE   
                    WHEN (config_inputs_unnested.input_element->>'type')::int = 0 THEN 'text'
                    WHEN (config_inputs_unnested.input_element->>'type')::int = 1 THEN 'text' 
                    WHEN (config_inputs_unnested.input_element->>'type')::int = 2 THEN 'number'
                    WHEN (config_inputs_unnested.input_element->>'type')::int = 3 THEN 'date'
                    ELSE 'text'
                  END as field_type,
                  'input' as category,
                  COUNT(*) as frequency
                FROM delivery_data_points ddp
                JOIN data_points dp ON dp.id = ddp.data_point_id
                JOIN data_point_configs dpc ON dpc.id = dp.data_point_config_id
                JOIN deliveries d ON d.id = ddp.delivery_id
                LEFT JOIN LATERAL (
                  SELECT input_element
                  FROM jsonb_array_elements(dpc.inputs) AS input_element
                ) AS config_inputs_unnested ON TRUE
                WHERE jsonb_array_length(dp.inputs) > 0
                ${documentFilter}
                GROUP BY field_name, field_type
                
                UNION ALL
                
                -- Delivery Material Container Input Fields
                SELECT
                  COALESCE(
                    TRIM(BOTH '"' FROM (config_inputs_unnested.input_element->>'label')),
                    dp.name,
                    'input_' || dp.id
                  ) as field_name,
                  'Input field' as description,
                  CASE   
                    WHEN (config_inputs_unnested.input_element->>'type')::int = 0 THEN 'text'
                    WHEN (config_inputs_unnested.input_element->>'type')::int = 1 THEN 'text' 
                    WHEN (config_inputs_unnested.input_element->>'type')::int = 2 THEN 'number'
                    WHEN (config_inputs_unnested.input_element->>'type')::int = 3 THEN 'date'
                    ELSE 'text'
                  END as field_type,
                  'input' as category,
                  COUNT(*) as frequency
                FROM delivery_payload_containers dpc
                JOIN deliveries d ON d.id = dpc.delivery_id
                JOIN material_container_data_points mcdp ON mcdp.material_container_id = dpc.container_id
                JOIN data_points dp ON dp.id = mcdp.data_point_id
                JOIN data_point_configs dpconfig ON dpconfig.id = dp.data_point_config_id
                LEFT JOIN LATERAL (
                  SELECT input_element
                  FROM jsonb_array_elements(dpconfig.inputs) AS input_element
                ) AS config_inputs_unnested ON TRUE
                WHERE jsonb_array_length(dpconfig.inputs) > 0
                ${documentFilter}
                GROUP BY field_name, field_type
              )
              SELECT
                field_name as name,
                description,
                field_type as "fieldType",
                category,
                frequency
              FROM field_analysis
              WHERE frequency >= 1
              ORDER BY category, frequency DESC, name;
            `;
            break;
        }
        case 'emission_log': {
            const documentFilter = `
              AND EXISTS (
                SELECT 1 FROM file_references fr
                JOIN proof_sources ps ON ps.file_reference_id = fr.id
                JOIN emissions_logs el_filter ON ps.emissions_log_id = el_filter.id
                WHERE el_filter.id = el.id
                AND el_filter.site_id = '${siteId}'
                ${this.buildFileTypeFilter('fr.file_name', fileTypes)}
              )
            `;

            query = `
              WITH field_analysis AS (
                -- Emission Log Metadata
                SELECT
                  'created' as field_name,
                  'Emission log creation timestamp' as description,
                  'date' as field_type,
                  'metadata' as category,
                  COUNT(*) as frequency
                FROM emissions_logs el
                WHERE el.created IS NOT NULL
                ${documentFilter}
                
                UNION ALL
                
                -- Emission Log Input Fields
                SELECT
                  COALESCE(
                    TRIM(BOTH '"' FROM (config_inputs_unnested.input_element->>'label')),
                    dpc.name,
                    'input_' || dpc.id
                  ) as field_name,
                  'Input field' as description,
                  CASE
                    WHEN (config_inputs_unnested.input_element->>'type')::int = 0 THEN 'text'
                    WHEN (config_inputs_unnested.input_element->>'type')::int = 1 THEN 'text'
                    WHEN (config_inputs_unnested.input_element->>'type')::int = 2 THEN 'number'
                    WHEN (config_inputs_unnested.input_element->>'type')::int = 3 THEN 'date'
                    ELSE 'text'
                  END as field_type,
                  'input' as category,
                  COUNT(*) as frequency
                FROM emissions_log_configs elc
                JOIN emissions_log_config_data_point_configs elcdpc ON elcdpc.emissions_log_config_id = elc.id
                JOIN data_point_configs dpc ON dpc.id = elcdpc.data_point_config_id
                LEFT JOIN LATERAL (
                  SELECT input_element
                  FROM jsonb_array_elements(dpc.inputs) AS input_element
                ) AS config_inputs_unnested ON TRUE
                WHERE jsonb_array_length(dpc.inputs) > 0
                AND elc.site_id = '${siteId}'
                ${fileTypes?.length ? `AND EXISTS (
                  SELECT 1 FROM emissions_logs el_file_filter
                  JOIN proof_sources ps ON ps.emissions_log_id = el_file_filter.id
                  JOIN file_references fr ON fr.id = ps.file_reference_id
                  WHERE el_file_filter.config_id = elc.id
                  ${this.buildFileTypeFilter('fr.file_name', fileTypes)}
                )` : ''}
                GROUP BY field_name, field_type
                
                UNION ALL
                
                -- Emission Log Calculation Node Manual Input Fields
                SELECT
                  ct.name as field_name,
                  'Input field' as description,
                  'number' as field_type,
                  'input' as category,
                  COUNT(*) as frequency
                FROM (
                  WITH RECURSIVE calculation_tree AS (
                    -- Start with root calculation configs from emissions_log_configs
                    SELECT 
                      elc.id as emission_log_config_id,
                      elc.site_id,
                      encf.id as node_config_id,
                      encf.name,
                      encf.type,
                      encf.source_config,
                      0 as depth
                    FROM emissions_log_configs elc
                    JOIN emission_calculation_node_configs encf ON encf.id = elc.calculation_config_id
                    WHERE elc.site_id = '${siteId}'
                    
                    UNION ALL
                    
                    -- Recursively find child nodes
                    SELECT 
                      ct.emission_log_config_id,
                      ct.site_id,
                      child.id as node_config_id,
                      child.name,
                      child.type,
                      child.source_config,
                      ct.depth + 1
                    FROM calculation_tree ct
                    JOIN emission_calculation_config_node_configs junction ON junction.emission_calculation_config_id = ct.node_config_id
                    JOIN emission_calculation_node_configs child ON child.id = junction.node_config_id
                    WHERE ct.depth < 10 -- Prevent infinite loops
                  )
                  SELECT 
                    name,
                    type,
                    source_config
                  FROM calculation_tree
                  WHERE type = 'VARIABLE' 
                  AND source_config->>'valueSourceType' = 'manual'
                ) ct
                WHERE 1=1 ${fileTypes?.length ? `
                  AND EXISTS (
                    SELECT 1 FROM emissions_logs el
                    JOIN proof_sources ps ON ps.emissions_log_id = el.id
                    JOIN file_references fr ON fr.id = ps.file_reference_id
                    WHERE el.config_id IN (
                      SELECT elc_config.id FROM emissions_log_configs elc_config
                      WHERE elc_config.site_id = '${siteId}'
                    )
                    ${this.buildFileTypeFilter('fr.file_name', fileTypes)}
                  )` : ''}
                GROUP BY ct.name
              )
              SELECT
                field_name as name,
                description,
                field_type as "fieldType",
                category,
                frequency
              FROM field_analysis
              WHERE frequency >= 1
              ORDER BY category, frequency DESC, name;
            `;
            break;
        }
        default:
            return { metadata: [], inputs: [] };
    }

    if (!query) {
        return { metadata: [], inputs: [] };
    }

    const result = await connection.execute(sql.raw(query));
    const fields = result as FieldOption[];
    
    // Group fields by category
    const grouped = {
      metadata: fields.filter(f => f.category === 'metadata'),
      inputs: fields.filter(f => f.category === 'input')
    };
    
    return grouped;
  }
}

export const fieldDiscoveryService = new FieldDiscoveryService();

import Redis from 'ioredis';

// Redis connection configuration
const redisConfig = {
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379'),
  password: process.env.REDIS_PASSWORD,
  db: parseInt(process.env.REDIS_DB || '0'),
  maxRetriesPerRequest: null, // Required for BullMQ blocking operations
  retryDelayOnFailover: 100,
  lazyConnect: true,
};

// Create Redis connection for BullMQ
export const redis = new Redis(redisConfig);

// Create a separate Redis connection for subscriptions
export const redisSubscriber = new Redis(redisConfig);

// Connection event handlers
redis.on('connect', () => {
  console.log('🔗 Redis connected');
});

redis.on('ready', () => {
  console.log('✅ Redis ready for BullMQ');
});

redis.on('error', (err) => {
  console.error('❌ Redis connection error:', err);
});

redis.on('close', () => {
  console.log('🔌 Redis connection closed');
});

// Health check function
export const checkRedisHealth = async (): Promise<boolean> => {
  try {
    const result = await redis.ping();
    return result === 'PONG';
  } catch (error) {
    console.error('Redis health check failed:', error);
    return false;
  }
};

// Graceful shutdown
export const closeRedis = async (): Promise<void> => {
  try {
    await redis.disconnect();
    console.log('✅ Redis connection closed gracefully');
  } catch (error) {
    console.error('❌ Error closing Redis connection:', error);
  }
}; 
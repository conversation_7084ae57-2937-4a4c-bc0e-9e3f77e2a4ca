import { db, culaDb, datasets, documents, extractionConfigs, extractions, groundTruths } from '../db/index.js';
import { eq, and } from 'drizzle-orm';
import { sql } from 'drizzle-orm';
import type { Dataset, Document, ExtractionConfig, Extraction, GroundTruth, UserAnnotation } from '../types/resource.js';
import { externalGroundTruthService } from './externalGroundTruth.js';
import { webSocketService } from './websocket.js';
import { randomUUID } from 'crypto';

export class ResourcePool {
  private static instance: ResourcePool;
  private cache: Map<string, any> = new Map();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  static getInstance(): ResourcePool {
    if (!ResourcePool.instance) {
      ResourcePool.instance = new ResourcePool();
    }
    return ResourcePool.instance;
  }

  private isExpired(entry: { timestamp: number }): boolean {
    return Date.now() - entry.timestamp > this.CACHE_TTL;
  }

  private setCache(key: string, data: any): void {
    this.cache.set(key, { data, timestamp: Date.now() });
  }

  private getCache(key: string): any {
    const entry = this.cache.get(key);
    if (!entry || this.isExpired(entry)) {
      this.cache.delete(key);
      return null;
    }
    return entry.data;
  }

  // Bootstrap - load all resources at once
  async bootstrap(): Promise<{
    datasets: any[];
    documents: any[];
    extractionConfigs: any[];
    extractions: any[];
    groundTruths: any[];
  }> {
    console.log('🚀 ResourcePool: Starting bootstrap...');
    
    try {
      // Load basic resources first
      const [
        allDatasets,
        allExtractionConfigs,
        allExtractions,
        allGroundTruths
      ] = await Promise.all([
        db.select().from(datasets),
        db.select().from(extractionConfigs),
        db.select().from(extractions),
        db.select().from(groundTruths)
      ]);

      // Load documents from external database via dataset queries
      const allDocuments: Document[] = [];
      for (const dataset of allDatasets) {
        if (dataset.query) {
          const datasetDocuments = await this.getDocumentsByDataset(dataset.id);
          allDocuments.push(...datasetDocuments);
        }
      }
    
      // Cache everything
      this.setCache('datasets:all', allDatasets);
      this.setCache('documents:all', allDocuments);
      this.setCache('extractionConfigs:all', allExtractionConfigs);
      this.setCache('extractions:all', allExtractions);
      this.setCache('groundTruths:all', allGroundTruths);

      // Cache individual items
      allDatasets.forEach(item => this.setCache(`datasets:${item.id}`, item));
      allDocuments.forEach(item => this.setCache(`documents:${item.id}`, item));
      allExtractionConfigs.forEach(item => this.setCache(`extractionConfigs:${item.id}`, item));
      allExtractions.forEach(item => this.setCache(`extractions:${item.id}`, item));
      allGroundTruths.forEach(item => this.setCache(`groundTruths:${item.id}`, item));

      console.log(`✅ ResourcePool: Bootstrap complete! Loaded ${allDatasets.length} datasets, ${allDocuments.length} documents, ${allExtractionConfigs.length} configs, ${allExtractions.length} extractions, ${allGroundTruths.length} ground truths`);

      return {
        datasets: allDatasets as any[],
        documents: allDocuments as any[],
        extractionConfigs: allExtractionConfigs as any[],
        extractions: allExtractions as any[],
        groundTruths: allGroundTruths as any[]
      };
    } catch (error) {
      console.error('❌ ResourcePool: Bootstrap failed:', error);
      throw error;
    }
  }

  // Get all resources of a type
  async getDatasets(): Promise<Dataset[]> {
    const cached = this.getCache('datasets:all');
    if (cached) return cached;

    const results = await db.select().from(datasets);
    this.setCache('datasets:all', results);
    return results as Dataset[];
  }

  async getDocuments(): Promise<Document[]> {
    const cached = this.getCache('documents:all');
    if (cached) return cached;

    const results = await db.select().from(documents);
    this.setCache('documents:all', results);
    return results as Document[];
  }

  async getDocumentsByDataset(datasetId: string): Promise<Document[]> {
    const cacheKey = `documents:dataset:${datasetId}`;
    const cached = this.getCache(cacheKey);
    if (cached) return cached;

    // First try to get documents from local database
    const localDocuments = await db.select().from(documents).where(eq(documents.datasetId, datasetId));
    if (localDocuments.length > 0) {
      console.log(`✅ Found ${localDocuments.length} documents in local database for dataset ${datasetId}`);
      this.setCache(cacheKey, localDocuments);
      return localDocuments as Document[];
    }

    // If no local documents, sync from external database
    console.log(`📥 No local documents found for dataset ${datasetId}, syncing from external database...`);
    return await this.syncDocumentsForDataset(datasetId);
  }

  async syncDocumentsForDataset(datasetId: string): Promise<Document[]> {
    // Get the dataset to access its query
    const dataset = await this.getDataset(datasetId);
    if (!dataset || !dataset.query) {
      console.warn(`⚠️ No dataset found or no query defined for dataset ${datasetId}`);
      return [];
    }

    try {
      // Execute the dataset query against the external database
      console.log(`🔍 Executing external query for dataset ${datasetId}:`, dataset.query);
      const externalResults = await culaDb.execute(sql.raw(dataset.query));
      
      // Log the first result to inspect the columns
      if (externalResults.length > 0) {
        console.log('🔍 First row from external query:', externalResults[0]);
      }

      // Transform external query results into Document format
      const externalDocuments = externalResults.map((row: any, index: number) => ({
        id: row.id || randomUUID(),
        name: row.file_name || row.name || `Document ${index + 1}`,
        path: row.cloud_storage_id || row.path || '',
        type: this.inferDocumentType(row.file_name || ''),
        size: row.size || 0,
        datasetId: datasetId,
        isApproved: false,
        canApprove: false,
        gcsPath: row.cloud_storage_id,
        siteName: row.name,
        fileName: row.file_name,
        fileSize: row.size || 0,
        objectType: row.object_type,
        emissionsLogId: row.emissions_log_id,
        runningDeliveryId: row.running_delivery_id,
        stepExecutionId: row.step_execution_id,
        previewUrl: row.preview_url,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }));

      console.log('Mapped external documents:', externalDocuments);

      // Persist documents to local database
      const syncedDocuments = await this.persistDocumentsToLocal(externalDocuments, datasetId);

      console.log(`✅ Synced ${syncedDocuments.length} documents to local database for dataset ${datasetId}`);
      
      // Note: Ground truth population is now handled during dataset refresh operations

      // Cache the synced documents
      const cacheKey = `documents:dataset:${datasetId}`;
      this.setCache(cacheKey, syncedDocuments);
      this.setCache('documents:all', null); // Invalidate all documents cache

      return syncedDocuments;
    } catch (error) {
      console.error(`❌ Error syncing documents for dataset ${datasetId}:`, error);
      return [];
    }
  }

  // Inference helper for document types
  private inferDocumentType(fileName: string): string {
    const ext = fileName.toLowerCase().split('.').pop();
    switch (ext) {
      case 'pdf': return 'pdf';
      case 'jpg': case 'jpeg': case 'png': case 'gif': return 'image';
      case 'doc': case 'docx': return 'document';
      case 'txt': return 'text';
      default: return 'unknown';
    }
  }

  // Persist external documents to local database
  private async persistDocumentsToLocal(externalDocuments: any[], datasetId: string): Promise<Document[]> {
    const syncedDocuments: Document[] = [];

    try {
      // Delete existing documents for this dataset to ensure clean sync
      await db.delete(documents).where(eq(documents.datasetId, datasetId));
      console.log(`🗑️ Cleared existing documents for dataset ${datasetId}`);

      // Insert new documents in batches
      const batchSize = 100;
      for (let i = 0; i < externalDocuments.length; i += batchSize) {
        const batch = externalDocuments.slice(i, i + batchSize);
        
        // Prepare documents for insertion (remove non-database fields)
        const documentsToInsert = batch.map(doc => ({
          id: doc.id,
          name: doc.name,
          path: doc.path,
          type: doc.type,
          size: doc.size,
          datasetId: doc.datasetId,
          gcsPath: doc.gcsPath,
          fileName: doc.fileName,
          fileSize: doc.fileSize,
          objectType: doc.objectType,
          siteName: doc.siteName,
          emissionsLogId: doc.emissionsLogId,
          runningDeliveryId: doc.runningDeliveryId,
          stepExecutionId: doc.stepExecutionId,
          previewUrl: doc.previewUrl,
          isApproved: doc.isApproved || false,
          canApprove: doc.canApprove || false,
          createdAt: new Date(),
          updatedAt: new Date(),
        }));

        const insertedBatch = await db.insert(documents).values(documentsToInsert).returning();
        syncedDocuments.push(...insertedBatch as Document[]);
        
        console.log(`📥 Synced batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(externalDocuments.length / batchSize)} (${insertedBatch.length} documents)`);
      }

      // Update dataset document count
      await this.updateDataset(datasetId, { 
        documentCount: syncedDocuments.length,
        status: 'ready' as any 
      });

      console.log(`✅ Successfully synced ${syncedDocuments.length} documents to local database for dataset ${datasetId}`);
      
      return syncedDocuments;
    } catch (error) {
      console.error(`❌ Error persisting documents to local database:`, error);
      throw error;
    }
  }

  // NEW: Trigger async extraction (calls backend endpoint) - IDEMPOTENT
  async triggerExtractionAsync(documentId: string, extractionConfigId: string, extractorModel: string): Promise<any> {
    try {
      // Get extraction config to find provider for this model
      const config = await this.getExtractionConfig(extractionConfigId);
      if (!config?.data?.extractors) {
        throw new Error('Extraction config not found or has no extractors');
      }

      console.log(`🔍 Looking for extractor model: "${extractorModel}"`);
      console.log(`🔍 Available extractors in config:`, config.data.extractors.map(ext => `"${ext.model}"`));
      
      // First try exact match, then try cleaning config models
      let extractor = config.data.extractors.find(ext => ext.model === extractorModel);
      if (!extractor) {
        throw new Error(`Extractor model '${extractorModel}' not found in config`);
      }
      
      console.log(`✅ Found extractor:`, extractor);

      // Get document info for additional context
      const document = await this.getDocument(documentId);
      const documentName = document?.name || 'Unknown document';
      const documentPath = document?.path || document?.gcsPath;

      // Queue extraction job with BullMQ (idempotent)
      const { extractionQueueService } = await import('./extractionQueue.js');
      
      const result = await extractionQueueService.queueExtraction(
        documentId,
        extractionConfigId,
        extractorModel,
        extractor?.provider,
        {
          documentName,
          documentPath,
          datasetId: document?.datasetId,
        }
      );

      console.log('✅ Extraction job queued via BullMQ:', result);
      
      // Create appropriate message based on action taken
      let message = '';
      switch (result.action) {
        case 'already_running':
          message = `Extraction already running for ${extractorModel}`;
          break;
        case 'requeued':
          message = `Extraction requeued for ${extractorModel} (cancelled previous job)`;
          break;
        case 'queued':
        default:
          message = `Extraction queued for ${extractorModel}`;
      }
      
      return {
        success: true,
        jobId: result.jobId,
        status: result.status,
        action: result.action,
        message,
        documentId,
        extractorModel: extractorModel,
        provider: extractor.provider
      };

    } catch (error) {
      console.error('❌ Failed to trigger async extraction:', error);
      throw error;
    }
  }

  // NEW: Trigger batch async extractions
  async triggerBatchExtractionAsync(documentIds: string[], extractionConfigId: string, extractorModels: string[]): Promise<any> {
    try {
      // Get extraction config to validate extractors
      const config = await this.getExtractionConfig(extractionConfigId);
      if (!config?.data?.extractors) {
        throw new Error('Extraction config not found or has no extractors');
      }

      // Validate that all requested models exist in the config
      const availableModels = config.data.extractors.map(ext => ext.model);
      const invalidModels = extractorModels.filter(model => !availableModels.includes(model));
      
      if (invalidModels.length > 0) {
        throw new Error(`Invalid extractor models: ${invalidModels.join(', ')}`);
      }

      // Queue batch extraction job with BullMQ  
      const { extractionQueueService } = await import('./extractionQueue.js');
      
      const result = await extractionQueueService.queueBatchExtraction(
        documentIds,
        extractionConfigId,
        extractorModels,
        false // includeAlreadyExtracted = false
      );

      console.log('✅ Batch extraction job queued via BullMQ:', result);
      
      return {
        success: true,
        jobId: result.jobId,
        status: result.status,
        totalJobs: result.totalJobs,
        message: `Batch extraction queued for ${documentIds.length} documents and ${extractorModels.length} extractors`,
        documentIds,
        extractorModels,
        extractionConfigId
      };

    } catch (error) {
      console.error('❌ Failed to trigger batch async extraction:', error);
      throw error;
    }
  }

  // NEW: Apply data patches from WebSocket events
  applyPatch(patchType: string, data: any): void {
    console.log(`📨 ResourcePool: Applying patch ${patchType}:`, data);

    switch (patchType) {
      case 'extraction_completed':
        this.handleExtractionCompleted(data);
        break;
      case 'extraction_created':
        this.handleExtractionCreated(data);
        break;
      case 'dataset_updated':
        this.handleDatasetUpdated(data);
        break;
      case 'document_updated':
        this.handleDocumentUpdated(data);
        break;
      case 'ground_truth_updated':
        this.handleGroundTruthUpdated(data);
        break;
      default:
        console.warn(`⚠️ Unknown patch type: ${patchType}`);
    }
  }

  private handleExtractionCompleted(data: any): void {
    if (data.extraction) {
      // Add or update extraction in state
      const extraction = data.extraction;
      const existingIndex = this.getCache('extractions:all')?.findIndex((e: any) => e.id === extraction.id);
      
      if (existingIndex >= 0) {
        // Update existing
        const allExtractions = this.getCache('extractions:all') || [];
        allExtractions[existingIndex] = extraction;
        this.setCache('extractions:all', allExtractions);
      } else {
        // Add new
        const allExtractions = this.getCache('extractions:all') || [];
        allExtractions.push(extraction);
        this.setCache('extractions:all', allExtractions);
      }

      // Update individual cache
      this.setCache(`extractions:${extraction.id}`, extraction);
      
      console.log(`✅ Applied extraction completion patch for ${extraction.id}`);
    }
  }

  private handleExtractionCreated(data: any): void {
    if (data.extraction) {
      const extraction = data.extraction;
      const allExtractions = this.getCache('extractions:all') || [];
      allExtractions.push(extraction);
      this.setCache('extractions:all', allExtractions);
      this.setCache(`extractions:${extraction.id}`, extraction);
      
      console.log(`✅ Applied extraction creation patch for ${extraction.id}`);
    }
  }

  private handleDatasetUpdated(data: any): void {
    if (data.dataset) {
      const dataset = data.dataset;
      const allDatasets = this.getCache('datasets:all') || [];
      const index = allDatasets.findIndex((d: any) => d.id === dataset.id);
      
      if (index >= 0) {
        allDatasets[index] = dataset;
        this.setCache('datasets:all', allDatasets);
        this.setCache(`datasets:${dataset.id}`, dataset);
        
        console.log(`✅ Applied dataset update patch for ${dataset.id}`);
      }
    }
  }

  private handleDocumentUpdated(data: any): void {
    if (data.document) {
      const document = data.document;
      const allDocuments = this.getCache('documents:all') || [];
      const index = allDocuments.findIndex((d: any) => d.id === document.id);
      
      if (index >= 0) {
        allDocuments[index] = document;
        this.setCache('documents:all', allDocuments);
        this.setCache(`documents:${document.id}`, document);
        
        console.log(`✅ Applied document update patch for ${document.id}`);
      }
    }
  }

  private handleGroundTruthUpdated(data: any): void {
    if (data.groundTruth) {
      const groundTruth = data.groundTruth;
      const allGroundTruths = this.getCache('groundTruths:all') || [];
      const index = allGroundTruths.findIndex((gt: any) => gt.id === groundTruth.id);
      
      if (index >= 0) {
        allGroundTruths[index] = groundTruth;
      } else {
        allGroundTruths.push(groundTruth);
      }
      
      this.setCache('groundTruths:all', allGroundTruths);
      this.setCache(`groundTruths:${groundTruth.id}`, groundTruth);
      
      console.log(`✅ Applied ground truth update patch for ${groundTruth.id}`);
    }
  }

  async getExtractionConfigs(): Promise<ExtractionConfig[]> {
    const cached = this.getCache('extractionConfigs:all');
    if (cached) return cached;

    const results = await db.select().from(extractionConfigs);
    this.setCache('extractionConfigs:all', results);
    return results as ExtractionConfig[];
  }

  async getExtractions(): Promise<Extraction[]> {
    const cached = this.getCache('extractions:all');
    if (cached) return cached;

    const results = await db.select().from(extractions);
    this.setCache('extractions:all', results);
    return results as Extraction[];
  }

  async getExtractionsByDocument(documentId: string): Promise<Extraction[]> {
    const cacheKey = `extractions:document:${documentId}`;
    const cached = this.getCache(cacheKey);
    if (cached) return cached;

    const results = await db.select().from(extractions).where(eq(extractions.documentId, documentId));
    this.setCache(cacheKey, results);
    return results as Extraction[];
  }

  async getGroundTruths(): Promise<GroundTruth[]> {
    const cached = this.getCache('groundTruths:all');
    if (cached) return cached;

    const results = await db.select().from(groundTruths);
    this.setCache('groundTruths:all', results);
    return results as GroundTruth[];
  }

  // Get individual resources
  async getDataset(id: string): Promise<Dataset | null> {
    const cached = this.getCache(`datasets:${id}`);
    if (cached) return cached;

    const results = await db.select().from(datasets).where(eq(datasets.id, id));
    const result = results[0] || null;
    if (result) this.setCache(`datasets:${id}`, result);
    return result as Dataset | null;
  }

  async getDocument(id: string): Promise<Document | null> {
    const cached = this.getCache(`documents:${id}`);
    if (cached) return cached;

    const results = await db.select().from(documents).where(eq(documents.id, id));
    const result = results[0] || null;
    if (result) this.setCache(`documents:${id}`, result);
    return result as Document | null;
  }

  async getExtractionConfig(id: string): Promise<ExtractionConfig | null> {
    const cached = this.getCache(`extractionConfigs:${id}`);
    if (cached) return cached;

    const results = await db.select().from(extractionConfigs).where(eq(extractionConfigs.id, id));
    const result = results[0] || null;
    if (result) this.setCache(`extractionConfigs:${id}`, result);
    return result as ExtractionConfig | null;
  }

  async getExtraction(id: string): Promise<Extraction | null> {
    const cached = this.getCache(`extractions:${id}`);
    if (cached) return cached;

    const results = await db.select().from(extractions).where(eq(extractions.id, id));
    const result = results[0] || null;
    if (result) this.setCache(`extractions:${id}`, result);
    return result as Extraction | null;
  }

  async getGroundTruth(id: string): Promise<GroundTruth | null> {
    const cached = this.getCache(`groundTruths:${id}`);
    if (cached) return cached;

    const results = await db.select().from(groundTruths).where(eq(groundTruths.id, id));
    const result = results[0] || null;
    if (result) this.setCache(`groundTruths:${id}`, result);
    return result as GroundTruth | null;
  }

  async getDatasetById(id: string): Promise<Dataset | null> {
    return this.getDataset(id);
  }

  async getAllDocuments(): Promise<Document[]> {
    return this.getDocuments();
  }

  // Create operations (invalidate relevant caches)
  async createDataset(data: Partial<Omit<Dataset, 'id' | 'createdAt' | 'updatedAt'>> & { selectedFields?: any[], selectedExtractors?: any[] }): Promise<Dataset> {
    console.log('🔍 CreateDataset - Input data:', JSON.stringify({
      selectedFields: data.selectedFields,
      selectedExtractors: data.selectedExtractors,
      hasSelectedFields: !!(data.selectedFields && data.selectedFields.length > 0),
      hasSelectedExtractors: !!(data.selectedExtractors && data.selectedExtractors.length > 0)
    }, null, 2));

    // First create the dataset without an extraction config
    const [result] = await db.insert(datasets).values({
      name: data.name,
      description: data.description,
      query: data.query,
      extractionConfigId: null, // Will be updated after creating the extraction config
      version: data.version || 1,
      createdAt: new Date(),
      updatedAt: new Date(),
    }).returning();

    // Now create the extraction config with the dataset ID
    let extractionConfig;
    if (data.selectedFields && data.selectedFields.length > 0) {
      extractionConfig = await this.createExtractionConfig({
        datasetId: result.id,
        data: {
          fields: data.selectedFields.map(field => ({
            name: field.name,
            description: field.description,
            fieldType: field.fieldType
          })),
          extractors: data.selectedExtractors && data.selectedExtractors.length > 0 
            ? data.selectedExtractors 
            : [{
                provider: 'gemini',
                model: 'gemini-2.5-flash',
                prompt: this.generateSmartPrompt(data.selectedFields)
              }]
        },
        version: 1
      });
    } else {
      // Create a default extraction config if none is provided
      extractionConfig = await this.createExtractionConfig({
        datasetId: result.id,
        data: {
          fields: [
            { name: 'document_type', description: 'Type of the document', fieldType: 'text' },
            { name: 'total_amount', description: 'Total amount due', fieldType: 'currency' },
          ],
          extractors: [
            {
              provider: 'gemini',
              model: 'gemini-2.5-flash',
              prompt: 'Extract the document type and total amount from the document.',
            },
          ],
        },
        version: 1,
      });
    }

    // Update the dataset with the extraction config ID
    const [updatedResult] = await db.update(datasets)
      .set({ 
        extractionConfigId: extractionConfig.id,
        updatedAt: new Date()
      })
      .where(eq(datasets.id, result.id))
      .returning();

    // Invalidate caches
    this.cache.delete('datasets:all');
    this.setCache(`datasets:${updatedResult.id}`, updatedResult);

    // Automatically populate ground truth from external database if extraction config is set
    if (updatedResult.extractionConfigId) {
      console.log(`🔄 Auto-populating ground truth for new dataset ${updatedResult.id}...`);
      this.populateGroundTruthForDataset(updatedResult.id).catch(error => {
        console.error(`❌ Failed to auto-populate ground truth for dataset ${updatedResult.id}:`, error);
      });
    }

    // Broadcast dataset creation via WebSocket
    webSocketService.broadcast({
      type: 'dataset_created',
      dataset: updatedResult
    });

    return updatedResult as Dataset;
  }

  private generateSmartPrompt(fields: any[]): string {
    const fieldDescriptions = fields.map(f =>
      `- ${f.name} (${f.fieldType}): ${f.description}`
    ).join('\n');

    return `Extract the following information from the document:
  ${fieldDescriptions}

  Focus on accuracy and return "null" if a field cannot be determined from the document.`;
  }

  async createDocument(data: Omit<Document, 'id' | 'createdAt' | 'updatedAt'>): Promise<Document> {
    const [result] = await db.insert(documents).values({
      ...data,
      createdAt: new Date(),
      updatedAt: new Date(),
    }).returning();

    // Invalidate caches
    this.cache.delete('documents:all');
    if (data.datasetId) {
      this.cache.delete(`documents:dataset:${data.datasetId}`);
    }
    this.setCache(`documents:${result.id}`, result);

    // Populate ground truth for new document if it belongs to a dataset with extraction config
    if (result.datasetId) {
      const dataset = await this.getDatasetById(result.datasetId);
      if (dataset?.extractionConfigId) {
        console.log(`🔄 Auto-populating ground truth for new document ${result.id}...`);
        this.populateGroundTruthForDocument(result.id, dataset.extractionConfigId).catch(error => {
          console.error(`❌ Failed to auto-populate ground truth for document ${result.id}:`, error);
        });
      }
    }

    return result as Document;
  }

  async createExtractionConfig(data: Omit<ExtractionConfig, 'id' | 'createdAt' | 'updatedAt'>): Promise<ExtractionConfig> {
    console.log('🔍 CreateExtractionConfig - Raw data type:', typeof data.data);
    console.log('🔍 CreateExtractionConfig - Raw data:', JSON.stringify(data.data, null, 2));
    
    // Ensure data field is properly handled as JSONB object
    const configData = {
      ...data,
      data: typeof data.data === 'string' ? JSON.parse(data.data) : data.data,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    
    console.log('🔍 CreateExtractionConfig - Final data type:', typeof configData.data);
    console.log('🔍 CreateExtractionConfig - Final data:', JSON.stringify(configData.data, null, 2));
    
    const [result] = await db.insert(extractionConfigs).values(configData).returning();

    // Verify the data was stored correctly by querying it back
    const [verifyResult] = await db.select().from(extractionConfigs).where(eq(extractionConfigs.id, result.id));
    console.log('🔍 CreateExtractionConfig - Stored result type:', typeof verifyResult.data);
    console.log('🔍 CreateExtractionConfig - Stored result:', JSON.stringify(verifyResult.data, null, 2));

    // Invalidate caches
    this.cache.delete('extractionConfigs:all');
    this.setCache(`extractionConfigs:${result.id}`, result);

    return result as ExtractionConfig;
  }

  async createExtraction(data: Omit<Extraction, 'id' | 'createdAt' | 'updatedAt'>): Promise<Extraction> {
    const results = await db.insert(extractions).values({
      ...data,
      createdAt: new Date(),
      updatedAt: new Date(),
    }).returning();

    if (!results || results.length === 0) {
      throw new Error(`Failed to create extraction for document: ${data.documentId}`);
    }

    const result = results[0];

    // Invalidate caches
    this.cache.delete('extractions:all');
    this.cache.delete(`extractions:document:${data.documentId}`);
    this.setCache(`extractions:${result.id}`, result);

    // Emit WebSocket event for extraction creation
    webSocketService.broadcast({
      type: 'extraction_created',
      extraction: result,
      timestamp: new Date().toISOString()
    }, 'extractions');
    
    // Also broadcast to document-specific channel if documentId exists
    if (result.documentId) {
      webSocketService.broadcast({
        type: 'extraction_created',
        extraction: result,
        timestamp: new Date().toISOString()
      }, `document:${result.documentId}`);
    }

    return result as Extraction;
  }

  async createGroundTruth(data: Omit<GroundTruth, 'id' | 'createdAt' | 'updatedAt'>): Promise<GroundTruth> {
    const [result] = await db.insert(groundTruths).values({
      ...data,
      createdAt: new Date(),
      updatedAt: new Date(),
    }).returning();

    // Invalidate caches
    this.cache.delete('groundTruths:all');
    this.setCache(`groundTruths:${result.id}`, result);

    return result as GroundTruth;
  }

  async upsertGroundTruth(documentId: string, fieldName: string, userAnnotation: any, isNotExtractable?: boolean): Promise<GroundTruth> {
    
    // Check if ground truth already exists for this document and field
    const existingResults = await db.select().from(groundTruths)
      .where(and(
        eq(groundTruths.documentId, documentId),
        eq(groundTruths.fieldName, fieldName)
      ));
    
    let result: GroundTruth;
    
    if (existingResults.length > 0) {
      // Update existing ground truth
      const [updated] = await db.update(groundTruths)
        .set({
          userAnnotation: isNotExtractable ? null : userAnnotation,
          markedNotExtractable: Boolean(isNotExtractable),
          updatedAt: new Date(),
        })
        .where(eq(groundTruths.id, existingResults[0].id))
        .returning();
      result = updated as GroundTruth;
    } else {
      // Create new ground truth - we need to get the extractionConfigId from the document
      const document = await this.getDocument(documentId);
      if (!document) {
        throw new Error(`Document not found: ${documentId}`);
      }
      
      const dataset = await this.getDataset(document.datasetId);
      if (!dataset || !dataset.extractionConfigId) {
        throw new Error(`Dataset or extraction config not found for document: ${documentId}`);
      }
      
      const [created] = await db.insert(groundTruths).values({
        documentId,
        extractionConfigId: dataset.extractionConfigId,
        fieldName,
        extractedValue: null,
        userAnnotation: isNotExtractable ? null : userAnnotation,
        markedNotExtractable: Boolean(isNotExtractable),
        fieldType: 'text',
        createdAt: new Date(),
        updatedAt: new Date(),
      }).returning();
      result = created as GroundTruth;
    }
    
    // Invalidate caches
    this.cache.delete('groundTruths:all');
    this.setCache(`groundTruths:${result.id}`, result);
    
    // Emit WebSocket event for ground truth update
    webSocketService.broadcast({
      type: 'ground_truth_updated',
      groundTruth: result,
      timestamp: new Date().toISOString()
    }, 'ground_truths');
    
    // Also broadcast to document-specific channel
    webSocketService.broadcast({
      type: 'ground_truth_updated', 
      groundTruth: result,
      timestamp: new Date().toISOString()
    }, `document:${documentId}`);
    
    return result;
  }

  async removeGroundTruth(documentId: string, fieldName: string): Promise<boolean> {
    // Find existing ground truth
    const existingResults = await db.select().from(groundTruths)
      .where(and(
        eq(groundTruths.documentId, documentId),
        eq(groundTruths.fieldName, fieldName)
      ));
    
    if (existingResults.length === 0) {
      return false; // Nothing to remove
    }
    
    const existing = existingResults[0];
    
    // Always keep the record and just remove the user annotation and flag
    // This preserves the extractedValue (even if null) so "DB Value: null" shows
    const [updated] = await db.update(groundTruths)
      .set({
        userAnnotation: null,
        markedNotExtractable: false,
        updatedAt: new Date(),
      })
      .where(eq(groundTruths.id, existing.id))
      .returning();
    
    // Invalidate caches
    this.cache.delete('groundTruths:all');
    this.setCache(`groundTruths:${updated.id}`, updated);
    
    // Emit WebSocket event for ground truth update (not deletion)
    webSocketService.broadcast({
      type: 'ground_truth_updated',
      groundTruth: updated,
      timestamp: new Date().toISOString()
    }, 'ground_truths');
    
    webSocketService.broadcast({
      type: 'ground_truth_updated',
      groundTruth: updated,
      timestamp: new Date().toISOString()
    }, `document:${documentId}`);
    
    return true;
  }

  // Update operations
  async updateDataset(id: string, data: Partial<Omit<Dataset, 'id' | 'createdAt' | 'updatedAt'>>): Promise<Dataset> {
    const [result] = await db.update(datasets)
      .set({ ...data, updatedAt: new Date() })
      .where(eq(datasets.id, id))
      .returning();

    // Invalidate caches
    this.cache.delete('datasets:all');
    this.cache.delete(`datasets:${id}`);
    this.setCache(`datasets:${id}`, result);

    // If the query was updated, re-sync documents
    if (data.query) {
      console.log(`🔄 Dataset ${id} query updated. Re-syncing documents...`);
      this.syncDocumentsForDataset(id).catch(error => {
        console.error(`❌ Failed to re-sync documents for dataset ${id}:`, error);
      });
    }

    // If extraction config was updated, repopulate ground truth
    if (data.extractionConfigId && result.extractionConfigId) {
      console.log(`🔄 Dataset ${id} extraction config updated. Refreshing ground truth...`);
      this.populateGroundTruthForDataset(id).catch(error => {
        console.error(`❌ Failed to refresh ground truth for dataset ${id}:`, error);
      });
    }

    return result as Dataset;
  }

  async refreshDataset(id: string, data: Partial<Omit<Dataset, 'id' | 'createdAt' | 'updatedAt'>>): Promise<{ dataset: Dataset, documents: Document[] }> {
    // First, update the dataset
    const updatedDataset = await this.updateDataset(id, data);

    // Then, re-sync the documents for the dataset
    const syncedDocuments = await this.syncDocumentsForDataset(id);

    // After syncing documents, populate ground truth for the dataset if it has an extraction config
    if (updatedDataset.extractionConfigId) {
      console.log(`🔄 Populating ground truth for dataset ${id} during refresh...`);
      try {
        await this.populateGroundTruthForDataset(id);
        console.log(`✅ Ground truth populated for dataset ${id} during refresh`);
      } catch (error) {
        console.error(`❌ Failed to populate ground truth for dataset ${id} during refresh:`, error);
      }
    }

    // Return both the updated dataset and the newly synced documents
    return {
      dataset: updatedDataset,
      documents: syncedDocuments
    };
  }

  async updateExtraction(id: string, data: Partial<Omit<Extraction, 'id' | 'createdAt' | 'updatedAt'>>): Promise<Extraction> {
    const results = await db.update(extractions)
      .set({ ...data, updatedAt: new Date() })
      .where(eq(extractions.id, id))
      .returning();

    if (!results || results.length === 0) {
      throw new Error(`Failed to update extraction with id: ${id}. No rows affected.`);
    }

    const result = results[0];

    // Invalidate caches
    this.cache.delete('extractions:all');
    this.cache.delete(`extractions:${id}`);
    if (result.documentId) {
      this.cache.delete(`extractions:document:${result.documentId}`);
    }
    this.setCache(`extractions:${id}`, result);

    // Emit WebSocket event for extraction update
    webSocketService.broadcast({
      type: 'extraction_updated',
      extraction: result,
      timestamp: new Date().toISOString()
    }, 'extractions');
    
    // Also broadcast to document-specific channel if documentId exists
    if (result.documentId) {
      webSocketService.broadcast({
        type: 'extraction_updated',
        extraction: result,
        timestamp: new Date().toISOString()
      }, `document:${result.documentId}`);
    }

    return result as Extraction;
  }

  async upsertExtraction(data: Omit<Extraction, 'id' | 'createdAt' | 'updatedAt'>): Promise<Extraction> {
    const { documentId, extractionConfigId, provider, model } = data;

    // Try to find an existing extraction
    const existing = await db.select().from(extractions).where(
      and(
        eq(extractions.documentId, documentId),
        eq(extractions.extractionConfigId, extractionConfigId),
        eq(extractions.provider, provider),
        eq(extractions.model, model)
      )
    ).limit(1);

    if (existing.length > 0) {
      // Update existing extraction
      // Explicitly clear error field if status is not 'failed'
      const updateData = { 
        ...data, 
        updatedAt: new Date(),
        // Clear error if extraction is not failed
        ...(data.status !== 'failed' && { error: null })
      };
      
      const updateResults = await db.update(extractions)
        .set(updateData)
        .where(eq(extractions.id, existing[0].id))
        .returning();
      
      if (!updateResults || updateResults.length === 0) {
        throw new Error(`Failed to update existing extraction with id: ${existing[0].id}. No rows affected.`);
      }

      const updated = updateResults[0];
      
      this.cache.delete('extractions:all');
      this.cache.delete(`extractions:${updated.id}`);
      if (updated.documentId) {
        this.cache.delete(`extractions:document:${updated.documentId}`);
      }
      this.setCache(`extractions:${updated.id}`, updated);
      
      // Emit WebSocket event for extraction update
      webSocketService.broadcast({
        type: 'extraction_updated',
        extraction: updated,
        timestamp: new Date().toISOString()
      }, 'extractions');
      
      // Also broadcast to document-specific channel if documentId exists
      if (updated.documentId) {
        webSocketService.broadcast({
          type: 'extraction_updated',
          extraction: updated,
          timestamp: new Date().toISOString()
        }, `document:${updated.documentId}`);
      }
      
      return updated as Extraction;
    } else {
      // Create new extraction
      return this.createExtraction(data);
    }
  }

  // Delete operations
  async deleteDataset(id: string): Promise<void> {
    await db.delete(datasets).where(eq(datasets.id, id));
    
    // Invalidate caches
    this.cache.delete('datasets:all');
    this.cache.delete(`datasets:${id}`);
  }

  async deleteDocument(id: string): Promise<void> {
    // Get document to know which dataset cache to invalidate
    const doc = await this.getDocument(id);
    
    await db.delete(documents).where(eq(documents.id, id));
    
    // Invalidate caches
    this.cache.delete('documents:all');
    this.cache.delete(`documents:${id}`);
    if (doc?.datasetId) {
      this.cache.delete(`documents:dataset:${doc.datasetId}`);
    }
  }

  async approveDocument(id: string, approvedBy?: string): Promise<Document> {
    const [result] = await db.update(documents)
      .set({ 
        isApproved: true,
        approvedAt: new Date(),
        approvedBy: approvedBy || 'system',
        updatedAt: new Date()
      })
      .where(eq(documents.id, id))
      .returning();

    if (!result) {
      throw new Error(`Document with id ${id} not found`);
    }

    // Invalidate caches
    this.cache.delete('documents:all');
    this.cache.delete(`documents:${id}`);
    if (result.datasetId) {
      this.cache.delete(`documents:dataset:${result.datasetId}`);
    }
    this.setCache(`documents:${id}`, result);

    // Broadcast the update via WebSocket (if available)
    // webSocketService.broadcastToAll('document:updated', { documentId: id, isApproved: true });

    return result as Document;
  }

  async unapproveDocument(id: string): Promise<Document> {
    const [result] = await db.update(documents)
      .set({ 
        isApproved: false,
        approvedAt: null,
        approvedBy: null,
        updatedAt: new Date()
      })
      .where(eq(documents.id, id))
      .returning();

    if (!result) {
      throw new Error(`Document with id ${id} not found`);
    }

    // Invalidate caches
    this.cache.delete('documents:all');
    this.cache.delete(`documents:${id}`);
    if (result.datasetId) {
      this.cache.delete(`documents:dataset:${result.datasetId}`);
    }
    this.setCache(`documents:${id}`, result);

    // Broadcast the update via WebSocket (if available)
    // webSocketService.broadcastToAll('document:updated', { documentId: id, isApproved: false });

    return result as Document;
  }

  async getNextUnapprovedDocument(datasetId: string, currentDocumentId?: string): Promise<Document | null> {
    const cacheKey = `documents:dataset:${datasetId}:unapproved:${currentDocumentId || 'first'}`;
    const cached = this.getCache(cacheKey);
    if (cached) {
      return cached;
    }

    // Get all documents in the dataset ordered by creation date
    const allDocs = await db.select()
      .from(documents)
      .where(and(
        eq(documents.datasetId, datasetId),
        eq(documents.isApproved, false)
      ))
      .orderBy(documents.createdAt);

    if (allDocs.length === 0) {
      this.setCache(cacheKey, null);
      return null;
    }

    let nextDoc: any = null;

    if (currentDocumentId) {
      // Find the current document index and get the next unapproved one
      const currentIndex = allDocs.findIndex(doc => doc.id === currentDocumentId);
      if (currentIndex >= 0 && currentIndex < allDocs.length - 1) {
        nextDoc = allDocs[currentIndex + 1];
      } else {
        // If current document is the last or not found, get the first unapproved document
        nextDoc = allDocs[0];
      }
    } else {
      // No current document specified, return the first unapproved document
      nextDoc = allDocs[0];
    }

    this.setCache(cacheKey, nextDoc);
    return nextDoc as Document;
  }

  async deleteExtractionConfig(id: string): Promise<void> {
    await db.delete(extractionConfigs).where(eq(extractionConfigs.id, id));
    
    // Invalidate caches
    this.cache.delete('extractionConfigs:all');
    this.cache.delete(`extractionConfigs:${id}`);
  }

  async updateExtractionConfig(id: string, data: any): Promise<ExtractionConfig> {
    const [result] = await db.update(extractionConfigs)
      .set({ data, updatedAt: new Date() })
      .where(eq(extractionConfigs.id, id))
      .returning();

    // Invalidate caches
    this.cache.delete('extractionConfigs:all');
    this.cache.delete(`extractionConfigs:${id}`);
    this.setCache(`extractionConfigs:${id}`, result);

    // Emit WebSocket event for extraction config update
    webSocketService.broadcast({
      type: 'extraction_config_updated',
      extractionConfig: result,
      timestamp: new Date().toISOString()
    }, 'extraction_configs');

    return result as ExtractionConfig;
  }

  // Clear all caches
  clearCache(): void {
    this.cache.clear();
    console.log('🗑️ ResourcePool: Cache cleared');
  }

  // Get cache stats for debugging
  getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    };
  }

  // Ground Truth Population from External Database
  
  /**
   * Populate ground truth data from external database for a specific document
   */
  async populateGroundTruthForDocument(documentId: string, extractionConfigId: string): Promise<GroundTruth[]> {
    console.log(`🔄 Populating ground truth for document ${documentId} from external database...`);
    
    try {
      const externalData = await externalGroundTruthService.getDetailedGroundTruthForDocument(documentId);
      const populatedRecords: GroundTruth[] = [];

      for (const [fieldName, data] of Object.entries(externalData)) {
        try {
          // Check if ground truth already exists in our database
          const existingRecord = await db.select()
            .from(groundTruths)
            .where(and(
              eq(groundTruths.documentId, documentId),
              eq(groundTruths.fieldName, fieldName)
            ))
            .limit(1);

          if (existingRecord.length > 0) {
            // Update existing record with external data (only if no user annotation exists)
            const existing = existingRecord[0];
            if (existing.userAnnotation === null && data.extractedValue !== null) {
              const [updated] = await db.update(groundTruths)
                .set({
                  extractedValue: data.extractedValue,
                  fieldType: data.fieldType,
                  updatedAt: new Date()
                })
                .where(eq(groundTruths.id, existing.id))
                .returning();

              populatedRecords.push(updated);
              console.log(`🔄 Updated existing ground truth for field ${fieldName}`);
            } else {
              populatedRecords.push(existing);
              console.log(`⏭️  Skipped field ${fieldName} - user annotation exists or no external data`);
            }
          } else {
            // Create new ground truth record
            const [newRecord] = await db.insert(groundTruths)
              .values({
                documentId,
                extractionConfigId,
                fieldName,
                extractedValue: data.extractedValue,
                userAnnotation: data.userAnnotation,
                fieldType: data.fieldType,
              })
              .returning();

            populatedRecords.push(newRecord);
            console.log(`✅ Created new ground truth for field ${fieldName}`);
          }
        } catch (error) {
          console.error(`❌ Failed to process ground truth for field ${fieldName}:`, error);
        }
      }

      // Invalidate cache
      this.cache.delete('groundTruths:all');
      
      console.log(`✅ Successfully populated ${populatedRecords.length} ground truth records for document ${documentId}`);
      return populatedRecords;
    } catch (error) {
      console.error(`❌ Failed to populate ground truth for document ${documentId}:`, error);
      return [];
    }
  }

  /**
   * Populate ground truth data for all documents in a dataset
   */
  async populateGroundTruthForDataset(datasetId: string): Promise<{ populated: number; skipped: number; errors: number }> {
    console.log(`🔄 Populating ground truth for dataset ${datasetId} from external database...`);
    
    try {
      // Get dataset and its extraction config
      const dataset = await this.getDatasetById(datasetId);
      if (!dataset || !dataset.extractionConfigId) {
        throw new Error(`Dataset ${datasetId} not found or has no extraction config`);
      }

      // Get all documents in the dataset
      const docs = await this.getAllDocuments();
      const datasetDocs = docs.filter(doc => doc.datasetId === datasetId);
      
      if (datasetDocs.length === 0) {
        console.log(`ℹ️  No documents found in dataset ${datasetId}`);
        return { populated: 0, skipped: 0, errors: 0 };
      }

      console.log(`📄 Found ${datasetDocs.length} documents in dataset ${datasetId}`);

      // Get external ground truth data for all documents at once
      const documentIds = datasetDocs.map(doc => doc.id);
      const externalData = await externalGroundTruthService.getGroundTruthForDocuments(documentIds);

      let populated = 0;
      let skipped = 0;
      let errors = 0;

      // Process each document
      for (const doc of datasetDocs) {
        try {
          const docExternalData = externalData[doc.id];
          if (!docExternalData || Object.keys(docExternalData).length === 0) {
            console.log(`⏭️  No external ground truth found for document ${doc.id}`);
            skipped++;
            continue;
          }

          const populatedRecords = await this.populateGroundTruthForDocument(doc.id, dataset.extractionConfigId);
          populated += populatedRecords.length;
        } catch (error) {
          console.error(`❌ Failed to populate ground truth for document ${doc.id}:`, error);
          errors++;
        }
      }

      console.log(`✅ Ground truth population complete for dataset ${datasetId}: ${populated} populated, ${skipped} skipped, ${errors} errors`);
      return { populated, skipped, errors };
    } catch (error) {
      console.error(`❌ Failed to populate ground truth for dataset ${datasetId}:`, error);
      return { populated: 0, skipped: 0, errors: 1 };
    }
  }

  /**
   * Check if external ground truth database is available
   */
  async isExternalGroundTruthAvailable(): Promise<boolean> {
    return await externalGroundTruthService.isAvailable();
  }

  /**
   * Force refresh ground truth data from external database (ignores existing data)
   */
  async refreshGroundTruthFromExternal(documentId: string, extractionConfigId: string): Promise<GroundTruth[]> {
    console.log(`🔄 Force refreshing ground truth for document ${documentId} from external database...`);
    
    try {
      const externalData = await externalGroundTruthService.getDetailedGroundTruthForDocument(documentId);
      const refreshedRecords: GroundTruth[] = [];

      for (const [fieldName, data] of Object.entries(externalData)) {
        try {
          // Delete existing record
          await db.delete(groundTruths)
            .where(and(
              eq(groundTruths.documentId, documentId),
              eq(groundTruths.fieldName, fieldName)
            ));

          // Create new record with external data
          const [newRecord] = await db.insert(groundTruths)
            .values({
              documentId,
              extractionConfigId,
              fieldName,
              extractedValue: data.extractedValue,
              userAnnotation: data.userAnnotation,
              fieldType: data.fieldType,
            })
            .returning();

          refreshedRecords.push(newRecord);
          console.log(`🔄 Refreshed ground truth for field ${fieldName}`);
        } catch (error) {
          console.error(`❌ Failed to refresh ground truth for field ${fieldName}:`, error);
        }
      }

      // Invalidate cache
      this.cache.delete('groundTruths:all');
      
      console.log(`✅ Successfully refreshed ${refreshedRecords.length} ground truth records for document ${documentId}`);
      return refreshedRecords;
    } catch (error) {
      console.error(`❌ Failed to refresh ground truth for document ${documentId}:`, error);
      return [];
    }
  }
}

export const resourcePool = ResourcePool.getInstance(); 
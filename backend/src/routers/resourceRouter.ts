import { z } from 'zod';
import { procedure, router } from '../trpc.js';
import { resourcePool } from '../services/resourcePool.js';
import { fieldDiscoveryService } from '../services/fieldDiscoveryService.js';
import { queryConfigService } from '../services/queryConfigService.js';
import { externalGroundTruthService } from '../services/externalGroundTruth.js';
import { 
  DatasetSchema, 
  DocumentSchema, 
  ExtractionConfigSchema, 
  ExtractionSchema, 
  GroundTruthSchema,
  ExtractionConfigDataSchema,
} from '../types/resource.js';

export const resourceRouter = router({
  // Bootstrap - load all resources at once
  bootstrap: procedure.query(async () => {
    return await resourcePool.bootstrap();
  }),

  // Datasets
  datasets: router({
    list: procedure.query(async () => {
      return await resourcePool.getDatasets();
    }),
    
    byId: procedure
      .input(z.object({ id: z.string().uuid() }))
      .query(async ({ input }) => {
        return await resourcePool.getDataset(input.id);
      }),
    
    create: procedure
      .input(DatasetSchema.omit({ id: true, createdAt: true, updatedAt: true }).extend({
        selectedFields: z.array(z.any()).optional(),
        selectedExtractors: z.array(z.any()).optional()
      }))
      .mutation(async ({ input }) => {
        return await resourcePool.createDataset(input);
      }),
    
    update: procedure
      .input(z.object({
        id: z.string().uuid(),
        data: DatasetSchema.omit({ id: true, createdAt: true, updatedAt: true }).partial()
      }))
      .mutation(async ({ input }) => {
        return await resourcePool.updateDataset(input.id, input.data);
      }),
    
    delete: procedure
      .input(z.object({ id: z.string().uuid() }))
      .mutation(async ({ input }) => {
        await resourcePool.deleteDataset(input.id);
        return { success: true };
      }),

    // NEW: Sync documents from external database to local database
    syncDocuments: procedure
      .input(z.object({ id: z.string().uuid() }))
      .mutation(async ({ input }) => {
        const documents = await resourcePool.syncDocumentsForDataset(input.id);
        return { 
          success: true, 
          documentsCount: documents.length,
          message: `Synced ${documents.length} documents to local database`
        };
      }),
      
    refresh: procedure
      .input(z.object({
        id: z.string().uuid(),
        data: DatasetSchema.omit({ id: true, createdAt: true, updatedAt: true }).partial()
      }))
      .mutation(async ({ input }) => {
        return await resourcePool.refreshDataset(input.id, input.data);
      }),
  }),

  // Query Configuration
  queryConfig: router({
    getSites: procedure.query(async () => {
      return await queryConfigService.getAvailableSites();
    }),

    getEventTypes: procedure
      .input(z.object({ 
        siteId: z.string().uuid()
      }))
      .query(async ({ input }) => {
        return await queryConfigService.getEventTypeConfigs(input.siteId);
      }),

    getStepExecutionTypes: procedure
      .input(z.object({ siteId: z.string().uuid() }))
      .query(async ({ input }) => {
        return await queryConfigService.getStepExecutionTypes(input.siteId);
      }),

    getDeliveryDirectionCounts: procedure
      .input(z.object({ siteId: z.string().uuid() }))
      .query(async ({ input }) => {
        return await queryConfigService.getDeliveryDirectionCounts(input.siteId);
      }),

    getDeliveryConfigs: procedure
      .input(z.object({ 
        siteId: z.string().uuid()
      }))
      .query(async ({ input }) => {
        return await queryConfigService.getDeliveryConfigs(input.siteId);
      }),

    getDocumentsForEventType: procedure
      .input(z.object({ 
        siteId: z.string().uuid(),
        eventType: z.enum(['step_execution', 'delivery', 'emission_log']),
        fileTypes: z.array(z.string()).optional(),
        limit: z.number().optional(),
        stepExecutionTypes: z.array(z.string()).optional(),
        deliveryConfigIds: z.array(z.string().uuid()).optional(),
        timestamp: z.number().optional() // Cache busting parameter
      }))
      .query(async ({ input }) => {
        return await queryConfigService.getDocumentsForEventType(
          input.siteId, 
          input.eventType, 
          input.fileTypes, 
          input.limit, 
          input.stepExecutionTypes,
          input.deliveryConfigIds
        );
      }),
    getDocumentTypes: procedure
      .input(z.object({
        siteId: z.string().uuid(),
        eventType: z.enum(['step_execution', 'delivery', 'emission_log']),
        stepExecutionTypes: z.array(z.string()).optional(),
        deliveryConfigIds: z.array(z.string().uuid()).optional()
      }))
      .query(async ({ input }) => {
        return await queryConfigService.getDocumentTypesForEventType(
          input.siteId, 
          input.eventType, 
          input.stepExecutionTypes,
          input.deliveryConfigIds
        );
      }),

    generateQuery: procedure
      .input(z.object({
        siteId: z.string().uuid(),
        eventType: z.enum(['step_execution', 'delivery', 'emission_log']),
        configIds: z.array(z.string().uuid()).optional(),
        stepExecutionTypes: z.array(z.string()).optional(),
        fileTypes: z.array(z.string()).optional(),
        deliveryConfigIds: z.array(z.string().uuid()).optional()
      }))
      .mutation(async ({ input }) => {
        const query = queryConfigService.generateQuery({
          siteId: input.siteId,
          eventType: input.eventType,
          configIds: input.configIds,
          stepExecutionTypes: input.stepExecutionTypes,
          fileTypes: input.fileTypes,
          deliveryConfigIds: input.deliveryConfigIds
        });
        return { query };
      }),

    discoverFields: procedure
      .input(z.object({
        siteId: z.string().uuid().optional(),
        eventType: z.enum(['step_execution', 'delivery', 'emission_log']).optional(),
        configIds: z.array(z.string().uuid()).optional(),
        stepExecutionTypes: z.array(z.string()).optional(),
        deliveryConfigIds: z.array(z.string().uuid()).optional(),
        fileTypes: z.array(z.string()).optional()
      }))
      .query(async ({ input }) => {
        return await fieldDiscoveryService.discoverAvailableFields(input);
      }),

    discoverFieldsForDocument: procedure
      .input(z.object({
        documentId: z.string().uuid(),
        eventType: z.enum(['step_execution', 'delivery', 'emission_log']),
      }))
      .query(async ({ input }) => {
        const { documentId, eventType } = input;
        let siteId: string | undefined;
        let stepExecutionTypes: string[] | undefined;
        let deliveryConfigIds: string[] | undefined;

        // This is a simplified lookup. A more robust solution might involve
        // a dedicated service to resolve document context.
        const db = fieldDiscoveryService['getConnection']();
        if (!db) return { metadata: [], inputs: [] };

        switch (eventType) {
          case 'step_execution': {
            const result = await db.select({ siteId: z.string(), type: z.string() }).from('step_executions' as any).where({ id: documentId }).limit(1);
            if (result.length > 0) {
              siteId = result[0].siteId;
              stepExecutionTypes = [result[0].type];
            }
            break;
          }
          case 'delivery': {
            const result = await db.select({ issuingSiteId: z.string(), consumerSiteId: z.string(), deliveryConfigId: z.string() }).from('deliveries' as any).where({ id: documentId }).limit(1);
            if (result.length > 0) {
              // Assuming we check both issuing and consumer for siteId match
              // This might need more specific logic based on product requirements
              siteId = result[0].issuingSiteId || result[0].consumerSiteId;
              deliveryConfigIds = [result[0].deliveryConfigId];
            }
            break;
          }
          case 'emission_log': {
            const result = await db.select({ siteId: z.string() }).from('emissions_logs' as any).where({ id: documentId }).limit(1);
            if (result.length > 0) {
              siteId = result[0].siteId;
            }
            break;
          }
        }

        if (!siteId) {
          // Handle case where document or siteId is not found
          return { metadata: [], inputs: [] };
        }

        return await fieldDiscoveryService.discoverAvailableFields({
          siteId,
          eventType,
          stepExecutionTypes,
          deliveryConfigIds,
          // We are not considering fileTypes for single document preview for now
        });
      }),

    previewQuery: procedure
      .input(z.object({ query: z.string() }))
      .query(async ({ input }) => {
        return await queryConfigService.previewQuery(input.query);
      }),
  }),

  // Field Discovery (Legacy - kept for backward compatibility)
  fieldDiscovery: router({
    availableFields: procedure.query(async () => {
      return await fieldDiscoveryService.discoverAvailableFields();
    }),

    byCategory: procedure
      .input(z.object({ 
        category: z.enum(['metadata', 'input', 'datapoint']) 
      }))
      .query(async ({ input }) => {
        const fields = await fieldDiscoveryService.discoverAvailableFields();
        return fields.metadata.concat(fields.inputs).concat(fields.datapoints)
          .filter(f => f.category === input.category);
      }),

    popular: procedure
      .input(z.object({ 
        limit: z.number().min(1).max(100).default(20) 
      }))
      .query(async ({ input }) => {
        const fields = await fieldDiscoveryService.discoverAvailableFields();
        const allFields = fields.metadata.concat(fields.inputs).concat(fields.datapoints);
        return allFields
          .sort((a, b) => b.frequency - a.frequency)
          .slice(0, input.limit);
      }),
  }),

  // Documents  
  documents: router({
    list: procedure.query(async () => {
      return await resourcePool.getDocuments();
    }),
    
    byId: procedure
      .input(z.object({ id: z.string().uuid() }))
      .query(async ({ input }) => {
        return await resourcePool.getDocument(input.id);
      }),
    
    byDataset: procedure
      .input(z.object({ datasetId: z.string().uuid() }))
      .query(async ({ input }) => {
        return await resourcePool.getDocumentsByDataset(input.datasetId);
      }),
    
    getByDataset: procedure
      .input(z.object({ datasetId: z.string().uuid() }))
      .query(async ({ input }) => {
        return await resourcePool.syncDocumentsForDataset(input.datasetId);
      }),
    
    create: procedure
      .input(DocumentSchema.omit({ id: true, createdAt: true, updatedAt: true }))
      .mutation(async ({ input }) => {
        return await resourcePool.createDocument(input);
      }),
    
    delete: procedure
      .input(z.object({ id: z.string().uuid() }))
      .mutation(async ({ input }) => {
        await resourcePool.deleteDocument(input.id);
        return { success: true };
      }),

    approve: procedure
      .input(z.object({ 
        id: z.string().uuid(),
        approvedBy: z.string().optional()
      }))
      .mutation(async ({ input }) => {
        return await resourcePool.approveDocument(input.id, input.approvedBy);
      }),

    unapprove: procedure
      .input(z.object({ 
        id: z.string().uuid()
      }))
      .mutation(async ({ input }) => {
        return await resourcePool.unapproveDocument(input.id);
      }),

    getNextUnapproved: procedure
      .input(z.object({ 
        datasetId: z.string().uuid(),
        currentDocumentId: z.string().uuid().optional()
      }))
      .query(async ({ input }) => {
        return await resourcePool.getNextUnapprovedDocument(input.datasetId, input.currentDocumentId);
      }),
  }),

  // Extraction Configs
  extractionConfigs: router({
    list: procedure.query(async () => {
      return await resourcePool.getExtractionConfigs();
    }),
    
    byId: procedure
      .input(z.object({ id: z.string().uuid() }))
      .query(async ({ input }) => {
        return await resourcePool.getExtractionConfig(input.id);
      }),
    
    create: procedure
      .input(ExtractionConfigSchema.omit({ id: true, createdAt: true, updatedAt: true }))
      .mutation(async ({ input }) => {
        return await resourcePool.createExtractionConfig(input);
      }),

    update: procedure
      .input(z.object({
        id: z.string().uuid(),
        data: ExtractionConfigDataSchema,
      }))
      .mutation(async ({ input }) => {
        return await resourcePool.updateExtractionConfig(input.id, input.data);
      }),
    
    delete: procedure
      .input(z.object({ id: z.string().uuid() }))
      .mutation(async ({ input }) => {
        await resourcePool.deleteExtractionConfig(input.id);
        return { success: true };
      }),
  }),

  // Extractions
  extractions: router({
    list: procedure.query(async () => {
      return await resourcePool.getExtractions();
    }),
    
    byId: procedure
      .input(z.object({ id: z.string().uuid() }))
      .query(async ({ input }) => {
        return await resourcePool.getExtraction(input.id);
      }),
    
    byDocument: procedure
      .input(z.object({ documentId: z.string().uuid() }))
      .query(async ({ input }) => {
        return await resourcePool.getExtractionsByDocument(input.documentId);
      }),
    
    create: procedure
      .input(ExtractionSchema.omit({ id: true, createdAt: true, updatedAt: true }))
      .mutation(async ({ input }) => {
        return await resourcePool.createExtraction(input);
      }),

    // NEW: Trigger async extraction (queues to Redis)
    runAsync: procedure
      .input(z.object({
        documentId: z.string().uuid(),
        extractionConfigId: z.string().uuid(),
        extractorModel: z.string()
      }))
      .mutation(async ({ input }) => {
        return await resourcePool.triggerExtractionAsync(
          input.documentId,
          input.extractionConfigId,
          input.extractorModel
        );
      }),

    // NEW: Trigger batch async extractions
    runBatchAsync: procedure
      .input(z.object({
        documentIds: z.array(z.string().uuid()),
        extractionConfigId: z.string().uuid(),
        extractorModels: z.array(z.string())
      }))
      .mutation(async ({ input }) => {
        return await resourcePool.triggerBatchExtractionAsync(
          input.documentIds,
          input.extractionConfigId,
          input.extractorModels
        );
      }),

    // NEW: Check if extraction is already queued or running
    checkStatus: procedure
      .input(z.object({
        documentId: z.string().uuid(),
        extractionConfigId: z.string().uuid(),
        extractorModel: z.string()
      }))
      .query(async ({ input }) => {
        // Get extraction config to find provider for this model
        const config = await resourcePool.getExtractionConfig(input.extractionConfigId);
        if (!config?.data?.extractors) {
          throw new Error('Extraction config not found or has no extractors');
        }

        const extractor = config.data.extractors.find(ext => ext.model === input.extractorModel);
        if (!extractor) {
          throw new Error(`Extractor model '${input.extractorModel}' not found in config`);
        }

        // Check queue status
        const { extractionQueueService } = await import('../services/extractionQueue.js');
        return await extractionQueueService.isExtractionQueued(
          input.documentId,
          input.extractionConfigId,
          input.extractorModel,
          extractor.provider
        );
      }),
  }),

  // Ground Truths
  groundTruths: router({
    list: procedure.query(async () => {
      return await resourcePool.getGroundTruths();
    }),
    
    byId: procedure
      .input(z.object({ id: z.string().uuid() }))
      .query(async ({ input }) => {
        return await resourcePool.getGroundTruth(input.id);
      }),
    
    create: procedure
      .input(GroundTruthSchema.omit({ id: true, createdAt: true, updatedAt: true }))
      .mutation(async ({ input }) => {
        return await resourcePool.createGroundTruth(input);
      }),
      
    upsert: procedure
      .input(z.object({
        documentId: z.string().uuid(),
        fieldName: z.string(),
        userAnnotation: z.any().optional(),
        isNotExtractable: z.boolean().optional()
      }))
      .mutation(async ({ input }) => {
        return await resourcePool.upsertGroundTruth(input.documentId, input.fieldName, input.userAnnotation, input.isNotExtractable);
      }),
      
    remove: procedure
      .input(z.object({
        documentId: z.string().uuid(),
        fieldName: z.string()
      }))
      .mutation(async ({ input }) => {
        return await resourcePool.removeGroundTruth(input.documentId, input.fieldName);
      }),
      
    // External database population
    populateForDocument: procedure
      .input(z.object({
        documentId: z.string().uuid(),
        extractionConfigId: z.string().uuid()
      }))
      .mutation(async ({ input }) => {
        return await resourcePool.populateGroundTruthForDocument(input.documentId, input.extractionConfigId);
      }),
      
    populateForDataset: procedure
      .input(z.object({
        datasetId: z.string().uuid()
      }))
      .mutation(async ({ input }) => {
        return await resourcePool.populateGroundTruthForDataset(input.datasetId);
      }),
      
    refreshFromExternal: procedure
      .input(z.object({
        documentId: z.string().uuid(),
        extractionConfigId: z.string().uuid()
      }))
      .mutation(async ({ input }) => {
        return await resourcePool.refreshGroundTruthFromExternal(input.documentId, input.extractionConfigId);
      }),
      
    checkExternalAvailability: procedure
      .query(async () => {
        return await resourcePool.isExternalGroundTruthAvailable();
      }),

    // Field Preview for Dataset Builder
    fieldPreview: procedure
      .input(z.object({
        documentId: z.string().uuid(),
        fieldNames: z.array(z.string())
      }))
      .query(async ({ input }) => {
        return await externalGroundTruthService.getFieldPreview(input.documentId, input.fieldNames);
      }),
  }),

  // Utility
  clearCache: procedure.mutation(async () => {
    resourcePool.clearCache();
    return { success: true, message: 'Cache cleared' };
  }),

  cacheStats: procedure.query(async () => {
    return resourcePool.getCacheStats();
  }),
}); 
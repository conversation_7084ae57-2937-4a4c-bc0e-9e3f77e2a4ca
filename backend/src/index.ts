import { config } from 'dotenv';

// Load environment variables from .env.local for development
config({ path: '.env.local' });
import express from 'express';
import cors from 'cors';
import { createServer } from 'http';
import { createExpressMiddleware } from '@trpc/server/adapters/express';
import { createContext } from './trpc.js';
import { appRouter } from './routers/index.js';
import { webSocketService } from './services/websocket.js';
import { redis, redisSubscriber, checkRedisHealth } from './services/redis.js';
import { Storage } from '@google-cloud/storage';
import { resourcePool } from './services/resourcePool.js';
import { fieldDiscoveryService } from './services/fieldDiscoveryService.js';
import type { Document } from './types/resource.js';
// Import workers to start BullMQ job processing
import './worker.js';

const app = express();
const port = process.env.PORT || 8000;

// Create HTTP server for WebSocket support
const server = createServer(app);

// Middleware
app.use(cors({
  origin: [
    'http://localhost:3000',
    'http://localhost:5173',
    'http://127.0.0.1:3000',
    'http://127.0.0.1:5173',
    'http://localhost:3333',
    'http://127.0.0.1:3333'
  ],
  credentials: true
}));

// tRPC middleware
app.use(
  '/trpc',
  createExpressMiddleware({
    router: appRouter,
    createContext,
  })
);

// Field discovery endpoint
app.get('/api/field-options', async (req, res) => {
  try {
    const fields = await fieldDiscoveryService.discoverAvailableFields();
    
    // Fields are already grouped by the service
    const grouped = fields;
    
    res.json(grouped);
  } catch (error) {
    const message = error instanceof Error ? error.message : 'An unknown error occurred';
    res.status(500).json({ error: message });
  }
});

// Document streaming endpoint
app.get('/api/documents/:id/stream', async (req, res) => {
  try {
    const documentId = req.params.id;
    console.log(`🔍 Looking for document: ${documentId}`);
    let document: Document | Partial<Document> | null = null;
    let documentSource = '';
    
    // Try both databases in parallel for better performance
    const [localDocument, externalDocument] = await Promise.allSettled([
      resourcePool.getDocument(documentId),
      (async () => {
        try {
          const { queryConfigService } = await import('./services/queryConfigService.js');
          return await queryConfigService.getDocumentById(documentId);
        } catch (error) {
          console.warn('Could not query external database for document:', error);
          return null;
        }
      })()
    ]);
    
    // Prioritize external (CULA) database for QueryConfigView compatibility
    if (externalDocument.status === 'fulfilled' && externalDocument.value) {
      document = externalDocument.value;
      documentSource = 'external (CULA) database';
      console.log(`📄 Found document in external database: ${document.name}`);
      console.log(`📄 EXTERNAL DOCUMENT OBJECT:`, JSON.stringify(document, null, 2));
    } else if (localDocument.status === 'fulfilled' && localDocument.value) {
      document = localDocument.value;
      documentSource = 'local database';
      console.log(`📄 Found document in local database: ${document.name}`);
      console.log(`📄 LOCAL DOCUMENT OBJECT:`, JSON.stringify(document, null, 2));
    }
    
    if (!document) {
      console.log(`❌ Document ${documentId} not found in either database`);
      return res.status(404).json({ error: 'Document not found' });
    }

    console.log(`📄 Streaming document: ${document.name} (${document.type}) from ${documentSource}`);

    // Determine content type based on file extension
    const getContentType = (filename: string): string => {
      const ext = filename.toLowerCase().split('.').pop();
      switch (ext) {
        case 'pdf': return 'application/pdf';
        case 'jpg': case 'jpeg': return 'image/jpeg';
        case 'png': return 'image/png';
        case 'gif': return 'image/gif';
        case 'webp': return 'image/webp';
        case 'bmp': return 'image/bmp';
        case 'svg': return 'image/svg+xml';
        default: return 'application/octet-stream';
      }
    };

    const contentType = getContentType(document.name || '');
    
    // For images, try to serve from preview URL first, then GCS
    if (contentType.startsWith('image/')) {
      if (document.previewUrl) {
        // Redirect to preview URL
        console.log(`🖼️ Redirecting to preview URL: ${document.previewUrl}`);
        return res.redirect(document.previewUrl!);
      }
    }

    // For PDFs or images without preview URL, stream from GCS
    if (document.gcsPath || document.id) {
      const bucketName = process.env.GCS_BUCKET_NAME;
      if (!bucketName) {
        return res.status(500).json({ error: 'GCS_BUCKET_NAME not configured' });
      }

      const storage = new Storage();
      const bucket = storage.bucket(bucketName);
      
      // Try multiple possible file paths in order of preference
      const possiblePaths = [
        document.gcsPath,           // Original path from database
        document.path,              // Alternative path field
        document.id                 // Fallback to just document ID (UUID)
      ].filter(Boolean); // Remove null/undefined values

      console.log(`☁️ Trying ${possiblePaths.length} possible GCS paths for document ${document.id}:`, possiblePaths);

      let file;
      let foundPath;
      
      // Try each path until we find one that exists
      for (const path of possiblePaths) {
        const testFile = bucket.file(path);
        try {
          const [exists] = await testFile.exists();
          console.log(`☁️ Checking path '${path}': ${exists}`);
          if (exists) {
            file = testFile;
            foundPath = path;
            break;
          }
        } catch (error) {
          console.log(`☁️ Error checking path '${path}':`, error.message);
          continue;
        }
      }

      if (!file) {
        console.log(`❌ No valid GCS file found for document ${document.id} in any of the tried paths`);
        return res.status(404).json({ error: 'File not found in GCS bucket' });
      }

      console.log(`✅ Found GCS file at path: ${foundPath}`);

      try {

        // Get file metadata
        const [metadata] = await file.getMetadata();
        
        // Set appropriate headers
        res.set({
          'Content-Type': contentType,
          'Content-Length': metadata.size,
          'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
          'Content-Disposition': `inline; filename="${document.name}"`,
        });

        console.log(`☁️ Streaming from GCS: ${bucketName}/${document.gcsPath}`);

        // Stream the file
        const stream = file.createReadStream();
        stream.pipe(res);

        stream.on('error', (error) => {
          console.error(`❌ Error streaming file: ${error instanceof Error ? error.message : 'Unknown error'}`);
          if (!res.headersSent) {
            res.status(500).json({ error: 'Error streaming file' });
          }
        });

      } catch (error) {
        console.error(`❌ GCS error: ${error instanceof Error ? error.message : 'Unknown error'}`);
        return res.status(500).json({ error: 'Error accessing file in GCS' });
      }
    } else {
      console.log(`❌ Document ${documentId} found in ${documentSource} but has no valid GCS path`);
      return res.status(404).json({ error: 'Document file is not available for streaming' });
    }

  } catch (error) {
    console.error(`❌ Document streaming error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Health check with Redis status
app.get('/health', async (req, res) => {
  const redisHealthy = await checkRedisHealth();
  const wsStats = webSocketService.getStats();
  
  res.json({ 
    status: 'ok', 
    service: 'cula-extractor-backend',
    redis: redisHealthy ? 'connected' : 'disconnected',
    websocket: {
      clients: wsStats.totalClients,
      subscriptions: wsStats.subscriptions,
    },
    timestamp: new Date().toISOString(),
  });
});

// Initialize WebSocket server
webSocketService.initialize(server);

// Set up Redis subscriber
redisSubscriber.subscribe('extractions', (err, count) => {
  if (err) {
    console.error('Failed to subscribe to Redis channel:', err);
    return;
  }
  console.log(`📡 Subscribed to ${count} Redis channels.`);
});

redisSubscriber.on('message', (channel, message) => {
  console.log(`Received message from ${channel}:`, message);
  try {
    const parsedMessage = JSON.parse(message);
    webSocketService.broadcast(parsedMessage, channel);
  } catch (error) {
    console.error('Failed to parse message from Redis:', error);
  }
});

// Graceful shutdown
process.on('SIGINT', async () => {
  console.log('🛑 Received SIGINT, shutting down gracefully...');
  
  webSocketService.shutdown();
  await redis.disconnect();
  
  server.close(() => {
    console.log('✅ Server shut down gracefully');
    process.exit(0);
  });
});

process.on('SIGTERM', async () => {
  console.log('🛑 Received SIGTERM, shutting down gracefully...');
  
  webSocketService.shutdown();
  await redis.disconnect();
  
  server.close(() => {
    console.log('✅ Server shut down gracefully');
    process.exit(0);
  });
});

server.listen(port, () => {
  console.log(`🚀 Backend server running on http://localhost:${port}`);
  console.log(`📡 tRPC endpoint: http://localhost:${port}/trpc`);
  console.log(`🔌 WebSocket endpoint: ws://localhost:${port}/ws`);
  console.log(`⚙️  BullMQ workers started and ready to process extraction jobs`);
});

export type AppRouter = typeof appRouter; 
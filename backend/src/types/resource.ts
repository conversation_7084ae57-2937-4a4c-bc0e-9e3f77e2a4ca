import { z } from 'zod';

// Base Resource interface - everything with an id is a Resource
export const ResourceSchema = z.object({
  id: z.string().uuid(),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
});

export type Resource = z.infer<typeof ResourceSchema>;

// Dataset Resource
export const DatasetSchema = ResourceSchema.extend({
  name: z.string(),
  description: z.string().optional().nullable(),
  query: z.string(),
  extractionConfigId: z.string().uuid().optional(),
  version: z.number(),
  documentCount: z.number().default(0),
  folderPath: z.string().optional(),
  status: z.enum(['ready', 'syncing', 'error']).default('ready'),
});

export type Dataset = z.infer<typeof DatasetSchema>;

// Document Resource
export const DocumentSchema = ResourceSchema.extend({
  name: z.string(),
  path: z.string(),
  type: z.string(),
  size: z.number(),
  datasetId: z.string().uuid().optional(),
  gcsPath: z.string().optional(),
  folderPath: z.string().optional(),
  fileName: z.string().optional(),
  fileSize: z.number().optional(),
  objectType: z.string().optional(),
  siteName: z.string().optional(),
  emissionsLogId: z.string().optional(),
  runningDeliveryId: z.string().optional(),
  stepExecutionId: z.string().optional(),
  previewUrl: z.string().optional(),
  isApproved: z.boolean().default(false),
  approvedAt: z.string().datetime().optional(),
  approvedBy: z.string().optional(),
  canApprove: z.boolean().default(false),
  approvalReason: z.string().optional(),
});

export type Document = z.infer<typeof DocumentSchema>;

// ExtractionConfig Resource
export const ExtractionFieldSchema = z.object({
  name: z.string(),
  description: z.string(),
  fieldType: z.enum(['text', 'number', 'date', 'boolean', 'percentage', 'currency', 'email', 'phone', 'address']),
  validationPattern: z.string().optional(),
  examples: z.array(z.string()).optional(),
  unit: z.string().optional(),
});

export const ExtractorSchema = z.object({
  provider: z.string(),
  model: z.string(),
  prompt: z.string(),
});

export const ExtractionConfigDataSchema = z.object({
  fields: z.array(ExtractionFieldSchema),
  extractors: z.array(ExtractorSchema),
});

export const ExtractionConfigSchema = ResourceSchema.extend({
  datasetId: z.string().uuid().optional(),
  data: ExtractionConfigDataSchema,
  version: z.number(),
});

export type ExtractionField = z.infer<typeof ExtractionFieldSchema>;
export type Extractor = z.infer<typeof ExtractorSchema>;
export type ExtractionConfigData = z.infer<typeof ExtractionConfigDataSchema>;
export type ExtractionConfig = z.infer<typeof ExtractionConfigSchema>;


export const ExtractedValueSchema = z.object({
  value: z.any(),
});

export const ExtractionSchema = ResourceSchema.extend({
  documentId: z.string().uuid(),
  extractionConfigId: z.string().uuid(),
  provider: z.string(),
  model: z.string(),
  processingTime: z.number(),
  data: z.record(ExtractedValueSchema),
  status: z.enum(['pending', 'running', 'completed', 'failed']),
  error: z.string().optional(),
  rawResponse: z.record(z.any()).optional(),
});

export type ExtractedValue = z.infer<typeof ExtractedValueSchema>;
export type Extraction = z.infer<typeof ExtractionSchema>;

// Ground Truth Resource
export const GroundTruthSchema = ResourceSchema.extend({
  documentId: z.string().uuid(),
  extractionConfigId: z.string().uuid(),
  fieldName: z.string(),
  extractedValue: z.any().optional().nullable(),
  userAnnotation: z.any().optional().nullable(),
  markedNotExtractable: z.boolean().default(false),
  fieldType: z.string(),
});

export type GroundTruth = z.infer<typeof GroundTruthSchema>;

// Export all schemas for validation
export const ResourceSchemas = {
  Resource: ResourceSchema,
  Dataset: DatasetSchema,
  Document: DocumentSchema,
  ExtractionConfig: ExtractionConfigSchema,
  Extraction: ExtractionSchema,
  GroundTruth: GroundTruthSchema,
} as const; 
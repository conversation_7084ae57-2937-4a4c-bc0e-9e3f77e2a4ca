# Use a Node.js base image
FROM node:20-alpine

# Set working directory
WORKDIR /app

# Install pnpm
RUN npm install -g pnpm

# Copy package.json and pnpm-lock.yaml
COPY backend/package.json ./
COPY pnpm-lock.yaml ./
COPY pnpm-workspace.yaml ./

# Install dependencies
RUN pnpm install --filter backend --prod

# Copy source code (for development, volumes will override this)
COPY backend/src ./src

# Expose the port the backend runs on
EXPOSE 8000

# Command to run the development server
CMD ["pnpm", "run", "dev"]

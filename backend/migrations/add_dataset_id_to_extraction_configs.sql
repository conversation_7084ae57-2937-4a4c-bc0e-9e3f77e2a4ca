-- Add dataset_id column to extraction_configs table
-- This allows extraction configs to be associated with specific datasets

ALTER TABLE extraction_configs 
ADD COLUMN dataset_id UUID REFERENCES datasets(id) ON DELETE CASCADE;

-- Create index for better query performance
CREATE INDEX idx_extraction_configs_dataset_id ON extraction_configs(dataset_id);

-- Update existing extraction_configs to link them to their datasets
-- Find the dataset that references each extraction config and update accordingly
UPDATE extraction_configs 
SET dataset_id = d.id 
FROM datasets d 
WHERE extraction_configs.id = d.extraction_config_id;
services:
  # Backend API Service
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: ${BACKEND_CONTAINER_NAME:-cula-extractor-backend}
    environment:
      # Database connection (external PostgreSQL)
      POSTGRES_HOST: host.docker.internal
      POSTGRES_PORT: 5432
      POSTGRES_DB: ${POSTGRES_DB:-extractor_db}
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-}
      
      # AI API Keys
      GOOGLE_API_KEY: ${GOOGLE_API_KEY:-}
      MISTRAL_API_KEY: ${MISTRAL_API_KEY:-}
      
      # Application config
      LOG_LEVEL: ${LOG_LEVEL:-INFO}
      CORS_ORIGINS: ${CORS_ORIGINS:-http://localhost:3000}
      API_PORT: 8000
      NODE_ENV: ${NODE_ENV:-production}
      
      # File storage
      DOCUMENTS_PATH: /app/data/documents
      EXTRACTIONS_PATH: /app/data/extractions
      
      # GCS config
      GCS_BUCKET_NAME: ${GCS_BUCKET_NAME:-}
      GOOGLE_CLOUD_CREDENTIALS_BASE64: ${GOOGLE_CLOUD_CREDENTIALS_BASE64:-}
      
      # Performance
      MAX_CONCURRENT_DOWNLOADS: ${MAX_CONCURRENT_DOWNLOADS:-3}
      MAX_REQUESTS_PER_MINUTE: ${MAX_REQUESTS_PER_MINUTE:-100}
    ports:
      - "${API_PORT:-8000}:8000"
    volumes:
      - documents_data:/app/data/documents
      - extractions_data:/app/data/extractions
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - extractor-network

  # Frontend Web Application
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      args:
        VITE_API_BASE_URL: http://localhost:${API_PORT:-8000}
    container_name: ${FRONTEND_CONTAINER_NAME:-cula-extractor-frontend}
    ports:
      - "${FRONTEND_PORT:-3000}:3000"
    depends_on:
      backend:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - extractor-network

volumes:
  documents_data:
    name: ${DOCUMENTS_DATA_VOLUME:-cula_extractor_documents_data}
  extractions_data:
    name: cula_extractor_extractions_data

networks:
  extractor-network:
    driver: bridge
    name: cula-extractor-network 
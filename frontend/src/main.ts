import { createApp } from 'vue'
import { createRouter, createWebHistory } from 'vue-router'
import App from './App.vue'
import DocumentDetail from './views/DocumentDetail.vue'
import DatasetList from './views/DatasetList.vue'
import DatasetDetail from './views/DatasetDetail.vue'
import DatasetBuilderView from './views/DatasetBuilderView.vue'
import DebugView from './views/DebugView.vue'
import FieldPreviewDebug from './views/FieldPreviewDebug.vue'
import { resourcePool } from './services/resourcePool'
import './index.css'


const router = createRouter({
  history: createWebHistory(),
  routes: [
  { path: '/', redirect: '/datasets' },
  { path: '/datasets/:datasetId/documents/:documentId', component: DocumentDetail, name: 'DocumentDetail', props: true },
    { path: '/datasets', component: DatasetList, name: 'DatasetList' },
  { path: '/datasets/:id', component: DatasetDetail, name: 'DatasetDetail', props: true },
  { path: '/datasets/builder', component: DatasetBuilderView, name: 'QueryConfig' },
  { path: '/debug', component: DebugView, name: 'Debug' },
  { path: '/debug/field-preview', component: FieldPreviewDebug, name: 'FieldPreviewDebug' },
],
})

const app = createApp(App)
app.use(router)
app.mount('#app')

// Attach resourcePool to window for debugging
if (typeof window !== 'undefined') {
  (window as any).resourcePool = resourcePool
} 
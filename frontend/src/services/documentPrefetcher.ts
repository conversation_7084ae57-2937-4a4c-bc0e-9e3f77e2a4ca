interface PrefetchedDocument {
  url: string;
  data: Blob | null;
  loading: boolean;
  error: string | null;
}

class DocumentPrefetcher {
  private cache: Map<string, PrefetchedDocument> = new Map();
  private pendingRequests: Set<string> = new Set();
  private maxCacheSize = 20; // Keep max 20 documents in memory
  
  /**
   * Prefetch a document by its identifier
   */
  async prefetch(documentIdentifier: string): Promise<void> {
    const baseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';
    const url = `${baseUrl}/api/documents/${encodeURIComponent(documentIdentifier)}/stream`;
    
    // Skip if already cached or currently loading
    if (this.cache.has(url) || this.pendingRequests.has(url)) {
      return;
    }
    
    // Add to pending requests
    this.pendingRequests.add(url);
    
    // Initialize cache entry
    this.cache.set(url, {
      url,
      data: null,
      loading: true,
      error: null
    });
    
    try {
      console.log(`🔄 Prefetching document: ${documentIdentifier}`);
      
      const response = await fetch(url);
      
      if (!response.ok) {
        if (response.status === 404) {
          // 404 is expected for documents that don't have files in GCS
          console.log(`📄 Document not available for streaming: ${documentIdentifier} (404)`);
          // Mark as successfully "prefetched" but with no data - this prevents retry attempts
          this.cache.set(url, {
            url,
            data: null,
            loading: false,
            error: 'Document file not available'
          });
          return;
        }
        throw new Error(`Failed to fetch document: ${response.status} ${response.statusText}`);
      }
      
      const blob = await response.blob();
      
      // Update cache with successful result
      this.cache.set(url, {
        url,
        data: blob,
        loading: false,
        error: null
      });
      
      console.log(`✅ Successfully prefetched document: ${documentIdentifier} (${blob.size} bytes)`);
      
      // Cleanup old cache entries if we exceed max size
      this.cleanupCache();
      
    } catch (error) {
      console.error(`❌ Failed to prefetch document ${documentIdentifier}:`, error);
      
      // Update cache with error
      this.cache.set(url, {
        url,
        data: null,
        loading: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      this.pendingRequests.delete(url);
    }
  }
  
  /**
   * Prefetch multiple documents in parallel, skipping already cached ones
   */
  async prefetchBatch(documentIdentifiers: string[]): Promise<void> {
    const baseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';
    // Filter out documents that are already cached or being fetched
    const documentsToFetch = documentIdentifiers.filter(id => {
      const url = `${baseUrl}/api/documents/${encodeURIComponent(id)}/stream`;
      return !this.cache.has(url) && !this.pendingRequests.has(url);
    });
    
    if (documentsToFetch.length === 0) {
      console.log('📦 All requested documents are already cached or being fetched');
      return;
    }
    
    console.log(`🚀 Starting batch prefetch of ${documentsToFetch.length} documents (${documentIdentifiers.length - documentsToFetch.length} already cached)`);
    
    const promises = documentsToFetch.map(id => this.prefetch(id));
    await Promise.allSettled(promises);
  }
  
  /**
   * Get cached document data
   */
  getCached(documentIdentifier: string): PrefetchedDocument | undefined {
    const baseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';
    const url = `${baseUrl}/api/documents/${encodeURIComponent(documentIdentifier)}/stream`;
    return this.cache.get(url);
  }
  
  /**
   * Check if a document is cached and ready
   */
  isCached(documentIdentifier: string): boolean {
    const cached = this.getCached(documentIdentifier);
    return cached ? cached.data !== null && !cached.loading : false;
  }
  
  /**
   * Get a blob URL for a cached document
   */
  getBlobUrl(documentIdentifier: string): string | null {
    const cached = this.getCached(documentIdentifier);
    if (cached?.data) {
      return URL.createObjectURL(cached.data);
    }
    return null;
  }
  
  /**
   * Clear all cached documents
   */
  clearCache(): void {
    // Revoke all blob URLs to prevent memory leaks
    for (const entry of this.cache.values()) {
      if (entry.data) {
        // Note: We can't revoke URLs here as they might be in use
        // URLs will be garbage collected when no longer referenced
      }
    }
    
    this.cache.clear();
    console.log('🗑️ Document cache cleared');
  }
  
  /**
   * Remove least recently used items from cache
   */
  private cleanupCache(): void {
    if (this.cache.size <= this.maxCacheSize) {
      return;
    }
    
    // Convert to array and remove oldest entries
    const entries = Array.from(this.cache.entries());
    const toRemove = entries.slice(0, entries.length - this.maxCacheSize);
    
    for (const [url] of toRemove) {
      this.cache.delete(url);
    }
    
    console.log(`🧹 Cleaned up ${toRemove.length} old cache entries`);
  }
  
  /**
   * Get cache statistics
   */
  getStats() {
    const total = this.cache.size;
    const loading = Array.from(this.cache.values()).filter(c => c.loading).length;
    const ready = Array.from(this.cache.values()).filter(c => c.data !== null).length;
    const errors = Array.from(this.cache.values()).filter(c => c.error !== null).length;
    
    return {
      total,
      loading,
      ready,
      errors,
      pending: this.pendingRequests.size
    };
  }
}

// Export singleton instance
export const documentPrefetcher = new DocumentPrefetcher();
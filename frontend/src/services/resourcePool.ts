import { ref, reactive, computed } from 'vue';
import { trpc } from '../lib/trpc.js';
import type { 
  Dataset, 
  Document, 
  ExtractionConfig, 
  Extraction, 
  GroundTruth,
  UserAnnotation
} from '../types/index.js';

// Reactive state for all resources
const state = reactive({
  datasets: [] as Dataset[],
  documents: [] as Document[],
  extractionConfigs: [] as ExtractionConfig[],
  extractions: [] as Extraction[],
  groundTruths: [] as GroundTruth[],
  isBootstrapped: false,
  isLoading: false,
  error: null as string | null,
  // WebSocket connection state
  wsConnected: false,
  wsError: null as string | null,
  // Bootstrap state tracking
  isBootstrapping: false,
});

class ResourcePool {
  private static instance: ResourcePool;
  private ws: WebSocket | null = null;
  private reconnectTimer: number | null = null;
  private reconnectAttempts = 0;
  private readonly maxReconnectAttempts = 5;
  private readonly wsUrl = import.meta.env.VITE_WS_URL || 'ws://localhost:8000/ws';

  static getInstance(): ResourcePool {
    if (!ResourcePool.instance) {
      ResourcePool.instance = new ResourcePool();
    }
    return ResourcePool.instance;
  }

  constructor() {
    // Auto-start WebSocket connection when ResourcePool is created
    this.initializeWebSocket();
  }

  // ============= WEBSOCKET MANAGEMENT =============
  
  private initializeWebSocket(): void {
    this.connectWebSocket();
  }

  private connectWebSocket(): void {
    try {
      console.log('🔌 ResourcePool: Connecting to WebSocket:', this.wsUrl);
      this.ws = new WebSocket(this.wsUrl);

      this.ws.onopen = () => {
        console.log('🔌 ResourcePool: WebSocket connected to', this.wsUrl);
        state.wsConnected = true;
        state.wsError = null;
        this.reconnectAttempts = 0;

        // Subscribe to all relevant channels
        this.subscribeToChannels(['extractions', 'ground_truths', 'datasets', 'documents', 'extraction_configs']);
      };

      this.ws.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          this.handleWebSocketMessage(message);
        } catch (err) {
          console.error('ResourcePool: Failed to parse WebSocket message:', err);
        }
      };

      this.ws.onclose = (event) => {
        console.log('🔌 ResourcePool: WebSocket disconnected:', { code: event.code, reason: event.reason, wasClean: event.wasClean });
        state.wsConnected = false;
        
        // Attempt to reconnect if not a clean close
        if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
          const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000);
          console.log(`🔄 ResourcePool: Reconnecting WebSocket in ${delay}ms (attempt ${this.reconnectAttempts + 1}/${this.maxReconnectAttempts})`);
          
          this.reconnectTimer = window.setTimeout(() => {
            this.reconnectAttempts++;
            this.connectWebSocket();
          }, delay);
        } else if (this.reconnectAttempts >= this.maxReconnectAttempts) {
          console.error('❌ ResourcePool: Max reconnection attempts reached. WebSocket connection failed permanently.');
          state.wsError = 'WebSocket connection failed after multiple attempts';
        }
      };

      this.ws.onerror = (err) => {
        console.error('🔌 ResourcePool: WebSocket error:', err);
        state.wsError = 'WebSocket connection error';
      };

    } catch (err) {
      console.error('🔌 ResourcePool: Failed to create WebSocket:', err);
      state.wsError = 'Failed to create WebSocket connection';
    }
  }

  private subscribeToChannels(channels: string[]): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      const subscribeMessage = {
        type: 'subscribe',
        channels
      };
      console.log('📡 ResourcePool: Sending subscription request:', subscribeMessage);
      this.ws.send(JSON.stringify(subscribeMessage));
      console.log('📡 ResourcePool: Subscription request sent for channels:', channels);
    } else {
      console.warn('⚠️ ResourcePool: Cannot subscribe - WebSocket not connected. State:', this.ws?.readyState);
    }
  }

  private handleWebSocketMessage(message: any): void {
    console.log('📨 ResourcePool: Received WebSocket message:', message.type, message);
    
    switch (message.type) {
      case 'connected':
        console.log('✅ ResourcePool: Connected to WebSocket updates');
        break;

      case 'subscribed':
        console.log('📡 ResourcePool: Subscribed to channels:', message.channels);
        break;

      // ===== EXTRACTION UPDATES =====
      case 'extraction_started':
      case 'extraction_running': 
      case 'extraction_progress':
      case 'extraction_completed':
      case 'extraction_failed':
      case 'extraction_updated':
        this.handleExtractionUpdate(message);
        break;

      // ===== GROUND TRUTH UPDATES =====
      case 'ground_truth_updated':
        this.handleGroundTruthUpdate(message.groundTruth);
        break;

      case 'ground_truth_deleted':
        this.handleGroundTruthDeleted(message.groundTruth);
        break;

      // ===== DATASET UPDATES =====
      case 'dataset_updated':
      case 'dataset_created':
        this.handleDatasetUpdate(message.dataset);
        break;

      case 'dataset_deleted':
        this.handleDatasetDeleted(message.dataset);
        break;

      // ===== DOCUMENT UPDATES =====
      case 'document_updated':
      case 'document_created':
        this.handleDocumentUpdate(message.document);
        break;

      case 'document_deleted':
        this.handleDocumentDeleted(message.document);
        break;

      // ===== USER ANNOTATIONS =====
      case 'user_annotation_created':
        this.handleUserAnnotationCreated(message.annotation);
        break;

      case 'error':
        console.error('❌ ResourcePool: WebSocket error:', message.message);
        state.wsError = message.message;
        break;

      case 'pong':
        console.log('🏓 ResourcePool: Pong received');
        break;

      default:
        console.log('📨 ResourcePool: Unknown WebSocket message type:', message.type);
    }
  }

  // ===== WEBSOCKET EVENT HANDLERS =====
  
  private handleExtractionUpdate(message: any): void {
    const extraction = message.extraction || message.result;
    if (!extraction) {
      console.warn('⚠️ ResourcePool: Extraction update message has no extraction data:', message);
      return;
    }

    console.log(`🔄 ResourcePool: Updating extraction ${extraction.id} (status: ${extraction.status})`);
    console.log('📊 ResourcePool: Extraction data:', extraction);
    
    const existingIndex = state.extractions.findIndex(e => e.id === extraction.id);
    
    if (existingIndex >= 0) {
      // Update existing extraction
      console.log(`🔄 ResourcePool: Found existing extraction at index ${existingIndex}, updating...`);
      state.extractions[existingIndex] = extraction;
    } else {
      // Add new extraction
      console.log(`➕ ResourcePool: Adding new extraction to state...`);
      state.extractions.push(extraction);
    }
    
    console.log(`✅ ResourcePool: Applied extraction update for ${extraction.id}. Total extractions: ${state.extractions.length}`);
  }

  private handleGroundTruthUpdate(groundTruth: any): void {
    if (!groundTruth) return;

    console.log(`🔄 ResourcePool: Updating ground truth ${groundTruth.id}`);
    
    const existingIndex = state.groundTruths.findIndex(gt => gt.id === groundTruth.id);
    
    if (existingIndex >= 0) {
      state.groundTruths[existingIndex] = groundTruth;
    } else {
      state.groundTruths.push(groundTruth);
    }
    
    console.log(`✅ ResourcePool: Applied ground truth update for ${groundTruth.id}`);
  }

  private handleGroundTruthDeleted(groundTruth: any): void {
    if (!groundTruth) return;

    console.log(`🗑️ ResourcePool: Deleting ground truth ${groundTruth.id}`);
    
    const existingIndex = state.groundTruths.findIndex(gt => gt.id === groundTruth.id);
    if (existingIndex >= 0) {
      state.groundTruths.splice(existingIndex, 1);
      console.log(`✅ ResourcePool: Deleted ground truth ${groundTruth.id}`);
    }
  }

  private handleDatasetUpdate(dataset: any): void {
    if (!dataset) return;

    console.log(`🔄 ResourcePool: Updating dataset ${dataset.id}`);
    
    const existingIndex = state.datasets.findIndex(d => d.id === dataset.id);
    
    if (existingIndex >= 0) {
      state.datasets[existingIndex] = dataset;
    } else {
      state.datasets.push(dataset);
    }
    
    console.log(`✅ ResourcePool: Applied dataset update for ${dataset.id}`);
  }

  private handleDatasetDeleted(dataset: any): void {
    if (!dataset) return;

    console.log(`🗑️ ResourcePool: Deleting dataset ${dataset.id}`);
    
    const existingIndex = state.datasets.findIndex(d => d.id === dataset.id);
    if (existingIndex >= 0) {
      state.datasets.splice(existingIndex, 1);
      console.log(`✅ ResourcePool: Deleted dataset ${dataset.id}`);
    }
  }

  private handleDocumentUpdate(document: any): void {
    if (!document) return;

    console.log(`🔄 ResourcePool: Updating document ${document.id}`);
    
    const existingIndex = state.documents.findIndex(d => d.id === document.id);
    
    if (existingIndex >= 0) {
      state.documents[existingIndex] = document;
    } else {
      state.documents.push(document);
    }
    
    console.log(`✅ ResourcePool: Applied document update for ${document.id}`);
  }

  private handleDocumentDeleted(document: any): void {
    if (!document) return;

    console.log(`🗑️ ResourcePool: Deleting document ${document.id}`);
    
    const existingIndex = state.documents.findIndex(d => d.id === document.id);
    if (existingIndex >= 0) {
      state.documents.splice(existingIndex, 1);
      console.log(`✅ ResourcePool: Deleted document ${document.id}`);
    }
  }

  private handleUserAnnotationCreated(annotation: any): void {
    if (!annotation) return;
    console.log(`✅ ResourcePool: User annotation created:`, annotation.id);
    // Handle user annotations if needed
  }

  // ============= BOOTSTRAP =============
  async bootstrap(): Promise<void> {
    if (state.isBootstrapped && !this.isEmpty()) {
      console.log('🎯 ResourcePool: Already bootstrapped, skipping');
      return;
    }

    if (state.isBootstrapping) {
      console.log('🎯 ResourcePool: Bootstrap already in progress, skipping');
      return;
    }

    state.isBootstrapping = true;
    state.isLoading = true;
    state.error = null;

    try {
      console.log('🚀 ResourcePool: Starting bootstrap...');
      
      const data = await trpc.resources.bootstrap.query();
      
      // Update reactive state
      state.datasets = data.datasets;
      state.documents = data.documents;
      state.extractionConfigs = data.extractionConfigs;
      state.extractions = data.extractions;
      state.groundTruths = data.groundTruths;
      state.isBootstrapped = true;

      console.log(`✅ ResourcePool: Bootstrap complete! Loaded ${data.datasets.length} datasets, ${data.documents.length} documents, ${data.extractionConfigs.length} configs, ${data.extractions.length} extractions, ${data.groundTruths.length} ground truths`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Bootstrap failed';
      state.error = errorMessage;
      console.error('❌ ResourcePool: Bootstrap failed:', error);
      throw error;
    } finally {
      state.isLoading = false;
      state.isBootstrapping = false;
    }
  }

  // ============= GETTERS =============
  
  // All datasets
  get datasets(): Dataset[] {
    return state.datasets;
  }

  // All documents
  get documents(): Document[] {
    return state.documents;
  }

  // Documents by dataset
  async getDocumentsByDataset(datasetId: string, force = false): Promise<Document[]> {
    const localDocuments = state.documents.filter(doc => doc.datasetId === datasetId);
    
    // If we have documents and we're not forcing a refresh, return local
    if (localDocuments.length > 0 && !force) {
      return localDocuments;
    }

    // Otherwise, fetch from the backend
    try {
      console.log(`🔄 Fetching documents for dataset ${datasetId} from backend...`);
      const fetchedDocuments = await trpc.resources.documents.getByDataset.query({ datasetId });
      
      // Update local state
      // Remove old documents for this dataset
      state.documents = state.documents.filter(doc => doc.datasetId !== datasetId);
      // Add new documents
      state.documents.push(...fetchedDocuments);
      
      console.log(`✅ Fetched ${fetchedDocuments.length} documents for dataset ${datasetId}`);
      return fetchedDocuments;
    } catch (error) {
      console.error(`❌ Failed to fetch documents for dataset ${datasetId}:`, error);
      return localDocuments; // Return what we have locally
    }
  }

  async refresh(): Promise<void> {
    console.log('🔄 ResourcePool: Refreshing all data...');
    state.isBootstrapped = false;
    await this.bootstrap();
  }

  private isEmpty(): boolean {
    return state.datasets.length === 0 && 
           state.documents.length === 0 && 
           state.extractionConfigs.length === 0 &&
           state.extractions.length === 0 &&
           state.groundTruths.length === 0;
  }

  // Individual getters
  getDataset(id: string): Dataset | undefined {
    return state.datasets.find(d => d.id === id);
  }

  getDocument(id: string): Document | undefined {
    return state.documents.find(d => d.id === id);
  }

  getExtractionConfig(id: string): ExtractionConfig | undefined {
    return state.extractionConfigs.find(c => c.id === id);
  }

  getExtraction(id: string): Extraction | undefined {
    return state.extractions.find(e => e.id === id);
  }

  getGroundTruth(id: string): GroundTruth | undefined {
    return state.groundTruths.find(gt => gt.id === id);
  }

  getExtractionsByDocument(documentId: string): Extraction[] {
    return state.extractions.filter(e => e.documentId === documentId);
  }

  // ============= MUTATIONS =============

  // Dataset mutations
  async createDataset(data: Omit<Dataset, 'id' | 'createdAt' | 'updatedAt'>): Promise<Dataset> {
    try {
      console.log('🆕 Creating dataset:', data);
      const newDataset = await trpc.resources.datasets.create.mutate(data);
      state.datasets.push(newDataset);
      console.log('✅ Dataset created:', newDataset.id);
      return newDataset;
    } catch (error) {
      console.error('❌ Failed to create dataset:', error);
      throw error;
    }
  }

  async updateDataset(id: string, data: Partial<Omit<Dataset, 'id' | 'createdAt' | 'updatedAt'>>): Promise<Dataset> {
    try {
      console.log(`📝 Updating dataset ${id}:`, data);
      const updatedDataset = await trpc.resources.datasets.update.mutate({ id, data });
      
      const index = state.datasets.findIndex(d => d.id === id);
      if (index >= 0) {
        state.datasets[index] = updatedDataset;
      }
      
      console.log('✅ Dataset updated:', id);
      return updatedDataset;
    } catch (error) {
      console.error(`❌ Failed to update dataset ${id}:`, error);
      throw error;
    }
  }

  async refreshDataset(id: string, data: Partial<Omit<Dataset, 'id' | 'createdAt' | 'updatedAt'>>): Promise<Dataset> {
    try {
      console.log(`🔄 Refreshing dataset ${id}:`, data);
      const refreshResult = await trpc.resources.datasets.refresh.mutate({ id, data });
      
      const refreshedDataset = refreshResult.dataset;
      const newDocuments = refreshResult.documents;

      const index = state.datasets.findIndex(d => d.id === id);
      if (index >= 0) {
        state.datasets[index] = refreshedDataset;
      }
      
      // Update documents in the state
      state.documents = state.documents.filter(doc => doc.datasetId !== id);
      state.documents.push(...newDocuments);

      console.log('✅ Dataset refreshed:', id);
      return refreshedDataset;
    } catch (error) {
      console.error(`❌ Failed to refresh dataset ${id}:`, error);
      throw error;
    }
  }

  async deleteDataset(id: string): Promise<void> {
    try {
      console.log(`🗑️ Deleting dataset ${id}`);
      await trpc.resources.datasets.delete.mutate({ id });
      
      state.datasets = state.datasets.filter(d => d.id !== id);
      
      console.log('✅ Dataset deleted:', id);
    } catch (error) {
      console.error(`❌ Failed to delete dataset ${id}:`, error);
      throw error;
    }
  }

  // Document mutations
  async createDocument(data: Omit<Document, 'id' | 'createdAt' | 'updatedAt'>): Promise<Document> {
    try {
      console.log('🆕 Creating document:', data);
      const newDocument = await trpc.resources.documents.create.mutate(data);
      state.documents.push(newDocument);
      console.log('✅ Document created:', newDocument.id);
      return newDocument;
    } catch (error) {
      console.error('❌ Failed to create document:', error);
      throw error;
    }
  }

  async deleteDocument(id: string): Promise<void> {
    try {
      console.log(`🗑️ Deleting document ${id}`);
      await trpc.resources.documents.delete.mutate({ id });
      
      state.documents = state.documents.filter(d => d.id !== id);
      
      console.log('✅ Document deleted:', id);
    } catch (error) {
      console.error(`❌ Failed to delete document ${id}:`, error);
      throw error;
    }
  }

  // ExtractionConfig mutations
  async createExtractionConfig(data: Omit<ExtractionConfig, 'id' | 'createdAt' | 'updatedAt'>): Promise<ExtractionConfig> {
    try {
      console.log('🆕 Creating extraction config:', data);
      const newConfig = await trpc.resources.extractionConfigs.create.mutate(data);
      state.extractionConfigs.push(newConfig);
      console.log('✅ Extraction config created:', newConfig.id);
      return newConfig;
    } catch (error) {
      console.error('❌ Failed to create extraction config:', error);
      throw error;
    }
  }

  async deleteExtractionConfig(id: string): Promise<void> {
    try {
      console.log(`🗑️ Deleting extraction config ${id}`);
      await trpc.resources.extractionConfigs.delete.mutate({ id });
      
      state.extractionConfigs = state.extractionConfigs.filter(c => c.id !== id);
      
      console.log('✅ Extraction config deleted:', id);
    } catch (error) {
      console.error(`❌ Failed to delete extraction config ${id}:`, error);
      throw error;
    }
  }

  async updateExtractionConfig(id: string, data: any): Promise<ExtractionConfig> {
    try {
      console.log(`📝 Updating extraction config ${id}:`, data);
      const updatedConfig = await trpc.resources.extractionConfigs.update.mutate({ id, data });
      
      const index = state.extractionConfigs.findIndex(c => c.id === id);
      if (index >= 0) {
        state.extractionConfigs[index] = updatedConfig;
      }
      
      console.log('✅ Extraction config updated:', id);
      return updatedConfig;
    } catch (error) {
      console.error(`❌ Failed to update extraction config ${id}:`, error);
      throw error;
    }
  }

  // Ground truth mutations
  async upsertGroundTruth(documentId: string, fieldName: string, userAnnotation: any, isNotExtractable?: boolean): Promise<GroundTruth> {
    try {
      console.log(`📝 Upserting ground truth for document ${documentId}, field ${fieldName}`);
      const groundTruth = await trpc.resources.groundTruths.upsert.mutate({
        documentId,
        fieldName,
        userAnnotation,
        isNotExtractable
      });
      
      const existingIndex = state.groundTruths.findIndex(gt => gt.id === groundTruth.id);
      if (existingIndex >= 0) {
        state.groundTruths[existingIndex] = groundTruth;
      } else {
        state.groundTruths.push(groundTruth);
      }
      
      console.log('✅ Ground truth upserted:', groundTruth.id);
      return groundTruth;
    } catch (error) {
      console.error('❌ Failed to upsert ground truth:', error);
      throw error;
    }
  }

  // Extraction actions
  async runExtraction(documentId: string, extractionConfigId: string, extractorModel: string): Promise<any> {
    try {
      console.log(`🚀 Running extraction: ${documentId} -> ${extractorModel}`);
      const result = await trpc.resources.extractions.runAsync.mutate({
        documentId,
        extractionConfigId,
        extractorModel
      });
      console.log('✅ Extraction queued:', result);
      return result;
    } catch (error) {
      console.error('❌ Failed to run extraction:', error);
      throw error;
    }
  }

  async runExtractions(documentIds: string[], extractionConfigId: string, extractorModels: string[]): Promise<any> {
    try {
      console.log(`🚀 Running batch extractions: ${documentIds.length} docs, ${extractorModels.length} extractors`);
      const result = await trpc.resources.extractions.runBatchAsync.mutate({
        documentIds,
        extractionConfigId,
        extractorModels
      });
      console.log('✅ Batch extraction queued:', result);
      return result;
    } catch (error) {
      console.error('❌ Failed to run batch extractions:', error);
      throw error;
    }
  }

  // WebSocket management
  disconnectWebSocket(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
    
    if (this.ws) {
      this.ws.close(1000, 'Manual disconnect');
      this.ws = null;
    }
    
    state.wsConnected = false;
  }

  reconnectWebSocket(): void {
    this.disconnectWebSocket();
    this.connectWebSocket();
  }

  // Method to subscribe to additional channels after initial connection
  subscribeToAdditionalChannels(channels: string[]): void {
    console.log('📡 ResourcePool: Requesting subscription to additional channels:', channels);
    this.subscribeToChannels(channels);
  }

  // ============= FIELD DISCOVERY =============
  
  async getAvailableFields(): Promise<{
    metadata: any[];
    inputs: any[];
    datapoints: any[];
  }> {
    try {
      const result = await trpc.resources.fieldDiscovery.availableFields.query();
      console.log('✅ Retrieved available fields:', result);
      return result;
    } catch (error) {
      console.error('❌ Failed to get available fields:', error);
      throw error;
    }
  }

  async getFieldsByCategory(category: 'metadata' | 'input' | 'datapoint'): Promise<any[]> {
    try {
      const result = await trpc.resources.fieldDiscovery.byCategory.query({ category });
      console.log(`✅ Retrieved ${category} fields:`, result);
      return result;
    } catch (error) {
      console.error(`❌ Failed to get ${category} fields:`, error);
      throw error;
    }
  }

  async getPopularFields(limit = 20): Promise<any[]> {
    try {
      const result = await trpc.resources.fieldDiscovery.popular.query({ limit });
      console.log(`✅ Retrieved ${limit} popular fields:`, result);
      return result;
    } catch (error) {
      console.error('❌ Failed to get popular fields:', error);
      throw error;
    }
  }
}

// Simple patch application function (legacy support)
export const applyPatch = (patchType: string, data: any): void => {
  console.log(`📨 ResourcePool: Applying patch ${patchType}:`, data);

  switch (patchType) {
    case 'extraction_completed':
    case 'extraction_created':
    case 'extraction_updated':
      if (data.extraction) {
        const extraction = data.extraction;
        const existingIndex = state.extractions.findIndex(e => e.id === extraction.id);
        
        if (existingIndex >= 0) {
          // Update existing extraction
          state.extractions[existingIndex] = extraction;
          console.log(`✅ Applied extraction update patch for ${extraction.id} (status: ${extraction.status})`);
        } else {
          // Add new extraction
          state.extractions.push(extraction);
          console.log(`✅ Applied extraction creation patch for ${extraction.id} (status: ${extraction.status})`);
        }
      }
      break;
      
    case 'dataset_updated':
      if (data.dataset) {
        const dataset = data.dataset;
        const index = state.datasets.findIndex(d => d.id === dataset.id);
        
        if (index >= 0) {
          state.datasets[index] = dataset;
          console.log(`✅ Applied dataset update patch for ${dataset.id}`);
        }
      }
      break;
      
    case 'document_updated':
      if (data.document) {
        const document = data.document;
        const index = state.documents.findIndex(d => d.id === document.id);
        
        if (index >= 0) {
          state.documents[index] = document;
          console.log(`✅ Applied document update patch for ${document.id}`);
        }
      }
      break;
      
    case 'ground_truth_updated':
      if (data.groundTruth) {
        const groundTruth = data.groundTruth;
        const index = state.groundTruths.findIndex(gt => gt.id === groundTruth.id);
        
        if (index >= 0) {
          state.groundTruths[index] = groundTruth;
        } else {
          state.groundTruths.push(groundTruth);
        }
        
        console.log(`✅ Applied ground truth update patch for ${groundTruth.id}`);
      }
      break;
      
    case 'ground_truth_deleted':
      if (data.groundTruth) {
        const groundTruth = data.groundTruth;
        const index = state.groundTruths.findIndex(gt => gt.id === groundTruth.id);
        
        if (index >= 0) {
          state.groundTruths.splice(index, 1);
          console.log(`✅ Applied ground truth deletion patch for ${groundTruth.id}`);
        }
      }
      break;
    default:
      console.warn(`⚠️ Unknown patch type: ${patchType}`);
  }
};

// Export reactive state for components to use
export const useResourcePool = () => {
  return {
    // Reactive state
    datasets: computed(() => state.datasets),
    documents: computed(() => state.documents),
    extractionConfigs: computed(() => state.extractionConfigs),
    extractions: computed(() => state.extractions),
    groundTruths: computed(() => state.groundTruths),
    isBootstrapped: computed(() => state.isBootstrapped),
    isLoading: computed(() => state.isLoading),
    error: computed(() => state.error),
    
    // WebSocket state
    wsConnected: computed(() => state.wsConnected),
    wsError: computed(() => state.wsError),
    
    // Methods
    bootstrap: () => resourcePool.bootstrap(),
    refresh: () => resourcePool.refresh(),
    
    // Getters
    getDataset: (id: string): Dataset | undefined => resourcePool.getDataset(id),
    getDocument: (id: string) => resourcePool.getDocument(id),
    getExtractionConfig: (id: string) => resourcePool.getExtractionConfig(id),
    getExtraction: (id: string) => resourcePool.getExtraction(id),
    getGroundTruth: (id: string) => resourcePool.getGroundTruth(id),
    
    getDocumentsByDataset: (datasetId: string, force?: boolean) => resourcePool.getDocumentsByDataset(datasetId, force),
    getExtractionsByDocument: (documentId: string): Extraction[] => resourcePool.getExtractionsByDocument(documentId),
    
    // Mutations
    createDataset: (data: Omit<Dataset, 'id' | 'createdAt' | 'updatedAt'>) => resourcePool.createDataset(data),
    updateDataset: (id: string, data: Partial<Omit<Dataset, 'id' | 'createdAt' | 'updatedAt'>>) => resourcePool.updateDataset(id, data),
    refreshDataset: (id: string, data: Partial<Omit<Dataset, 'id' | 'createdAt' | 'updatedAt'>>) => resourcePool.refreshDataset(id, data),
    deleteDataset: (id: string) => resourcePool.deleteDataset(id),
    
    createDocument: (data: Omit<Document, 'id' | 'createdAt' | 'updatedAt'>) => resourcePool.createDocument(data),
    deleteDocument: (id: string) => resourcePool.deleteDocument(id),
    
    createExtractionConfig: (data: Omit<ExtractionConfig, 'id' | 'createdAt' | 'updatedAt'>) => resourcePool.createExtractionConfig(data),
    updateExtractionConfig: (id: string, data: any) => resourcePool.updateExtractionConfig(id, data),
    deleteExtractionConfig: (id: string) => resourcePool.deleteExtractionConfig(id),
    
    upsertGroundTruth: (documentId: string, fieldName: string, userAnnotation: any, isNotExtractable?: boolean) => resourcePool.upsertGroundTruth(documentId, fieldName, userAnnotation, isNotExtractable),
    
    runExtraction: (documentId: string, extractionConfigId: string, extractorModel: string) => resourcePool.runExtraction(documentId, extractionConfigId, extractorModel),
    runExtractions: (documentIds: string[], extractionConfigId: string, extractorModels: string[]) => resourcePool.runExtractions(documentIds, extractionConfigId, extractorModels),
    
    // WebSocket management
    reconnectWebSocket: () => resourcePool.reconnectWebSocket(),
    disconnectWebSocket: () => resourcePool.disconnectWebSocket(),
    subscribeToAdditionalChannels: (channels: string[]) => resourcePool.subscribeToAdditionalChannels(channels),
    
    // Field Discovery
    getAvailableFields: () => resourcePool.getAvailableFields(),
    getFieldsByCategory: (category: 'metadata' | 'input' | 'datapoint') => resourcePool.getFieldsByCategory(category),
    getPopularFields: (limit?: number) => resourcePool.getPopularFields(limit),
  };
};

// Use the ResourcePool singleton
export const resourcePool = ResourcePool.getInstance();
// New tRPC-based ResourcePool system
export * from './resourcePool.js';

// tRPC client utilities
export * from '../lib/trpc.js';

// Export resourcePool instance with common aliases for compatibility
export { resourcePool } from './resourcePool.js';

// Document streaming helper (for binary data that tRPC doesn't handle)
export const getDocumentStreamingUrl = (documentIdentifier: string): string => {
  // Construct the streaming URL based on backend API pattern
  const baseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';
  return `${baseUrl}/api/documents/${encodeURIComponent(documentIdentifier)}/stream`;
};

// Document prefetching service
export { documentPrefetcher } from './documentPrefetcher.js';


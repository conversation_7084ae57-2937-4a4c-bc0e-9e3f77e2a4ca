import { ref, onMounted, onUnmounted } from 'vue'
import { applyPatch } from '@/services/resourcePool.js'

export const useWebSocketSync = (documentId?: string, datasetId?: string) => {
  const isConnected = ref(false)
  const error = ref<string | null>(null)
  let ws: WebSocket | null = null
  let reconnectTimer: number | null = null
  let reconnectAttempts = 0
  const maxReconnectAttempts = 5

  const connect = () => {
    try {
      // Connect to the new backend WebSocket endpoint
      const wsUrl = `ws://localhost:8000/ws`
      
      console.log('🔌 Connecting to WebSocket:', wsUrl)
      ws = new WebSocket(wsUrl)

      ws.onopen = () => {
        console.log('🔌 WebSocket connected for real-time updates')
        isConnected.value = true
        error.value = null
        reconnectAttempts = 0

        // Subscribe to extraction and ground truth updates
        const subscriptions = ['extractions', 'ground_truths']
        if (documentId) {
          subscriptions.push(`document:${documentId}`)
        }

        ws?.send(JSON.stringify({
          type: 'subscribe',
          channels: subscriptions
        }))
      }

      ws.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data)
          handleMessage(message)
        } catch (err) {
          console.error('Failed to parse WebSocket message:', err)
        }
      }

      ws.onclose = (event) => {
        console.log('🔌 WebSocket disconnected:', event.code)
        isConnected.value = false
        
        // Attempt to reconnect if not a clean close
        if (event.code !== 1000 && reconnectAttempts < maxReconnectAttempts) {
          const delay = Math.min(1000 * Math.pow(2, reconnectAttempts), 30000)
          console.log(`🔄 Reconnecting WebSocket in ${delay}ms (attempt ${reconnectAttempts + 1}/${maxReconnectAttempts})`)
          
          reconnectTimer = window.setTimeout(() => {
            reconnectAttempts++
            connect()
          }, delay)
        }
      }

      ws.onerror = (err) => {
        console.error('🔌 WebSocket error:', err)
        error.value = 'WebSocket connection error'
      }

    } catch (err) {
      console.error('🔌 Failed to create WebSocket:', err)
      error.value = 'Failed to create WebSocket connection'
    }
  }

  const handleMessage = (message: any) => {
    switch (message.type) {
      case 'connected':
        console.log('✅ Connected to extraction updates:', message.message)
        break

      case 'subscribed':
        console.log('📡 Subscribed to channels:', message.channels)
        break
      
      case 'extraction_running':
        console.log('🏃‍♂️ Extraction running:', message.extraction?.id)
        handleExtractionUpdate(message.extraction)
        break

      case 'extraction_updated':
        console.log('🔄 Extraction updated:', message.extraction?.id, message.extraction?.status)
        handleExtractionUpdate(message.extraction)
        break

      case 'ground_truth_updated':
        console.log('🔄 Ground truth updated:', message.groundTruth?.id, message.groundTruth?.fieldName)
        handleGroundTruthUpdate(message.groundTruth)
        break

      case 'ground_truth_deleted':
        console.log('🗑️ Ground truth deleted:', message.groundTruth?.id, message.groundTruth?.fieldName)
        handleGroundTruthDeleted(message.groundTruth)
        break

      case 'user_annotation_created':
        console.log('✅ User annotation created:', message.annotation?.id)
        handleUserAnnotationCreated(message.annotation)
        break

      case 'error':
        console.error('❌ WebSocket error:', message.message)
        error.value = message.message
        break

      case 'pong':
        // Handle pong response
        console.log('🏓 Pong received')
        break

      default:
        console.log('📨 WebSocket message:', message)
    }
  }

  const handleExtractionUpdate = (extraction: any) => {
    if (extraction) {
      // Apply patch to ResourcePool with the updated extraction
      applyPatch('extraction_updated', { extraction })
    }
  }

  const handleGroundTruthUpdate = (groundTruth: any) => {
    if (groundTruth) {
      // Apply patch to ResourcePool with the updated ground truth
      applyPatch('ground_truth_updated', { groundTruth })
    }
  }

  const handleGroundTruthDeleted = (groundTruth: any) => {
    if (groundTruth) {
      // Apply patch to ResourcePool with the deleted ground truth
      applyPatch('ground_truth_deleted', { groundTruth })
    }
  }

  const handleUserAnnotationCreated = (annotation: any) => {
    if (annotation) {
      // Apply patch to ResourcePool with the new annotation
      applyPatch('user_annotation_created', { annotation })
    }
  }

  const disconnect = () => {
    if (reconnectTimer) {
      clearTimeout(reconnectTimer)
      reconnectTimer = null
    }
    
    if (ws) {
      ws.close(1000, 'Manual disconnect')
      ws = null
    }
    
    isConnected.value = false
  }

  const send = (message: any) => {
    if (ws && ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify(message))
    } else {
      console.warn('⚠️ WebSocket not connected, cannot send message')
    }
  }

  // Auto-connect on mount
  onMounted(() => {
    connect()
  })

  // Clean up on unmount
  onUnmounted(() => {
    disconnect()
  })

  return {
    isConnected,
    error,
    connect,
    disconnect,
    send
  }
}

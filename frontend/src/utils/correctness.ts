/**
 * Calculate correctness score by comparing extracted value with ground truth
 */
export function calculateCorrectness(extractedValue: any, groundTruth: any, fieldType: string): number {
  // Normalize null-like values
  const normalizeNullValue = (value: any): any => {
    if (value === null || value === undefined) return null;
    if (typeof value === 'string') {
      const trimmed = value.trim().toLowerCase();
      // Treat common "null" representations as null
      if (trimmed === 'n/a' || trimmed === 'null' || trimmed === 'none' || trimmed === '' || trimmed === 'not available') {
        return null;
      }
    }
    return value;
  };

  const normalizedExtracted = normalizeNullValue(extractedValue);
  const normalizedGroundTruth = normalizeNullValue(groundTruth);

  // Both null - perfect match
  if (normalizedExtracted === null && normalizedGroundTruth === null) {
    return 100;
  }

  // One is null, the other isn't - no match
  if (normalizedExtracted === null || normalizedGroundTruth === null) {
    return 0;
  }

  // Convert both values to strings for comparison (handles edge cases)
  const extracted = String(normalizedExtracted).trim().toLowerCase()
  const truth = String(normalizedGroundTruth).trim().toLowerCase()

  // Exact match gets 100%
  if (extracted === truth) {
    return 100
  }

  // Field type specific logic
  switch (fieldType) {
    case 'number':
      return calculateNumericCorrectness(normalizedExtracted, normalizedGroundTruth);
    
    case 'date':
      return calculateDateCorrectness(extracted, truth);
    
    case 'text':
    default:
      return calculateTextCorrectness(extracted, truth);
  }
}

/**
 * Calculate correctness for numeric values with tolerance
 */
function calculateNumericCorrectness(extracted: any, truth: any): number {
  const extractedNum = parseFloat(String(extracted))
  const truthNum = parseFloat(String(truth))

  if (isNaN(extractedNum) || isNaN(truthNum)) {
    return 0
  }

  if (extractedNum === truthNum) {
    return 100
  }

  // Calculate percentage difference
  const difference = Math.abs(extractedNum - truthNum)
  const average = (Math.abs(extractedNum) + Math.abs(truthNum)) / 2
  
  if (average === 0) {
    return extractedNum === truthNum ? 100 : 0
  }

  const percentageDiff = (difference / average) * 100

  // Score based on how close the values are
  if (percentageDiff <= 1) return 95  // Within 1%
  if (percentageDiff <= 5) return 85  // Within 5%
  if (percentageDiff <= 10) return 70 // Within 10%
  if (percentageDiff <= 20) return 50 // Within 20%
  if (percentageDiff <= 50) return 25 // Within 50%
  
  return 0 // More than 50% difference
}

/**
 * Calculate correctness for date values
 */
function calculateDateCorrectness(extracted: string, truth: string): number {
  // Try to parse as dates
  const extractedDate = new Date(extracted)
  const truthDate = new Date(truth)

  if (isNaN(extractedDate.getTime()) || isNaN(truthDate.getTime())) {
    // Fall back to string comparison if not valid dates
    return calculateTextCorrectness(extracted, truth)
  }

  // Check if same date
  if (extractedDate.getTime() === truthDate.getTime()) {
    return 100
  }

  // Check if same day (ignore time)
  if (extractedDate.toDateString() === truthDate.toDateString()) {
    return 90
  }

  // Calculate day difference
  const dayDiff = Math.abs(extractedDate.getTime() - truthDate.getTime()) / (1000 * 60 * 60 * 24)
  
  if (dayDiff <= 1) return 80  // Within 1 day
  if (dayDiff <= 7) return 60  // Within 1 week
  if (dayDiff <= 30) return 40 // Within 1 month
  
  return 0 // More than 1 month difference
}

/**
 * Calculate correctness for text values using similarity
 */
function calculateTextCorrectness(extracted: string, truth: string): number {
  if (extracted === truth) {
    return 100
  }

  // Check if one contains the other
  if (extracted.includes(truth) || truth.includes(extracted)) {
    return 80
  }

  // Calculate Levenshtein distance for similarity
  const similarity = calculateStringSimilarity(extracted, truth)
  
  if (similarity >= 0.9) return 85
  if (similarity >= 0.8) return 70
  if (similarity >= 0.6) return 50
  if (similarity >= 0.4) return 30
  
  return 0
}

/**
 * Calculate string similarity using Levenshtein distance
 */
function calculateStringSimilarity(str1: string, str2: string): number {
  const len1 = str1.length
  const len2 = str2.length

  if (len1 === 0) return len2 === 0 ? 1 : 0
  if (len2 === 0) return 0

  const matrix = Array(len2 + 1).fill(null).map(() => Array(len1 + 1).fill(null))

  for (let i = 0; i <= len1; i++) matrix[0][i] = i
  for (let j = 0; j <= len2; j++) matrix[j][0] = j

  for (let j = 1; j <= len2; j++) {
    for (let i = 1; i <= len1; i++) {
      const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1
      matrix[j][i] = Math.min(
        matrix[j][i - 1] + 1,     // deletion
        matrix[j - 1][i] + 1,     // insertion
        matrix[j - 1][i - 1] + indicator // substitution
      )
    }
  }

  const distance = matrix[len2][len1]
  const maxLen = Math.max(len1, len2)
  
  return maxLen === 0 ? 1 : (maxLen - distance) / maxLen
} 
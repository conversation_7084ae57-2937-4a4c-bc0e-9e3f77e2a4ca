export interface ErrorInfo {
  type: 'network' | 'not_found' | 'server' | 'unknown';
  message: string;
  canRetry: boolean;
}

export function analyzeError(error: any): ErrorInfo {
  // Check if it's a network connectivity error
  if (error?.code === 'ECONNREFUSED' || 
      error?.code === 'NETWORK_ERROR' ||
      error?.name === 'NetworkError' ||
      error?.message?.includes('fetch') ||
      error?.message?.includes('Network request failed') ||
      error?.message?.includes('Failed to fetch') ||
      (error?.response === undefined && error?.request !== undefined)) {
    return {
      type: 'network',
      message: 'Unable to connect to the backend server. Please check if the server is running.',
      canRetry: true
    };
  }

  // Check if it's a 404 Not Found error
  if (error?.response?.status === 404 || 
      error?.status === 404 ||
      error?.message?.includes('404') ||
      error?.message?.includes('not found')) {
    return {
      type: 'not_found',
      message: 'The requested resource could not be found.',
      canRetry: false
    };
  }

  // Check if it's a server error (5xx)
  if (error?.response?.status >= 500 || 
      error?.status >= 500) {
    return {
      type: 'server',
      message: 'Server error occurred. Please try again later.',
      canRetry: true
    };
  }

  // Check for common network-related error messages
  if (error?.message) {
    const message = error.message.toLowerCase();
    if (message.includes('connection') || 
        message.includes('timeout') ||
        message.includes('network') ||
        message.includes('refused') ||
        message.includes('unreachable')) {
      return {
        type: 'network',
        message: 'Unable to connect to the backend server. Please check if the server is running.',
        canRetry: true
      };
    }
  }

  // Default to unknown error
  return {
    type: 'unknown',
    message: error?.message || 'An unexpected error occurred.',
    canRetry: true
  };
}

export function isNetworkError(error: any): boolean {
  return analyzeError(error).type === 'network';
}

export function isNotFoundError(error: any): boolean {
  return analyzeError(error).type === 'not_found';
} 
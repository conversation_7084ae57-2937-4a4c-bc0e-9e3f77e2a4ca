<template>
  <component 
    :is="iconComponent" 
    :class="className"
  />
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { 
  Type, 
  Hash, 
  Calendar, 
  ToggleLeft, 
  Percent, 
  DollarSign, 
  Mail, 
  Phone, 
  MapPin 
} from 'lucide-vue-next';

interface Props {
  type: string;
  class?: string;
}

const props = withDefaults(defineProps<Props>(), {
  class: 'h-4 w-4'
});

const className = computed(() => props.class);

const iconComponent = computed(() => {
  const iconMap: Record<string, any> = {
    'text': Type,
    'number': Hash,
    'date': Calendar,
    'boolean': ToggleLeft,
    'percentage': Percent,
    'currency': DollarSign,
    'email': Mail,
    'phone': Phone,
    'address': MapPin
  };
  
  return iconMap[props.type] || Type;
});
</script> 
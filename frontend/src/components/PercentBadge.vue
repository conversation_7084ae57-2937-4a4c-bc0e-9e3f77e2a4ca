<template>
  <div 
    class="inline-flex items-center justify-center px-2 py-1 rounded-full text-xs font-medium font-mono w-12 shadow-sm"
    :style="badgeStyle"
  >
    {{ displayText }}
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  percentage: number
}

const props = defineProps<Props>()

const displayText = computed(() => {
  return `${Math.round(props.percentage)}%`
})

const badgeStyle = computed(() => {
  // Calculate color based on percentage (0% = red, 50% = yellow, 100% = green)
  const percentage = Math.max(0, Math.min(100, props.percentage))
  
  // RGB values for -300 variants (Tailwind theme colors)
  const red = { r: 252, g: 165, b: 165 }     // red-300
  const yellow = { r: 253, g: 224, b: 71 }   // yellow-300
  const green = { r: 134, g: 239, b: 172 }   // green-300
  
  let r, g, b
  
  if (percentage <= 50) {
    // Interpolate from red to yellow (0% to 50%)
    const t = percentage / 50
    r = Math.round(red.r + (yellow.r - red.r) * t)
    g = Math.round(red.g + (yellow.g - red.g) * t)
    b = Math.round(red.b + (yellow.b - red.b) * t)
  } else {
    // Interpolate from yellow to green (50% to 100%)
    const t = (percentage - 50) / 50
    r = Math.round(yellow.r + (green.r - yellow.r) * t)
    g = Math.round(yellow.g + (green.g - yellow.g) * t)
    b = Math.round(yellow.b + (green.b - yellow.b) * t)
  }
  
  return {
    backgroundColor: `rgb(${r}, ${g}, ${b})`,
    color: '#374151' // gray-700 for better contrast on -300 backgrounds
  }
})
</script> 
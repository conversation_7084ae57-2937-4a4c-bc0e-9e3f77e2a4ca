<template>
  <div class="flex items-center justify-center" :class="sizeClasses">
    <!-- Gemini Logo -->
    <div v-if="normalizedExtractor === 'gemini'" class="flex items-center justify-center">
      <img 
        src="../assets/logos/gemini.svg" 
        :width="size" 
        :height="size" 
        alt="Gemini"
        class="object-contain"
      />
    </div>
    
    <!-- Mistral Logo -->
    <div v-else-if="normalizedExtractor === 'mistral'" class="flex items-center justify-center">
      <img 
        src="../assets/logos/mistral.svg" 
        :width="size" 
        :height="size" 
        alt="Mistral"
        class="object-contain"
      />
    </div>
    
    <!-- Default/Unknown Extractor -->
    <div v-else class="flex items-center justify-center bg-gray-200 rounded-full" :style="{ width: size + 'px', height: size + 'px' }">
      <span class="text-xs font-medium text-gray-600 uppercase">{{ extractor.slice(0, 2) }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  extractor: string;
  size?: number;
}

const props = withDefaults(defineProps<Props>(), {
  size: 16
});

const normalizedExtractor = computed(() => {
  const extractorLower = props.extractor.toLowerCase();
  
  if (extractorLower.includes('gemini')) {
    return 'gemini';
  } else if (extractorLower.includes('mistral')) {
    return 'mistral';
  }
  
  return extractorLower;
});

const sizeClasses = computed(() => {
  if (props.size <= 16) return 'w-4 h-4';
  if (props.size <= 20) return 'w-5 h-5';
  if (props.size <= 24) return 'w-6 h-6';
  return 'w-8 h-8';
});
</script> 
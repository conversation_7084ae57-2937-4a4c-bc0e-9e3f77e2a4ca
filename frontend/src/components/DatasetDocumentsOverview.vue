<template>
  <div class="bg-card border border-border rounded-lg shadow-sm">
    <div class="p-6 border-b border-border">
      <div class="flex items-center justify-between">
        <div>
          <h2 class="text-xl font-semibold text-foreground">Documents</h2>
          <p class="text-sm text-muted-foreground mt-1">
            {{ documents.length }} document{{ documents.length !== 1 ? 's' : '' }} found
            <span v-if="extractedCount > 0" class="ml-2 text-green-600">
              ({{ extractedCount }} extracted)
            </span>
            <span v-if="approvalSummary.approved > 0" class="ml-2 text-green-600">
              ({{ approvalSummary.approved }} approved)
            </span>
            <span v-if="approvalSummary.pending > 0" class="ml-2 text-yellow-600">
              ({{ approvalSummary.pending }} pending)
            </span>
          </p>
        </div>
        <div class="flex items-center gap-3">
          <div v-if="isLoadingDocs" class="flex items-center gap-2 text-sm text-muted-foreground">
            <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
            Loading documents...
          </div>
          <div v-if="documents.length > 0 && config" class="flex items-center gap-2">
            <button
              @click="runBatchExtraction(false)"
              :disabled="isBatchExtracting || extractionTasksToStart === 0"
              class="px-3 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors flex items-center gap-2"
            >
              <Play class="h-4 w-4" />
              Run Missing ({{ extractionTasksToStart }})
            </button>
            <button
              @click="runBatchExtraction(true)"
              :disabled="isBatchExtracting"
              class="px-3 py-2 text-sm bg-gray-600 text-white rounded-md hover:bg-gray-700 disabled:opacity-50 transition-colors flex items-center gap-2"
            >
              <Play class="h-4 w-4" />
              Run All
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <div v-if="documents.length === 0 && !isLoadingDocs" class="p-8 text-center text-muted-foreground">
      <FileText class="h-12 w-12 mx-auto mb-3 opacity-50" />
      <p>No documents found</p>
      <p class="text-sm mt-1">Check your SQL query to ensure it returns document paths</p>
    </div>
    
    <ul v-else class="divide-y divide-border">
      <li 
        v-for="doc in documents" 
        :key="doc.id" 
        class="p-4 hover:bg-muted/50 transition-colors"
      >
        <div class="flex items-center space-x-4">
          <div class="flex-shrink-0">
            <FileText class="h-5 w-5 text-muted-foreground" />
          </div>
          <div class="flex-1 min-w-0 cursor-pointer" @click="viewDocument(doc.id)">
            <div class="font-mono text-sm font-medium text-foreground truncate">
              {{ doc.gcsPath }}
            </div>
            <div class="flex items-center gap-4 text-xs text-muted-foreground mt-1">
              <span v-if="doc.siteName" class="flex items-center gap-1">
                <MapPin class="h-3 w-3" />
                {{ doc.siteName }}
              </span>
              <span v-if="doc.objectType" class="flex items-center gap-1">
                <Tag class="h-3 w-3" />
                {{ doc.objectType }}
              </span>
              <span v-if="doc.emissionsLogId" class="flex items-center gap-1">
                <Hash class="h-3 w-3" />
                {{ doc.emissionsLogId }}
              </span>
            </div>
          </div>
          <div class="flex items-center gap-4 flex-shrink-0">
            <!-- Document Status Indicator -->
            <div class="flex items-center">
              <DocumentStatusBadge :status="getDocumentStatus(doc.id)" />
            </div>
            
            <!-- Ground Truth Type Indicator -->
            <div class="flex items-center">
              <span 
                :class="{
                  'bg-gray-100 text-gray-600': getGroundTruthType(doc.id).type === 'none',
                  'bg-blue-100 text-blue-700': getGroundTruthType(doc.id).type === 'database',
                  'bg-green-100 text-green-700': getGroundTruthType(doc.id).type === 'annotated',
                  'bg-purple-100 text-purple-700': getGroundTruthType(doc.id).type === 'approved'
                }"
                class="px-2 py-1 text-xs font-medium rounded-full"
              >
                {{ getGroundTruthType(doc.id).label }}
              </span>
            </div>
            
            <!-- Extractor Panels -->
            <div class="flex flex-col gap-2">
              <ExtractorPanel 
                v-for="extractor in effectiveExtractors" 
                :key="extractor"
                :extractor="extractor"
                :accuracy="documentAccuracies.get(doc.id)?.[extractor] || null"
                :has-extraction="hasExtractorRun(doc.id, extractor)"
                :is-extracting="extractingDocs.has(`${doc.id}-${extractor}`)"
                :disabled="!config || extractingDocs.has(`${doc.id}-${extractor}`)"
                :extraction-status="extractionStatuses.get(`${doc.id}-${extractor}`) || null"
                @extract="() => {}"
              />
              
              <!-- Show "No extractions" if no methods found and no extractions exist -->
              <div v-if="!documentExtractions.has(doc.id) && effectiveExtractors.length === 0" class="px-2 py-0.5 text-xs font-medium rounded border bg-gray-100 text-gray-400 border-gray-200">
                No extractors available
              </div>
            </div>
            
            <ChevronRight class="h-5 w-5 text-muted-foreground cursor-pointer" @click="viewDocument(doc.id)" />
          </div>
        </div>
      </li>
    </ul>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useResourcePool } from '../services';
import type { Document, ExtractionConfig, ExtractionField } from '../types';
import ExtractorPanel from './ExtractorPanel.vue';
import DocumentStatusBadge from './DocumentStatusBadge.vue';
import { calculateCorrectness } from '@/utils/correctness';
import { 
  FileText, 
  ChevronRight, 
  MapPin,
  Tag,
  Hash,
  Play,
} from 'lucide-vue-next';


interface Props {
  documents: Document[];
  config: ExtractionConfig | null;
  isLoadingDocs: boolean;
  datasetId: string;
  extractionData: Map<string, any>;
  groundTruthData: Map<string, any>;
  approvalData: Map<string, any>;
  extractionStatusData: Map<string, any>;
  availableExtractors: string[];
}

const props = defineProps<Props>();
const router = useRouter();
const { documents: allDocuments, groundTruths } = useResourcePool();

const documentAccuracies = computed(() => {
  const allAccuracies = new Map<string, Record<string, any>>();
  for (const doc of props.documents) {
    allAccuracies.set(doc.id, getDocumentAccuracies(doc.id));
  }
  return allAccuracies;
});

const formattedConfigFields = computed(() => {
  if (!props.config?.data.fields) {
    return undefined;
  }
  return props.config.data.fields.reduce((acc: Record<string, { field_type: string }>, field: ExtractionField) => {
    acc[field.name] = { field_type: field.fieldType };
    return acc;
  }, {});
});

// Computed property that combines backend extractors with config extractors
const effectiveExtractors = computed(() => {
  const backendExtractors = props.availableExtractors || [];
  const configExtractors = props.config?.data?.extractors?.map(e => e.model) || [];
  
  // Use config extractors if available, otherwise fall back to backend extractors
  if (configExtractors.length > 0) {
    return configExtractors;
  }
  
  return backendExtractors;
});
const extractionStatuses = computed(() => props.extractionStatusData);
const documentExtractions = computed(() => props.extractionData);
const documentGroundTruth = computed(() => props.groundTruthData);
const documentApprovals = computed(() => props.approvalData);

const getApprovalStatus = (documentId: string) => {
  return documentApprovals.value.get(documentId);
};

// Document status function using resourcePool data  
const getDocumentStatus = (docId: string): 'approved' | 'unapproved' | 'needs-ground-truth' | 'pending-review' | 'unknown' => {
  const doc = allDocuments.value.find(d => d.id === docId);
  const hasGT = groundTruths.value.some(gt => gt.documentId === docId);
  
  if (!doc) return 'unknown';
  
  // Priority: Approval status first, then ground truth status
  if (doc.isApproved === true) {
    return 'approved';
  } else if (doc.isApproved === false) {
    return 'unapproved';
  } else if (!hasGT) {
    return 'needs-ground-truth';
  } else {
    return 'pending-review';
  }
};

const approvalSummary = computed(() => {
  let approved = 0;
  let pending = 0;
  let notReviewed = 0;
  
  props.documents.forEach(doc => {
    const status = getApprovalStatus(doc.id);
    if (status?.isApproved === true) {
      approved++;
    } else if (status?.isApproved === false) {
      pending++;
    } else {
      notReviewed++;
    }
  });
  
  return {
    approved,
    pending,
    notReviewed,
    total: props.documents.length
  };
});

const runBatchExtraction = async (includeCompleted: boolean) => {
  if (!props.config?.id || effectiveExtractors.value.length === 0) {
    return;
  }

  const documentIds = includeCompleted
    ? props.documents.map(doc => doc.id)
    : documentsWithoutExtractions.value.map(doc => doc.id);

  if (documentIds.length === 0) {
    return;
  }

  const extractors = props.config.data.extractors.map(e => ({
    provider: e.provider,
    model: e.model,
  }));

  try {
    await businessAPI.runBatchExtraction(
      documentIds,
      props.config.id,
      extractors,
      includeCompleted
    );
  } catch (error) {
    console.error('Failed to start batch extraction:', error);
  }
};

const isBatchExtracting = computed(() => {
  return [...extractingDocs.value].some(key => key.includes('-batch-'));
});

const extractedCount = computed(() => {
  return props.documents.filter(doc => documentExtractions.value.has(doc.id)).length;
});

const documentsWithExtractions = computed(() => {
  return props.documents.filter(doc => documentExtractions.value.has(doc.id));
});

const documentsWithoutExtractions = computed(() => {
  if (effectiveExtractors.value.length === 0) return props.documents;
  
  return props.documents.filter(doc => {
    const docExtractions = documentExtractions.value.get(doc.id);
    if (!docExtractions) return true;
    
    // Check if document has extractions for all available extractors
    const hasAllExtractions = effectiveExtractors.value.every(extractor => 
      docExtractions.extraction_results.some((r: any) => r.model === extractor)
    );
    
    // Include document if it doesn't have all extractions
    return !hasAllExtractions;
  });
});

const extractionTasksToStart = computed(() => {
  if (effectiveExtractors.value.length === 0) return 0;
  
  let totalTasks = 0;
  documentsWithoutExtractions.value.forEach(doc => {
    const docExtractions = documentExtractions.value.get(doc.id);
    if (!docExtractions) {
      // Document has no extractions, so all extractors need to run
      totalTasks += effectiveExtractors.value.length;
    } else {
      // Count missing extractors for this document
      const missingExtractors = effectiveExtractors.value.filter(extractor => 
        !docExtractions.extraction_results.some((r: any) => r.model === extractor)
      );
      totalTasks += missingExtractors.length;
    }
  });
  
  return totalTasks;
});

const viewDocument = (docId: string) => {
  router.push({ 
    name: 'DocumentDetail', 
    params: { datasetId: props.datasetId, documentId: docId }, 
    query: { extraction_config_id: props.config?.id }
  });
};

const hasExtractorRun = (docId: string, extractor: string): boolean => {
  const docExtractions = documentExtractions.value.get(docId);
  if (!docExtractions) {
    return false;
  }
  return docExtractions.extraction_results.some((r: any) => r.model === extractor);
};


const getGroundTruthType = (docId: string): { type: 'none' | 'database' | 'annotated' | 'approved', label: string } => {
  // Find the document to check approval status
  const doc = props.documents.find(d => d.id === docId);
  
  // Check for approval first (highest priority)
  if (doc && doc.isApproved === true) {
    return { type: 'approved', label: 'Approved' };
  }
  
  const detailedGroundTruth = documentGroundTruth.value.get(docId);
  
  if (!detailedGroundTruth || Object.keys(detailedGroundTruth).length === 0) {
    return { type: 'none', label: 'No Ground Truth' };
  }

  const fields = Object.values(detailedGroundTruth);
  const hasAnnotation = fields.some((field: any) => field.user_annotation !== null && field.user_annotation !== undefined);
  if (hasAnnotation) {
    return { type: 'annotated', label: 'Annotated' };
  }
  
  const hasExtractedValue = fields.some((field: any) => field.extracted_value !== null && field.extracted_value !== undefined);
  if (hasExtractedValue) {
    return { type: 'database', label: 'Database GT' };
  }
  
  return { type: 'none', label: 'No Ground Truth' };
};

const getDocumentAccuracies = (docId: string) => {
  const extractionData = documentExtractions.value.get(docId);
  const groundTruthData = documentGroundTruth.value.get(docId) || {};
  
  if (!extractionData || !extractionData.extraction_results) {
    return {};
  }
  
  const accuracies: Record<string, {
    correct: number;
    total: number;
    percentage: number;
    hasGroundTruth: boolean;
  }> = {};
  
  // Group extractions by method
  const extractionsByMethod: Record<string, any> = {};
  extractionData.extraction_results.forEach((extraction: any) => {
    // Find the matching extractor name using includes logic
    const matchingExtractor = effectiveExtractors.value.find(extractor => 
      extraction.model === extractor
    );
    if (matchingExtractor) {
      extractionsByMethod[matchingExtractor] = extraction.extracted_data;
    }
  });
  
  // Calculate accuracy for each method
  Object.entries(extractionsByMethod).forEach(([method, extractedData]) => {
    let correct = 0;
    let total = 0;
    let hasAnyGroundTruth = false;
    
    // Compare each field with ground truth
    Object.entries(groundTruthData).forEach(([fieldName, groundTruthField]: [string, any]) => {
      // Extract ground truth using same logic as DocumentDetail.vue
      // If user_annotation property exists (even if null), use it; otherwise use extracted_value
      const groundTruthValue = groundTruthField?.hasOwnProperty('user_annotation') 
        ? groundTruthField.user_annotation 
        : (groundTruthField?.extracted_value ?? undefined);
      
      // Only count fields that have some form of ground truth (including null for "not extractable")
      if (groundTruthValue !== undefined) {
        hasAnyGroundTruth = true;
        total++;
        
        const extractedValue = extractedData?.[fieldName];
        if (extractedValue !== undefined) {
          // Get field type for comparison
          const fieldConfig = formattedConfigFields.value?.[fieldName];
          const fieldType = fieldConfig?.field_type || 'text';
          
          // Use improved correctness calculation that handles null values properly
          const correctnessScore = calculateCorrectness(extractedValue, groundTruthValue, fieldType);
          // Consider it correct if score is >= 70%
          if (correctnessScore >= 70) {
            correct++;
          }
        }
      }
    });
    
    // Always include the method in accuracies, even if no ground truth
    accuracies[method] = {
      correct,
      total,
      percentage: total > 0 ? (correct / total) * 100 : 0,
      hasGroundTruth: hasAnyGroundTruth
    };
  });
  
  return accuracies;
};
</script> 
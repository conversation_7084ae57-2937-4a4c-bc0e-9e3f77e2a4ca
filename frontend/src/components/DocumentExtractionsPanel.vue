<template>
  <div class="bg-card border border-border rounded-lg overflow-hidden lg:col-span-2">
    <div class="border-b border-border p-4">
      <div class="flex items-center justify-between">
        <div>
          <h2 class="text-lg font-semibold text-foreground">Extraction Comparison</h2>
          <p class="text-sm text-muted-foreground">
            Side-by-side results from different models
          </p>
        </div>
      </div>
    </div>
    
    <div class="p-4 overflow-y-auto h-full">
      <div v-if="isInitializing" class="space-y-4">
        <!-- Skeleton for extractor headers -->
        <div class="grid gap-4 pb-2 border-b border-border" :style="{ gridTemplateColumns: `repeat(${Math.max(availableExtractors.length + 1, 3)}, minmax(0, 1fr))` }">
          <div class="col-span-1"></div>
          <div v-for="i in Math.max(availableExtractors.length, 2)" :key="i" class="text-center col-span-1 space-y-2">
            <div class="flex items-center justify-center space-x-3 mb-3">
              <Skeleton class="w-6 h-6 rounded" />
              <Skeleton class="h-4 w-16" />
              <Skeleton class="w-5 h-5 rounded" />
            </div>
            <Skeleton class="w-8 h-8 rounded-lg mx-auto" />
            <Skeleton class="h-3 w-20 mx-auto" />
          </div>
        </div>
        
        <!-- Skeleton for field rows -->
        <div class="space-y-2">
          <div v-for="i in 5" :key="i" class="border border-border rounded-lg p-4">
            <div class="grid gap-4" :style="{ gridTemplateColumns: `repeat(${Math.max(availableExtractors.length + 1, 3)}, minmax(0, 1fr))` }">
              <div class="space-y-2">
                <Skeleton class="h-4 w-24" />
                <Skeleton class="h-3 w-16" />
              </div>
              <div v-for="j in Math.max(availableExtractors.length, 2)" :key="j" class="space-y-2">
                <Skeleton class="h-4 w-full" />
                <Skeleton class="h-3 w-3/4" />
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="space-y-2">
        <!-- Extractor Headers -->
        <div v-if="!isInitializing && extractionConfig" class="grid gap-4 pb-4 border-b border-border" :style="{ gridTemplateColumns: `repeat(${availableExtractors.length + 1}, minmax(0, 1fr))` }">
          <!-- Empty column for field names -->
          <div class="col-span-1">
            <div class="flex items-center justify-between">
              <h3 class="text-sm font-medium text-muted-foreground">Fields</h3>
              <button
                @click="$emit('run-all-extractions')"
                :disabled="isInitializing"
                class="px-3 py-1.5 bg-primary hover:bg-primary/90 text-primary-foreground rounded-md text-xs font-medium disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-1"
                title="Run all extractors"
              >
                <Play class="h-3 w-3" />
                <span>Run All</span>
              </button>
            </div>
          </div>
          
          <!-- Extractor columns -->
          <div v-for="extractor in availableExtractors" :key="extractor.model" class="col-span-1 text-center">
            <div class="space-y-3">
              <!-- Extractor info -->
              <div class="flex items-center justify-center space-x-2">
                <ExtractorLogo :extractor="extractor.model" :size="20" />
                <h4 class="font-medium text-sm text-foreground">{{ extractor.model }}</h4>
                <div 
                  class="w-2 h-2 rounded-full"
                  :class="{
                    'bg-green-500': extractorStatus[extractor.model] === 'success',
                    'bg-yellow-500': extractorStatus[extractor.model] === 'loading',
                    'bg-red-500': extractorStatus[extractor.model] === 'error',
                    'bg-gray-300': extractorStatus[extractor.model] === 'idle'
                  }"
                  :title="`Status: ${extractorStatus[extractor.model] || 'idle'}`"
                ></div>
              </div>
              
              <!-- Run button -->
              <button
                @click="runSingleExtraction(extractor.model)"
                :disabled="extractorStatus[extractor.model] === 'loading' || isInitializing"
                class="w-full px-2 py-1 text-xs border border-border hover:bg-accent rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-1"
                :title="`Run ${extractor.model} extraction`"
              >
                <component 
                  :is="extractorStatus[extractor.model] === 'loading' ? Loader2 : Play" 
                  class="h-3 w-3"
                  :class="{ 'animate-spin': extractorStatus[extractor.model] === 'loading' }"
                />
                <span>{{ extractorStatus[extractor.model] === 'loading' ? 'Running' : 'Run' }}</span>
              </button>
              
              <!-- Provider info -->
              <p class="text-xs text-muted-foreground">{{ extractor.provider }}</p>
            </div>
          </div>
        </div>
       
        <!-- Field Comparisons -->
        <div v-if="!isInitializing && extractionConfig" class="space-y-2">
          <FieldComparison
            v-for="field in comparedFields"
            :key="field.name"
            :field="field"
            :document-id="documentId"
            :extraction-providers="availableExtractors"
            :ground-truth-data="groundTruthData"
            :extractor-status="extractorStatus"
            @update-annotation="handleAnnotationUpdate" 
            @remove-annotation="handleAnnotationRemoval"
          />
        </div>
        
        <!-- Failed Extractions Error Row - Sticky at bottom -->
        <div 
          v-if="!isInitializing && failedExtractions.length > 0" 
          class="sticky bottom-0 bg-red-50 border border-red-200 rounded-lg p-4 mt-4 shadow-lg z-10"
        >
          <div class="flex items-start space-x-3">
            <XCircle class="h-5 w-5 text-red-500 flex-shrink-0 mt-0.5" />
            <div class="flex-1">
              <h3 class="text-sm font-medium text-red-800 mb-2">
                Failed Extractions ({{ failedExtractions.length }})
              </h3>
              <div class="space-y-2">
                <div 
                  v-for="failed in failedExtractions" 
                  :key="failed.extractor.id"
                  class="flex items-start space-x-3 p-2 bg-red-100 rounded border border-red-200"
                >
                  <ExtractorLogo :extractor="failed.extractor.model || 'unknown'" :size="16" />
                  <div class="flex-1 min-w-0">
                    <div class="flex items-center space-x-2">
                      <span class="text-sm font-medium text-red-700">{{ failed.extractor.model }}</span>
                      <span class="text-xs text-red-600 bg-red-200 px-2 py-0.5 rounded">Failed</span>
                    </div>
                    <p class="text-xs text-red-600 mt-1 break-words">{{ failed.error }}</p>
                  </div>
                  <button
                    @click="runSingleExtraction(failed.extractor.id)"
                    :disabled="extractorStatus[failed.extractor.id] === 'loading'"
                    class="p-1 bg-red-600 hover:bg-red-700 text-white rounded text-xs disabled:opacity-50 disabled:cursor-not-allowed transition-colors shadow-sm flex items-center space-x-1"
                    :title="`Retry ${failed.extractor.model} extraction`"
                  >
                    <RotateCcw class="h-3 w-3" />
                    <span class="hidden sm:inline">Retry</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Skeleton loading state for field comparisons -->
        <div v-else-if="isInitializing" class="space-y-4">
          <!-- Skeleton for extractor headers -->
          <div class="grid gap-4 pb-2 border-b border-border" :style="{ gridTemplateColumns: 'repeat(3, minmax(0, 1fr))' }">
            <div class="col-span-1"></div>
            <div v-for="i in 2" :key="i" class="text-center col-span-1 space-y-2">
              <div class="flex items-center justify-center space-x-3 mb-3">
                <Skeleton class="w-6 h-6 rounded" />
                <Skeleton class="h-4 w-16" />
                <Skeleton class="w-5 h-5 rounded" />
              </div>
              <Skeleton class="w-8 h-8 rounded-lg mx-auto" />
              <Skeleton class="h-3 w-20 mx-auto" />
            </div>
          </div>
          
          <!-- Skeleton for loading field rows -->
          <div class="space-y-2">
            <div v-for="i in 4" :key="i" class="border border-border rounded-lg p-4">
              <div class="grid gap-4" :style="{ gridTemplateColumns: 'repeat(3, minmax(0, 1fr))' }">
                <div class="space-y-2">
                  <Skeleton class="h-4 w-24" />
                  <Skeleton class="h-3 w-16" />
                </div>
                <div v-for="j in 2" :key="j" class="space-y-2">
                  <Skeleton class="h-4 w-full" />
                  <Skeleton class="h-3 w-3/4" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { CheckCircle2, XCircle, Play, RotateCcw, Loader2, X } from 'lucide-vue-next'
import FieldComparison from '@/components/FieldComparison.vue'
import ExtractorLogo from '@/components/ExtractorLogo.vue'
import { Skeleton } from '@/components/ui/skeleton'
import { calculateCorrectness } from '@/utils/correctness'
import { useResourcePool } from '@/services/resourcePool.js'
import { trpc } from '@/lib/trpc.js'

interface Props {
  documentId: string
  datasetId: string
  extractionConfig: any
  availableExtractors: Array<{id: string, model: string, provider: string}>
  comparedFields: Array<{name: string, results: Record<string, any>}>
  groundTruthData: Record<string, any>
  failedExtractions: Array<{extractor: any, error: string}>
  extractorStatus: Record<string, 'idle' | 'loading' | 'success' | 'error'>
  isInitializing: boolean
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'run-single-extraction': [extractorId: string]
  'run-all-extractions': []
}>()

const {
  extractionConfigs, extractions, groundTruths,
} = useResourcePool()

// Methods
const handleAnnotationUpdate = async (fieldName: string, value: any, isNotExtractable?: boolean) => {
  try {
    await trpc.resources.groundTruths.upsert.mutate({
      documentId: props.documentId,
      fieldName,
      userAnnotation: value,
      isNotExtractable: isNotExtractable
    })
  } catch (error) {
    console.error('Failed to update annotation:', error)
  }
}

const handleAnnotationRemoval = async (fieldName: string) => {
  try {
    await trpc.resources.groundTruths.remove.mutate({
      documentId: props.documentId,
      fieldName
    })
  } catch (error) {
    console.error('Failed to remove annotation:', error)
  }
}

const runSingleExtraction = (extractorId: string) => {
  emit('run-single-extraction', extractorId)
}


</script>

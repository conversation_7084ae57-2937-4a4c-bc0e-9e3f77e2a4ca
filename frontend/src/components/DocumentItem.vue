<template>
  <div
    class="bg-card border border-border rounded-lg p-4 hover:bg-accent/50 cursor-pointer transition-colors"
    @click="$emit('click')"
  >
    <div class="flex items-start justify-between">
      <div class="flex-1">
        <div class="flex items-center space-x-3">
          <div class="p-2 bg-primary/10 rounded-md">
            <FileText class="h-5 w-5 text-primary" />
          </div>
          <div>
            <h3 class="font-medium text-foreground">{{ document.name }}</h3>
            <p class="text-sm text-muted-foreground">{{ document.type }} • {{ formatFileSize(document.size) }}</p>
          </div>
        </div>
        
        <div class="mt-3 flex items-center space-x-4 text-sm text-muted-foreground">
          <span>{{ formatDate(document.createdAt) }}</span>
          <span>Ready for extraction</span>
        </div>
      </div>

      <div class="flex items-center space-x-2">
        <ChevronRight class="h-4 w-4 text-muted-foreground" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { FileText, ChevronRight } from 'lucide-vue-next'
import type { Document } from '@/types'

interface Props {
  document: Document
}

defineProps<Props>()

defineEmits<{
  click: []
}>()

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`
}

const formatDate = (date: string): string => {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  }).format(new Date(date))
}
</script>

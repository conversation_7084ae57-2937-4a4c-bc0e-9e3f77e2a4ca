<template>
  <Card 
    ref="fieldElement"
    :class="[
      'transition-colors duration-200 relative cursor-pointer px-2',
      {
        'border-red-200 bg-red-50': extraction?.status === 'failed' || extraction?.status === 'error',
        'hover:bg-blue-50 hover:border-blue-200': extraction,
        'border-dashed border-gray-300 bg-gray-50/50': !extraction,
        'border-green-500': isAnnotated,
      }
    ]"
    @mouseenter="onHover"
    @mouseleave="onUnhover"
    @click="onClick"
  >
    <!-- Hover Check Mark with Tooltip (only show if extraction has been run) -->
    <div 
      v-show="isHovered && extraction"
      class="absolute top-2 right-2 bg-blue-600 text-white rounded-full p-1 shadow-lg z-10 group"
    >
      <Check class="h-3 w-3" />
      <div class="absolute bottom-full right-0 mb-2 px-2 py-1 bg-gray-900 text-white text-xs rounded whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none">
        Click to use as ground truth
        <div class="absolute top-full right-2 w-0 h-0 border-l-2 border-r-2 border-t-2 border-transparent border-t-gray-900"></div>
      </div>
    </div>

    <div class="flex items-start justify-between">
      <div class="flex-1">
        <div v-if="!hideHeader" class="flex items-center space-x-2">
          <h3 class="font-medium text-foreground">
            {{ fieldName }}
          </h3>
          <FieldTypeIcon :type="fieldType" />
        </div>
        
        <div :class="{ 'mt-2': !hideHeader }">
          <!-- Show different content based on extraction state -->
          <div v-if="!extraction" class="flex flex-col items-center justify-center py-4 text-center">
            <div class="text-gray-400 mb-1">
              <svg class="h-8 w-8 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 5l7 7-7 7" />
              </svg>
            </div>
            <div class="text-sm text-gray-500 font-medium">Click "Run" above</div>
            <div class="text-xs text-gray-400">to extract this field</div>
          </div>
          
          <!-- Model Overloaded State -->
          <div v-else-if="isModelOverloaded" class="flex flex-col items-center justify-center py-4 text-center">
            <div class="text-orange-500 mb-2">
              <svg class="h-8 w-8 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <div class="text-sm text-orange-600 font-medium mb-1">Model Overloaded</div>
            <div class="text-xs text-gray-500">Click "Run" above to retry</div>
          </div>
          
          <!-- Running/Pending State -->
          <div v-else-if="isExtractionRunning" class="flex flex-col items-center justify-center py-4 text-center">
            <div class="text-blue-500 mb-2">
              <Loader2 class="h-8 w-8 mx-auto animate-spin" />
            </div>
            <div class="text-sm text-blue-600 font-medium mb-1">Extracting...</div>
            <div class="text-xs text-gray-500">Please wait while extraction is running</div>
          </div>
          
          <!-- Regular Error State -->
          <div v-else-if="(extraction.status === 'failed' || extraction.status === 'error') || (extractorStatus && extractorStatus[model] === 'error')" class="flex flex-col items-center justify-center py-4 text-center">
            <div class="text-red-500 mb-2">
              <svg class="h-8 w-8 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </div>
            <div class="text-sm text-red-600 font-medium mb-1">Extraction Failed</div>
            <div class="text-xs text-gray-500 mb-3">Click "Run" above to retry</div>
          </div>
          
          <!-- Success State with Value and Accuracy -->
          <div v-else-if="extraction && (extraction.status === 'completed' || (extractorStatus && extractorStatus[model] === 'success'))" class="flex items-center space-x-3">
            <!-- Extracted Value -->
            <div class="flex items-baseline space-x-2 flex-1">
              <span class="text-lg font-semibold text-foreground">
                {{ formatValue(extractedValue) }}
              </span>
            </div>
            
            <!-- Accuracy Badge -->
            <PercentBadge 
              v-if="accuracy !== null"
              :percentage="accuracy"
              class="flex-shrink-0"
            />
          </div>
          
          <!-- Default/Unknown State -->
          <div v-else-if="extraction" class="flex flex-col items-center justify-center py-4 text-center">
            <div class="text-gray-400 mb-1">
              <svg class="h-8 w-8 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div class="text-sm text-gray-500 font-medium">Status: {{ extraction.status }}</div>
            <div class="text-xs text-gray-400">Processing...</div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Additional metadata -->
    <div v-if="showMetadata" class="mt-3 pt-3 border-t border-border">
      <div class="grid grid-cols-2 gap-2 text-xs text-muted-foreground">
        <div>Type: {{ fieldType }}</div>
        <div v-if="accuracy !== null">
          Accuracy: {{ Math.round(accuracy) }}%
        </div>
      </div>
    </div>
  </Card>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Check, Loader2 } from 'lucide-vue-next'
import PercentBadge from './PercentBadge.vue'
import FieldTypeIcon from './FieldTypeIcon.vue'
import { Card } from '@/components/ui/card'
import { calculateCorrectness } from '@/utils/correctness'
import { useResourcePool } from '@/services/resourcePool.js'

interface Props {
  documentId: string
  fieldName: string
  fieldType: 'text' | 'number' | 'date' | 'boolean'
  model: string
  hideHeader?: boolean
  showMetadata?: boolean
  extractorStatus?: Record<string, 'idle' | 'loading' | 'success' | 'error'>
}

interface Emits {
  (e: 'hover', fieldName: string, element: HTMLElement): void
  (e: 'unhover'): void
  (e: 'click', fieldName: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const { extractions, groundTruths, upsertGroundTruth } = useResourcePool()

const extraction = computed(() => {
  return extractions.value.find(ext => 
    ext.documentId === props.documentId && 
    ext.model === props.model
  )
})

const extractedValue = computed(() => {
  return extraction.value?.data?.[props.fieldName]
})

const groundTruth = computed(() => {
  return groundTruths.value.find(gt => 
    gt.documentId === props.documentId && 
    gt.fieldName === props.fieldName
  )
})

const accuracy = computed(() => {
  // Only return null if we don't have an extraction at all or no ground truth
  if (extractedValue.value === undefined || !groundTruth.value) {
    return null
  }
  
  // The extractedValue is the direct value, not an object
  const actualExtractedValue = extractedValue.value
  const truthValue = groundTruth.value.userAnnotation ?? groundTruth.value.extractedValue
  
  // Only skip calculation if we don't have ground truth data
  // Allow null extracted values to be compared with ground truth
  if (truthValue === undefined) {
    return null
  }
  
  return calculateCorrectness(
    actualExtractedValue,
    truthValue,
    props.fieldType
  )
})

const isModelOverloaded = computed(() => {
  const error = extraction.value?.error
  return error && (error.includes('overloaded') || error.includes('503') || error.includes('UNAVAILABLE'))
})

const isExtractionRunning = computed(() => {
  // First check the passed extractorStatus for immediate UI feedback
  if (props.extractorStatus && props.extractorStatus[props.model] === 'loading') {
    return true
  }
  
  // Fallback to extraction status from the database/state
  return extraction.value?.status === 'running' || extraction.value?.status === 'pending'
})

const isHovered = ref(false)
const fieldElement = ref<HTMLElement>()

const formatValue = (value: any): string => {
  if (value === null || value === undefined) {
    return 'null'
  }
  if (typeof value === 'string') {
    const trimmed = value.trim().toLowerCase()
    if (['n/a', 'null', 'none', '', 'not available'].includes(trimmed)) {
      return 'null'
    }
    if (value.match(/^\d{4}-\d{2}-\d{2}/)) {
      return new Date(value).toLocaleDateString()
    }
  }
  if (typeof value === 'number') {
    return value.toLocaleString()
  }
  return String(value)
}

const onHover = () => {
  isHovered.value = true
  if (fieldElement.value) {
    emit('hover', props.fieldName, fieldElement.value)
  }
}

const onUnhover = () => {
  isHovered.value = false
  emit('unhover')
}

const isAnnotated = computed(() => {
  return groundTruth.value?.userAnnotation !== null || groundTruth.value?.markedNotExtractable;
});

const onClick = () => {
  if (extraction.value && extraction.value.status === 'completed') {
    const value = extraction.value.data[props.fieldName]
    const isNotExtractable = value === null
    upsertGroundTruth(props.documentId, props.fieldName, value, isNotExtractable)
  }
  emit('click', props.fieldName)
}
</script>

<template>
  <div class="flex flex-col items-center space-y-1">
    <!-- Circular Progress Indicator -->
    <div class="relative w-16 h-16">
      <!-- Background Circle -->
      <svg class="w-16 h-16 transform -rotate-90" viewBox="0 0 36 36">
        <path
          class="stroke-current text-muted/30"
          fill="none"
          stroke-width="3"
          d="M18 2.0845
            a 15.9155 15.9155 0 0 1 0 31.831
            a 15.9155 15.9155 0 0 1 0 -31.831"
        />
        <!-- Progress Circle -->
        <path
          class="stroke-current transition-all duration-300 ease-in-out"
          :class="getStrokeColorClass(percentage)"
          fill="none"
          stroke-width="3"
          stroke-linecap="round"
          :stroke-dasharray="`${circumference}, ${circumference}`"
          :stroke-dashoffset="circumference - (percentage / 100) * circumference"
          d="M18 2.0845
            a 15.9155 15.9155 0 0 1 0 31.831
            a 15.9155 15.9155 0 0 1 0 -31.831"
        />
      </svg>
      
      <!-- Percentage Text -->
      <div class="absolute inset-0 flex items-center justify-center">
        <span class="text-xs font-bold text-foreground">
          {{ Math.round(percentage) }}%
        </span>
      </div>
    </div>
    
    <!-- Stats Text -->
    <div class="text-xs text-muted-foreground text-center">
      <div class="font-medium">{{ correct }}/{{ total }}</div>
      <div>fields</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  percentage: number
  correct: number
  total: number
}

const props = withDefaults(defineProps<Props>(), {
  percentage: 0,
  correct: 0,
  total: 0
})

const circumference = computed(() => 2 * Math.PI * 15.9155)

const getStrokeColorClass = (percentage: number): string => {
  if (percentage >= 90) return 'text-green-500'
  if (percentage >= 80) return 'text-green-400'
  if (percentage >= 70) return 'text-yellow-500'
  if (percentage >= 60) return 'text-orange-500'
  if (percentage >= 50) return 'text-red-400'
  return 'text-red-500'
}
</script> 
<template>
  <div 
    class="grid gap-4 items-start py-4 border-b border-border"
    :style="{ gridTemplateColumns: `repeat(${props.extractionProviders.length + 1}, minmax(0, 1fr))` }"
  >
    <!-- Column 1: Field Name & Icon + Ground Truth -->
    <div class="col-span-1 sticky top-16">
      <div class="space-y-3">
        <!-- Field Header -->
        <div class="flex items-center space-x-2">
          <FieldTypeIcon :type="fieldType" />
          <h4 class="font-medium text-foreground">
            {{ field.name }}
          </h4>
        </div>
        
        <!-- Ground Truth Input Component -->
        <GroundTruthInput
          :field-name="field.name"
          :field-type="fieldType"
          :ground-truth-data="groundTruthData[field.name]"
          @update-annotation="handleAnnotationUpdate"
          @remove-annotation="handleAnnotationRemoval"
        />
      </div>
    </div>

    <!-- Columns 2+: Provider Results -->
    <div v-for="provider in extractionProviders" :key="provider.id" class="col-span-1">
      <div class="space-y-3">
        <!-- Empty space to align with field header -->
        <div class="h-6"></div>
        
        <!-- Extraction field aligned with ground truth input -->
        <div class="space-y-2">
          <ExtractionFieldCard
            :document-id="documentId"
            :field-name="field.name"
            :field-type="fieldType"
            :model="provider.id"
            :hide-header="true"
            :extractor-status="extractorStatus"
            @hover="$emit('hover', $event)"
            @unhover="$emit('unhover')"
            @click="handleExtractedValueClick(field.name, provider.id)"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useResourcePool } from '@/services/resourcePool.js'
import ExtractionFieldCard from './ExtractionFieldCard.vue'
import FieldTypeIcon from './FieldTypeIcon.vue'
import GroundTruthInput from './GroundTruthInput.vue'

interface FieldComparison {
  name: string
  results: Record<string, any>
}

interface Provider {
  id: string
  name: string
}

interface Props {
  field: FieldComparison
  extractionProviders: Provider[]
  documentId: string
  extractorStatus?: Record<string, 'idle' | 'loading' | 'success' | 'error'>
}

interface Emits {
  (e: 'updateAnnotation', fieldName: string, value: any, isNotExtractable?: boolean): void
  (e: 'removeAnnotation', fieldName: string): void
  (e: 'hover', fieldName: string): void
  (e: 'unhover'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const { extractions, groundTruths } = useResourcePool()

const fieldType = computed(() => {
  const firstProviderId = props.extractionProviders[0]?.id
  if (!firstProviderId) return 'text'
  const result = props.field.results[firstProviderId]
  return result?.type || 'text'
})

const groundTruthData = computed(() => {
  const data: Record<string, any> = {}
  groundTruths.value
    .filter(gt => gt.documentId === props.documentId)
    .forEach(gt => {
      data[gt.fieldName] = {
        extractedValue: gt.extractedValue,
        userAnnotation: gt.userAnnotation,
        markedNotExtractable: gt.markedNotExtractable
      }
    })
  return data
})

const handleExtractedValueClick = (fieldName: string, model: string) => {
  const extraction = extractions.value.find(ext => 
    ext.documentId === props.documentId && 
    ext.model === model
  )
  const value = extraction?.data?.[fieldName]?.value

  if (value !== undefined) {
    const isNullEquivalent = (val: any): boolean => {
      if (val === null || val === undefined) return true
      if (typeof val === 'string') {
        const trimmed = val.trim().toLowerCase()
        return ['null', 'n/a', 'none', '', 'not available'].includes(trimmed)
      }
      return false
    }
    
    if (isNullEquivalent(value)) {
      emit('updateAnnotation', fieldName, null)
    } else {
      emit('updateAnnotation', fieldName, value)
    }
  }
}

const handleAnnotationUpdate = (fieldName: string, value: any, isNotExtractable?: boolean) => {
  emit('updateAnnotation', fieldName, value, isNotExtractable)
}

const handleAnnotationRemoval = (fieldName: string) => {
  emit('removeAnnotation', fieldName)
}
</script>
 
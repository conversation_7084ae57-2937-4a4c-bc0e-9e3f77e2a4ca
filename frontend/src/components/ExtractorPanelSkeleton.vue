<template>
  <div class="flex flex-col items-center gap-1">
    <!-- Horizontal Layout: Logo, PercentBadge, Button -->
    <div class="flex items-center gap-2">
      <!-- Extractor Logo Skeleton -->
      <div class="w-4 flex justify-center">
        <Skeleton class="w-4 h-4 rounded" />
      </div>
      
      <!-- Accuracy Badge Skeleton -->
      <div class="w-20 flex justify-center">
        <Skeleton class="w-12 h-5 rounded" />
      </div>
      
      <!-- Extraction Button Skeleton -->
      <div class="w-5 flex justify-center">
        <Skeleton class="w-5 h-5 rounded-md" />
      </div>
    </div>
    
    <!-- Progress Bar Skeleton -->
    <div class="w-full mt-1">
      <Skeleton class="w-full h-1 rounded-full" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { Skeleton } from '@/components/ui/skeleton'
</script>
</template>
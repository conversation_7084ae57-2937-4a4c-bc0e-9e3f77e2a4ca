<template>
  <div class="flex flex-col items-center gap-1">
    <!-- Check if any method has ground truth -->
    <div v-if="hasAnyGroundTruth">
      <!-- Accuracy chips for each method -->
      <div v-for="(accuracy, method) in methodAccuracies" :key="method" class="flex items-center gap-2">
        <!-- Extractor Logo -->
        <ExtractorLogo :extractor="method" :size="16" />
        
        <!-- Accuracy Chip -->
        <div 
          v-if="accuracy.hasGroundTruth"
          :class="{
            'bg-green-100 text-green-700 border-green-200': accuracy.percentage >= 80,
            'bg-yellow-100 text-yellow-700 border-yellow-200': accuracy.percentage >= 60 && accuracy.percentage < 80,
            'bg-red-100 text-red-700 border-red-200': accuracy.percentage < 60,
          }"
          class="px-2 py-0.5 text-xs font-medium rounded border flex items-center gap-1"
        >
          <span>{{ accuracy.correct }}/{{ accuracy.total }}</span>
          <span class="text-xs opacity-75">({{ Math.round(accuracy.percentage) }}%)</span>
        </div>
      </div>
    </div>
    
    <!-- Single "No Ground Truth" indicator when no ground truth available -->
    <div 
      v-else-if="Object.keys(methodAccuracies).length > 0"
      class="px-2 py-0.5 text-xs font-medium rounded border bg-gray-100 text-gray-500 border-gray-200"
    >
      <span>No Ground Truth</span>
    </div>
    
    <!-- Show "No extractions" if no methods found -->
    <div 
      v-else
      class="px-2 py-0.5 text-xs font-medium rounded border bg-gray-100 text-gray-400 border-gray-200"
    >
      No extractions
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import ExtractorLogo from './ExtractorLogo.vue';

interface Props {
  extractions: {
    method: string;
    extracted_data: Record<string, any>;
  }[];
  groundTruth: Record<string, any>;
  configFields?: Record<string, { field_type: string }>;
}

const props = defineProps<Props>();

const methodAccuracies = computed(() => {
  const accuracies: Record<string, {
    correct: number;
    total: number;
    percentage: number;
    hasGroundTruth: boolean;
  }> = {};

  // Group extractions by method
  const extractionsByMethod: Record<string, any> = {};
  props.extractions.forEach(extraction => {
    extractionsByMethod[extraction.method] = extraction.extracted_data;
  });

  // Calculate accuracy for each method
  Object.entries(extractionsByMethod).forEach(([method, extractedData]) => {
    let correct = 0;
    let total = 0;
    let hasAnyGroundTruth = false;

    // Compare each field with ground truth
    Object.entries(props.groundTruth).forEach(([fieldName, groundTruthValue]) => {
      if (groundTruthValue !== null && groundTruthValue !== undefined && groundTruthValue !== '') {
        hasAnyGroundTruth = true;
        total++;

        const extractedValue = extractedData[fieldName];
        if (extractedValue !== null && extractedValue !== undefined) {
          // Get field type for comparison
          const fieldType = props.configFields?.[fieldName]?.field_type || 'text';
          
          // Calculate correctness based on field type
          const isCorrect = calculateFieldCorrectness(extractedValue, groundTruthValue, fieldType);
          if (isCorrect) {
            correct++;
          }
        }
      }
    });

    accuracies[method] = {
      correct,
      total,
      percentage: total > 0 ? (correct / total) * 100 : 0,
      hasGroundTruth: hasAnyGroundTruth
    };
  });

  return accuracies;
});

const hasAnyGroundTruth = computed(() => {
  return Object.values(methodAccuracies.value).some(accuracy => accuracy.hasGroundTruth);
});

const calculateFieldCorrectness = (extracted: any, groundTruth: any, fieldType: string): boolean => {
  if (extracted === null || extracted === undefined || extracted === 'N/A') {
    return false;
  }

  switch (fieldType) {
    case 'number':
      const extractedNum = parseFloat(String(extracted).replace(/[^\d.-]/g, ''));
      const groundTruthNum = parseFloat(String(groundTruth).replace(/[^\d.-]/g, ''));
      if (isNaN(extractedNum) || isNaN(groundTruthNum)) return false;
      // Allow 5% tolerance for numbers
      const tolerance = Math.abs(groundTruthNum) * 0.05;
      return Math.abs(extractedNum - groundTruthNum) <= tolerance;
      
    case 'date':
      try {
        const extractedDate = new Date(extracted).toISOString().split('T')[0];
        const groundTruthDate = new Date(groundTruth).toISOString().split('T')[0];
        return extractedDate === groundTruthDate;
      } catch {
        return false;
      }
      
    case 'boolean':
      return Boolean(extracted) === Boolean(groundTruth);
      
    default: // text and others
      const extractedStr = String(extracted).toLowerCase().trim();
      const groundTruthStr = String(groundTruth).toLowerCase().trim();
      
      // Exact match
      if (extractedStr === groundTruthStr) return true;
      
      // Allow for minor differences (fuzzy matching)
      if (extractedStr.length > 3 && groundTruthStr.length > 3) {
        const similarity = calculateStringSimilarity(extractedStr, groundTruthStr);
        return similarity >= 0.8; // 80% similarity threshold
      }
      
      return false;
  }
};

const calculateStringSimilarity = (str1: string, str2: string): number => {
  const longer = str1.length > str2.length ? str1 : str2;
  const shorter = str1.length > str2.length ? str2 : str1;
  
  if (longer.length === 0) return 1.0;
  
  const editDistance = levenshteinDistance(longer, shorter);
  return (longer.length - editDistance) / longer.length;
};

const levenshteinDistance = (str1: string, str2: string): number => {
  const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));
  
  for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
  for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;
  
  for (let j = 1; j <= str2.length; j++) {
    for (let i = 1; i <= str1.length; i++) {
      const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
      matrix[j][i] = Math.min(
        matrix[j][i - 1] + 1,     // deletion
        matrix[j - 1][i] + 1,     // insertion
        matrix[j - 1][i - 1] + indicator  // substitution
      );
    }
  }
  
  return matrix[str2.length][str1.length];
};
</script> 
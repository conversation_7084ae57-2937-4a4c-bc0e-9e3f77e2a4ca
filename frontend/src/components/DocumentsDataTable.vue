<template>
  <div class="space-y-4">
    <!-- Header with actions -->
    <div class="flex items-center justify-between">
      <div>
        <h2 class="text-xl font-semibold text-foreground">Documents</h2>
        <p class="text-sm text-muted-foreground mt-1">
          {{ documents.length }} document{{ documents.length !== 1 ? 's' : '' }} found
          <span v-if="extractedCount > 0" class="ml-2 text-green-600">
            ({{ extractedCount }} extracted)
          </span>
          <span v-if="approvalSummary.approved > 0" class="ml-2 text-green-600">
            ({{ approvalSummary.approved }} approved)
          </span>
          <span v-if="approvalSummary.pending > 0" class="ml-2 text-yellow-600">
            ({{ approvalSummary.pending }} pending)
          </span>
        </p>
      </div>
      <div class="flex items-center gap-3">
        <div v-if="isLoadingDocs" class="flex items-center gap-2 text-sm text-muted-foreground">
          <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
          Loading documents...
        </div>
        <div v-if="documents.length > 0 && config" class="flex items-center gap-2">
          <button
            @click="runBatchExtraction(false)"
            :disabled="isBatchExtracting || extractionTasksToStart === 0"
            class="px-3 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors flex items-center gap-2"
          >
            <Play class="h-4 w-4" />
            Run Missing & Failed ({{ extractionTasksToStart }})
          </button>
          <button
            @click="runBatchExtraction(true)"
            :disabled="isBatchExtracting"
            class="px-3 py-2 text-sm bg-gray-600 text-white rounded-md hover:bg-gray-700 disabled:opacity-50 transition-colors flex items-center gap-2"
          >
            <Play class="h-4 w-4" />
            Run All
          </button>
        </div>
      </div>
    </div>

    <!-- Search Input -->
    <div class="mt-4">
      <Input
        v-model="searchQuery"
        placeholder="Search by filename..."
        class="max-w-sm"
      />
    </div>

    <!-- Empty state -->
    <div v-if="documents.length === 0 && !isLoadingDocs" class="text-center py-12 bg-muted/50 rounded-lg border-2 border-dashed border-border">
      <FileText class="h-12 w-12 text-muted-foreground mx-auto mb-4 opacity-50" />
      <p class="text-muted-foreground">No documents found</p>
      <p class="text-sm text-muted-foreground mt-1">Check your SQL query to ensure it returns document paths</p>
    </div>

    <!-- Data Table -->
    <div v-else class="border border-border rounded-lg overflow-x-auto">
      <Table class="table-auto">
        <TableHeader>
          <TableRow>
            <TableHead class="w-auto min-w-64">Filename</TableHead>
            <TableHead class="w-16">Type</TableHead>
            <TableHead class="w-20">Size</TableHead>
            <TableHead class="w-32">Status</TableHead>
            <TableHead v-for="extractor in effectiveExtractors" :key="extractor" class="w-32">
              <div class="flex items-center justify-between">
                <span class="truncate">{{ extractor }}</span>
                <button
                  @click.stop="runExtractorForAllDocuments(extractor)"
                  :disabled="!config || isExtractorRunning(extractor)"
                  class="ml-2 p-1 rounded-md hover:bg-muted transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex-shrink-0"
                  :title="`Rerun ${extractor} for all documents`"
                >
                  <div v-if="isExtractorRunning(extractor)" class="animate-spin rounded-full h-3 w-3 border-b-2 border-current"></div>
                  <RotateCcw v-else class="h-3 w-3" />
                </button>
              </div>
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow 
            v-for="doc in documents" 
            :key="doc.id"
            class="cursor-pointer"
            @click="viewDocument(doc.id, datasetId)"
          >
            <TableCell class="min-w-64">
              <div class="flex items-center gap-3">
                <FileText class="h-4 w-4 text-muted-foreground flex-shrink-0" />
                <div class="min-w-0">
                  <div class="font-mono text-sm font-medium text-foreground truncate">
                    {{ getFilename(doc.gcsPath) }}
                  </div>
                  <div class="text-xs text-muted-foreground truncate">
                    {{ doc.gcsPath }}
                  </div>
                </div>
              </div>
            </TableCell>
            <TableCell class="w-16">
              <div class="flex items-center gap-2">
                <span class="px-2 py-1 bg-muted text-muted-foreground rounded text-xs font-medium">
                  {{ getFileExtension(doc.gcsPath) }}
                </span>
              </div>
            </TableCell>
            <TableCell class="w-20">
              <span class="text-sm text-muted-foreground">
                {{ filesize(doc.size) }}
              </span>
            </TableCell>
            <TableCell class="w-32">
              <div class="flex items-center gap-2">
                <!-- Document Status Badge -->
                <DocumentStatusBadge :status="getDocumentStatus(doc.id)" />
              </div>
            </TableCell>
            <TableCell v-for="extractor in effectiveExtractors" :key="extractor" class="w-32">
              <div class="flex justify-start items-center gap-2">
                <ExtractionStatusBadge :status="getExtractionStatus(doc.id, extractor)" />
                <PercentBadge 
                  v-if="getExtractionStatus(doc.id, extractor) === 'completed' && getExtractorAccuracy(doc.id, extractor) != null && !isNaN(getExtractorAccuracy(doc.id, extractor))"
                  :percentage="getExtractorAccuracy(doc.id, extractor)" 
                />
              </div>
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useRouter } from 'vue-router';
import { FileText, Play, RotateCcw } from 'lucide-vue-next';
import { Table, TableHeader, TableBody, TableRow, TableHead, TableCell } from '@/components/ui/table';
import { Input } from '@/components/ui/input';
import PercentBadge from './PercentBadge.vue';
import ExtractionStatusBadge from './ExtractionStatusBadge.vue';
import DocumentStatusBadge from './DocumentStatusBadge.vue';
import { useResourcePool } from '../services/resourcePool.js';
import { filesize } from 'filesize';

interface Document {
  id: string;
  gcsPath: string;
  siteName?: string;
  objectType?: string;
  emissionsLogId?: string;
  isApproved?: boolean | null;
  size?: number;
}

interface ExtractionConfig {
  id: string;
  data: {
    extractors?: Array<{ model: string }>;
  };
}

interface Props {
  documents: Document[];
  config: ExtractionConfig | null;
  availableExtractors: string[];
  extractingDocs: Set<string>;
  datasetId: string;
  isLoadingDocs: boolean;
  extractedCount: number;
  extractionTasksToStart: number;
  approvalSummary: {
    approved: number;
    pending: number;
    notReviewed: number;
  };
  runBatchExtraction: (includeCompleted: boolean) => void;
  getApprovalStatusText: (docId: string) => string;
  getDocumentAccuracies: (docId: string) => Array<{ extractor: string; accuracy: number }>;
  hasExtractorRun: (docId: string, extractor: string) => boolean;
  getExtractionStatus: (docId: string, extractor: string) => 'pending' | 'running' | 'completed' | 'failed' | null;
}

const props = defineProps<Props>();
const router = useRouter();
const { runExtractions, documents: allDocuments, groundTruths } = useResourcePool();

const searchQuery = ref('');

// Computed property for effective extractors
const effectiveExtractors = computed(() => {
  const backendExtractors = props.availableExtractors || [];
  const configExtractors = props.config?.data?.extractors?.map(e => e.model) || [];
  
  // Use config extractors if available, otherwise fall back to backend extractors
  return configExtractors.length > 0 ? configExtractors : backendExtractors;
});

const isBatchExtracting = computed(() => {
  return Array.from(props.extractingDocs).some(key => key.includes('-batch-'));
});

// Helper functions
const getFilename = (path: string | null | undefined): string => {
  if (!path) return 'Unknown File';
  return path.split('/').pop() || path;
};

const getFileExtension = (path: string | null | undefined): string => {
  if (!path) return 'UNKNOWN';
  const filename = getFilename(path);
  const ext = filename.split('.').pop()?.toUpperCase();
  return ext || 'UNKNOWN';
};

const getExtractorAccuracy = (docId: string, extractor: string): number | null => {
  const accuracies = props.getDocumentAccuracies(docId);
  const extractorAccuracy = accuracies.find(acc => acc.extractor === extractor);
  if (extractorAccuracy && typeof extractorAccuracy.accuracy === 'number' && !isNaN(extractorAccuracy.accuracy)) {
    return extractorAccuracy.accuracy;
  }
  return null;
};

const viewDocument = (docId: string, datasetId: string) => {
  router.push(`/datasets/${datasetId}/documents/${docId}`);
};

// Track running extractors
const runningExtractors = computed(() => {
  const running = new Set<string>();
  for (const key of props.extractingDocs) {
    if (key.includes('-extractor-')) {
      const extractor = key.split('-extractor-')[1];
      running.add(extractor);
    }
  }
  return running;
});

const isExtractorRunning = (extractor: string): boolean => {
  return runningExtractors.value.has(extractor);
};

const runExtractorForAllDocuments = async (extractor: string) => {
  if (!props.config?.id) {
    console.error('No extraction config available');
    return;
  }

  try {
    console.log(`🚀 Starting ${extractor} extraction for all ${props.documents.length} documents`);
    
    // Get all document IDs
    const documentIds = props.documents.map(doc => doc.id);
    
    // Run extractions for this specific model
    await runExtractions(documentIds, props.config.id, [extractor]);
    
    console.log(`✅ ${extractor} extraction started for all documents`);
  } catch (error) {
    console.error(`❌ Failed to start ${extractor} extraction for all documents:`, error);
  }
};

// Document status function using resourcePool data
const getDocumentStatus = (docId: string): 'approved' | 'unapproved' | 'needs-ground-truth' | 'pending-review' | 'unknown' => {
  const doc = allDocuments.value.find(d => d.id === docId);
  const hasGT = groundTruths.value.some(gt => gt.documentId === docId);
  
  if (!doc) return 'unknown';
  
  // Priority: Approval status first, then ground truth status
  if (doc.isApproved === true) {
    return 'approved';
  } else if (doc.isApproved === false) {
    return 'unapproved';
  } else if (!hasGT) {
    return 'needs-ground-truth';
  } else {
    return 'pending-review';
  }
};
</script>
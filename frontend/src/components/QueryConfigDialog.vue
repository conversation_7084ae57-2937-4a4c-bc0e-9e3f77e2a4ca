<template>
  <div v-if="isOpen" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg w-full max-w-4xl mx-4 max-h-[90vh] flex flex-col">
      <div class="p-6 border-b">
        <h2 class="text-xl font-semibold">Configure Dataset Query</h2>
      </div>
      
      <div class="p-6 overflow-y-auto flex-grow">
        <!-- Step 1: Site Selection -->
        <div v-if="currentStep === 1" class="space-y-4">
          <h3 class="text-lg font-medium">Step 1: Select Site</h3>
          <div v-if="isLoadingSites" class="text-center py-8">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p class="text-gray-600">Loading sites...</p>
          </div>
          <div v-else-if="sites.length === 0" class="text-center py-8 text-gray-500">
            No sites available
          </div>
          <div v-else>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead @click="sortBy('name')" class="cursor-pointer">
                    Name
                    <span v-if="sortKey === 'name'">{{ sortOrder === 'asc' ? ' ▲' : ' ▼' }}</span>
                  </TableHead>
                  <TableHead @click="sortBy('stepExecutions')" class="cursor-pointer">
                    Step Executions
                    <span v-if="sortKey === 'stepExecutions'">{{ sortOrder === 'asc' ? ' ▲' : ' ▼' }}</span>
                  </TableHead>
                  <TableHead @click="sortBy('deliveries')" class="cursor-pointer">
                    Deliveries
                    <span v-if="sortKey === 'deliveries'">{{ sortOrder === 'asc' ? ' ▲' : ' ▼' }}</span>
                  </TableHead>
                  <TableHead @click="sortBy('emissionLogs')" class="cursor-pointer">
                    Emission Logs
                    <span v-if="sortKey === 'emissionLogs'">{{ sortOrder === 'asc' ? ' ▲' : ' ▼' }}</span>
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow 
                  v-for="site in sortedSites" 
                  :key="site.id" 
                  @click="selectedSite = site"
                  class="cursor-pointer"
                  :class="{ 'bg-blue-50': selectedSite?.id === site.id }"
                >
                  <TableCell>{{ site.name }}</TableCell>
                  <TableCell>{{ site.eventCounts.stepExecutions }}</TableCell>
                  <TableCell>{{ site.eventCounts.deliveries }}</TableCell>
                  <TableCell>{{ site.eventCounts.emissionLogs }}</TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>
        </div>

        <!-- Step 2: Event Type Selection -->
        <div v-if="currentStep === 2" class="space-y-4">
          <h3 class="text-lg font-medium">Step 2: Select Event Type</h3>
          <div class="grid gap-4">
            <div v-if="isLoadingEventTypes" class="text-center py-8">
              <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p class="text-gray-600">Loading event types...</p>
            </div>
            <div v-else-if="eventTypes.length === 0" class="text-center py-8 text-gray-500">
              No event types available for this site
            </div>
            <div v-else class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div
                v-for="eventType in eventTypes"
                :key="eventType.type"
                @click="selectedEventType = eventType"
                class="relative rounded-lg p-4 cursor-pointer transition-all"
                :class="{
                  'border-blue-600 bg-blue-50 ring-2 ring-blue-500': selectedEventType?.type === eventType.type,
                  'border-gray-200 hover:border-gray-300': selectedEventType?.type !== eventType.type,
                }"
              >
                <div class="flex justify-between items-start">
                  <div class="flex-1">
                    <h4 class="font-semibold">{{ eventType.displayName }}</h4>
                    <p class="text-sm text-gray-600 mt-1">
                      {{ eventType.configCount }} {{ eventType.type === 'step_execution' ? 'configurations' : 'items' }} available
                      <span v-if="eventType.fileCount" class="text-gray-500">• {{ eventType.fileCount }} files</span>
                    </p>
                  </div>
                  <div v-if="selectedEventType?.type === eventType.type" class="ml-4 flex-shrink-0">
                    <div class="bg-blue-600 text-white rounded-full h-6 w-6 flex items-center justify-center">
                      <Check class="h-4 w-4" />
                    </div>
                  </div>
                </div>
                <div class="mt-2">
                  <p class="text-xs text-gray-500 mb-1">Sample configurations:</p>
                  <div class="space-y-1">
                    <div v-for="config in eventType.sampleConfigs" :key="config.id" class="text-xs text-gray-600">
                      • {{ config.name }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Step 3: Step Execution Type Selection (only for step_execution event type) -->
        <div v-if="currentStep === 3 && selectedEventType?.type === 'step_execution'" class="space-y-4">
          <h3 class="text-lg font-medium">Step 3: Select Step Execution Types</h3>
          <div v-if="isLoadingStepExecutionTypes" class="text-center py-8">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p class="text-gray-600">Loading step execution types...</p>
          </div>
          <div v-else-if="stepExecutionTypes.length === 0" class="text-center py-8 text-gray-500">
            No step execution types available for this site
          </div>
          <div v-else class="grid gap-3">
            <div 
              v-for="stepType in stepExecutionTypes" 
              :key="stepType.type"
              class="border rounded-lg p-4 cursor-pointer transition-colors"
              :class="{
                'border-blue-500 bg-blue-50': selectedStepExecutionTypes.includes(stepType),
                'border-gray-200 hover:border-gray-300': !selectedStepExecutionTypes.includes(stepType)
              }"
              @click="toggleStepExecutionType(stepType)"
            >
              <div class="flex items-center justify-between">
                <div>
                  <h4 class="font-semibold">{{ stepType.displayName }}</h4>
                  <p class="text-sm text-gray-600 mt-1">{{ stepType.count }} executions</p>
                </div>
                <input
                  type="checkbox"
                  :checked="selectedStepExecutionTypes.includes(stepType)"
                  class="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  @click.stop
                  @change="toggleStepExecutionType(stepType)"
                >
              </div>
            </div>
          </div>
          <div v-if="selectedStepExecutionTypes.length > 0" class="mt-4 p-3 bg-blue-50 rounded-lg">
            <p class="text-sm text-blue-800">
              Selected {{ selectedStepExecutionTypes.length }} step execution type{{ selectedStepExecutionTypes.length !== 1 ? 's' : '' }}
            </p>
          </div>
        </div>

        <!-- Step 4: Field Selection (or Step 3 for non-step_execution types) -->
        <div v-if="currentStep === 4 || (currentStep === 3 && selectedEventType?.type !== 'step_execution')" class="space-y-4">
          <h3 class="text-lg font-medium">Step {{ selectedEventType?.type === 'step_execution' ? '4' : '3' }}: Select Fields</h3>
          <div v-if="isLoadingFields" class="text-center py-8">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p class="text-gray-600">Loading available fields...</p>
          </div>
          <div v-else class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div v-for="(fields, category) in availableFields" :key="category" class="border rounded-lg p-4">
              <h4 class="font-semibold capitalize mb-3 flex items-center justify-between">
                {{ category }}
                <span class="text-sm font-normal text-gray-500">({{ fields.length }})</span>
              </h4>
              <div class="space-y-2 max-h-64 overflow-y-auto">
                <div v-for="field in fields" :key="field.name" class="flex items-start space-x-2">
                  <input 
                    type="checkbox" 
                    :id="`field-${field.name}`" 
                    :value="field" 
                    v-model="selectedFields" 
                    class="mt-1 h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  >
                  <label :for="`field-${field.name}`" class="flex-1 text-sm">
                    <div class="font-medium">{{ field.name }}</div>
                    <div class="text-gray-500 text-xs">{{ field.description }}</div>
                    <div class="text-gray-400 text-xs">
                      {{ field.fieldType }} • {{ field.frequency }} occurrences
                    </div>
                  </label>
                </div>
              </div>
            </div>
          </div>
          <div v-if="selectedFields.length > 0" class="mt-4 p-3 bg-blue-50 rounded-lg">
            <p class="text-sm text-blue-800">
              Selected {{ selectedFields.length }} field{{ selectedFields.length !== 1 ? 's' : '' }}
            </p>
          </div>
        </div>

        <!-- Step 5: Query Preview (or Step 4 for non-step_execution types) -->
        <div v-if="currentStep === maxSteps" class="space-y-4">
          <h3 class="text-lg font-medium">Step {{ selectedEventType?.type === 'step_execution' ? '5' : '4' }}: Review & Generate Query</h3>
          
          <div v-if="isGeneratingQuery" class="text-center py-8">
            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto mb-2"></div>
            <p class="text-sm text-gray-600">Generating query...</p>
          </div>
          
          <QueryConfigPreview
            v-else
            :query="generatedQuery"
            :fields="selectedFields"
          />
        </div>
      </div>

      <!-- Navigation -->
      <div class="flex justify-between items-center p-6 border-t">
        <button
            @click="cancel"
            class="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
          >
            Cancel
          </button>

        <div class="flex items-center space-x-2">
          <!-- Step indicators -->
          <div class="flex space-x-2">
            <div 
              v-for="step in maxSteps" 
              :key="step"
              class="w-2 h-2 rounded-full"
              :class="{
                'bg-blue-600': step <= currentStep,
                'bg-gray-300': step > currentStep
              }"
            ></div>
          </div>
        </div>

        <div class="flex space-x-3">
          <button
            v-if="currentStep > 1"
            @click="previousStep"
            class="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
          >
            Previous
          </button>
          <button
            v-if="currentStep < maxSteps"
            @click="nextStep"
            :disabled="!canProceed"
            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Next
          </button>
          <button
            v-else
            @click="confirm"
            :disabled="!generatedQuery"
            class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Use Query
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { trpc } from '../lib/trpc.js';
import { Check } from 'lucide-vue-next';
import QueryConfigPreview from './QueryConfigPreview.vue';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

interface SiteOption {
  id: string;
  name: string;
  eventCounts: {
    stepExecutions: number;
    deliveries: number;
    emissionLogs: number;
  };
}

interface EventTypeConfig {
  type: 'step_execution' | 'delivery' | 'emission_log';
  displayName: string;
  configCount: number;
  fileCount?: number;
  sampleConfigs: Array<{
    id: string;
    name: string;
    description?: string;
  }>;
}

interface StepExecutionType {
  type: string;
  displayName: string;
  count: number;
}

export interface FieldOption {
  name: string;
  description: string;
  fieldType: string;
  category: string;
  frequency: number;
}

interface Props {
  isOpen: boolean;
}

interface Emits {
  (e: 'close'): void;
  (e: 'confirm', data: {
    query: string;
    selectedFields: FieldOption[];
    siteId: string;
    eventType: string;
  }): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// State
const currentStep = ref(1);
const sites = ref<SiteOption[]>([]);
const eventTypes = ref<EventTypeConfig[]>([]);
const stepExecutionTypes = ref<StepExecutionType[]>([]);
const availableFields = ref<{
  metadata: FieldOption[];
  inputs: FieldOption[];
  datapoints: FieldOption[];
}>({ metadata: [], inputs: [], datapoints: [] });

const selectedSite = ref<SiteOption | null>(null);
const selectedEventType = ref<EventTypeConfig | null>(null);
const selectedStepExecutionTypes = ref<StepExecutionType[]>([]);
const selectedFields = ref<FieldOption[]>([]);
const generatedQuery = ref('');

// Loading states
const isLoadingSites = ref(false);
const isLoadingEventTypes = ref(false);
const isLoadingStepExecutionTypes = ref(false);
const isLoadingFields = ref(false);
const isGeneratingQuery = ref(false);

// Sorting state
const sortKey = ref<keyof SiteOption | 'stepExecutions' | 'deliveries' | 'emissionLogs'>('stepExecutions');
const sortOrder = ref<'asc' | 'desc'>('desc');

// Computed
const maxSteps = computed(() => {
  if (!selectedEventType.value) return 4; // Default before selection
  return selectedEventType.value.type === 'step_execution' ? 5 : 4;
});


const canProceed = computed(() => {
  switch (currentStep.value) {
    case 1: return selectedSite.value !== null;
    case 2: return selectedEventType.value !== null;
    case 3: 
      if (selectedEventType.value?.type === 'step_execution') {
        return selectedStepExecutionTypes.value.length > 0;
      }
      return selectedFields.value.length > 0;
    case 4:
      if (selectedEventType.value?.type === 'step_execution') {
        return selectedFields.value.length > 0;
      }
       return generatedQuery.value !== '';
    case 5: return generatedQuery.value !== '';
    default: return false;
  }
});

const sortedSites = computed(() => {
  return [...sites.value].sort((a, b) => {
    let aValue, bValue;

    if (sortKey.value === 'name') {
      aValue = a.name;
      bValue = b.name;
    } else {
      aValue = a.eventCounts[sortKey.value as keyof typeof a.eventCounts];
      bValue = b.eventCounts[sortKey.value as keyof typeof b.eventCounts];
    }

    if (aValue < bValue) return sortOrder.value === 'asc' ? -1 : 1;
    if (aValue > bValue) return sortOrder.value === 'asc' ? 1 : -1;
    return 0;
  });
});

// Methods
const sortBy = (key: keyof SiteOption | 'stepExecutions' | 'deliveries' | 'emissionLogs') => {
  if (sortKey.value === key) {
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc';
  } else {
    sortKey.value = key;
    sortOrder.value = 'asc';
  }
};

const loadSites = async () => {
  isLoadingSites.value = true;
  try {
    sites.value = await trpc.resources.queryConfig.getSites.query();
  } catch (error) {
    console.error('Failed to load sites:', error);
  } finally {
    isLoadingSites.value = false;
  }
};

const loadEventTypes = async () => {
  if (!selectedSite.value) return;
  
  isLoadingEventTypes.value = true;
  try {
    eventTypes.value = await trpc.resources.queryConfig.getEventTypes.query({
      siteId: selectedSite.value.id
    });
  } catch (error) {
    console.error('Failed to load event types:', error);
  } finally {
    isLoadingEventTypes.value = false;
  }
};

const loadStepExecutionTypes = async () => {
  if (!selectedSite.value || selectedEventType.value?.type !== 'step_execution') return;
  
  isLoadingStepExecutionTypes.value = true;
  try {
    stepExecutionTypes.value = await trpc.resources.queryConfig.getStepExecutionTypes.query({
      siteId: selectedSite.value.id
    });
  } catch (error) {
    console.error('Failed to load step execution types:', error);
  } finally {
    isLoadingStepExecutionTypes.value = false;
  }
};

const loadFields = async () => {
  if (!selectedSite.value || !selectedEventType.value) return;
  
  isLoadingFields.value = true;
  try {
    availableFields.value = await trpc.resources.queryConfig.getFieldOptions.query({
      siteId: selectedSite.value.id,
      eventType: selectedEventType.value.type
    });
  } catch (error) {
    console.error('Failed to load fields:', error);
  } finally {
    isLoadingFields.value = false;
  }
};

const generateQuery = async () => {
  if (!selectedSite.value || !selectedEventType.value) return;
  
  isGeneratingQuery.value = true;
  try {
    const result = await trpc.resources.queryConfig.generateQuery.mutate({
      siteId: selectedSite.value.id,
      eventType: selectedEventType.value.type
    });
    generatedQuery.value = result.query;
  } catch (error) {
    console.error('Failed to generate query:', error);
  } finally {
    isGeneratingQuery.value = false;
  }
};

const nextStep = async () => {
  if (!canProceed.value) return;

  const at = (step: number) => {
    if (selectedEventType.value?.type !== 'step_execution') {
      return step - 1;
    }
    return step;
  }
  
  if (currentStep.value === at(2)) {
    if (selectedEventType.value?.type === 'step_execution') {
      await loadStepExecutionTypes();
    } else {
      await loadFields();
    }
  } else if (currentStep.value === at(3)) {
    await loadFields();
  } else if (currentStep.value === at(4)) {
    await generateQuery();
  }
  
  currentStep.value++;
};

const previousStep = () => {
  if (currentStep.value > 1) {
    currentStep.value--;
  }
};

const toggleStepExecutionType = (stepType: StepExecutionType) => {
  const index = selectedStepExecutionTypes.value.findIndex(t => t.type === stepType.type);
  if (index > -1) {
    selectedStepExecutionTypes.value.splice(index, 1);
  } else {
    selectedStepExecutionTypes.value.push(stepType);
  }
};

const cancel = () => {
  // Reset state
  currentStep.value = 1;
  selectedSite.value = null;
  selectedEventType.value = null;
  selectedStepExecutionTypes.value = [];
  selectedFields.value = [];
  generatedQuery.value = '';
  emit('close');
};

const confirm = () => {
  if (!selectedSite.value || !selectedEventType.value || !generatedQuery.value) return;
  
  emit('confirm', {
    query: generatedQuery.value,
    selectedFields: selectedFields.value,
    siteId: selectedSite.value.id,
    eventType: selectedEventType.value.type
  });
  
  cancel(); // Reset and close
};

// Watch for dialog opening
watch(() => props.isOpen, (isOpen) => {
  if (isOpen) {
    loadSites();
  }
});
</script>
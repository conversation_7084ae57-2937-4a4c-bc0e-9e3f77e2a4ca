<template>
  <div class="flex gap-4 h-full">
    <!-- SQL Query -->
    <div class="w-1/3 flex flex-col">
      <div class="bg-white border rounded-lg shadow-sm flex-grow flex flex-col">
        <div class="p-4 border-b">
          <h2 class="text-lg font-semibold">SQL Query</h2>
          <p v-if="queryPreview" class="text-sm text-gray-500 mt-1">
            Found {{ queryPreview.count }} documents
          </p>
        </div>
        <div class="p-4 flex-grow">
          <SqlEditor
            :model-value="editableQuery"
            @update:model-value="handleQueryUpdate"
            :height="'100%'"
          />
        </div>
      </div>
    </div>

    <!-- Document Preview -->
    <div class="w-1/3 flex flex-col">
      <div class="bg-white border rounded-lg shadow-sm flex-grow flex flex-col">
        <div class="p-4 border-b flex items-center justify-between">
          <h2 class="text-lg font-semibold">Document Preview</h2>
          <div v-if="queryPreview && queryPreview.documents && queryPreview.documents.length > 0" class="flex items-center space-x-2">
            <span class="text-sm text-gray-500">{{ currentDocumentIndex + 1 }} of {{ queryPreview.documents.length }}</span>
            <button
              @click="previousDocument"
              :disabled="currentDocumentIndex === 0"
              class="p-1 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
              </svg>
            </button>
            <button
              @click="nextDocument"
              :disabled="currentDocumentIndex === queryPreview.documents.length - 1"
              class="p-1 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
              </svg>
            </button>
          </div>
        </div>
        <div class="flex-grow">
          <div v-if="!queryPreview || !queryPreview.documents || queryPreview.documents.length === 0" class="flex items-center justify-center h-full text-gray-500 text-center p-8">
            <div>
              <svg class="w-16 h-16 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
              <h3 class="text-lg font-medium text-gray-600 mb-2">No Documents Found</h3>
              <p class="text-sm text-gray-400">
                No documents match your query criteria. Try adjusting your query or site selection.
              </p>
            </div>
          </div>
          <div v-else-if="currentDocument" class="h-full">
            <DocumentPreview :document="currentDocument" :default-zoom="'fit-width'" />
          </div>
        </div>
      </div>
    </div>

    <!-- Extraction Config -->
    <div class="w-1/3 flex flex-col">
      <div class="bg-white border rounded-lg shadow-sm flex-grow flex flex-col">
        <div class="p-4 border-b flex items-center justify-between">
          <h2 class="text-lg font-semibold">Extraction Configuration</h2>
        </div>
        <div class="p-4 overflow-y-auto">
          <ExtractionConfigurator
            :config="{ fields: editableFields, extractors: editableExtractors }"
            @update:config="handleConfigUpdate"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, PropType, ref, watch, computed } from 'vue';
import SqlEditor from '@/components/SqlEditor.vue';
import type { FieldOption } from '../views/DatasetBuilderView.vue';
import type { Document } from '../types';
import DocumentPreview from './DocumentPreview.vue';
import ExtractionConfigurator from './ExtractionConfigurator.vue';
import { documentPrefetcher } from '@/services/documentPrefetcher';

interface Extractor {
  provider: string;
  model: string;
  prompt?: string;
}

interface QueryPreview {
  count: number;
  documents: Partial<Document>[];
}

const props = defineProps({
  datasetName: {
    type: String,
    required: true,
  },
  query: {
    type: String,
    required: true,
  },
  fields: {
    type: Array as PropType<FieldOption[]>,
    required: true,
  },
  extractors: {
    type: Array as PropType<Extractor[]>,
    required: true,
  },
  queryPreview: {
    type: Object as PropType<QueryPreview | null>,
    default: null,
  },
});

const emit = defineEmits(['update:query', 'update:fields', 'update:extractors']);

const editableQuery = ref(props.query);
const editableFields = ref<FieldOption[]>(JSON.parse(JSON.stringify(props.fields)));
const editableExtractors = ref<Extractor[]>(JSON.parse(JSON.stringify(props.extractors)));

// Document navigation state
const currentDocumentIndex = ref(0);

// Current document computed property
const currentDocument = computed(() => {
  if (!props.queryPreview?.documents?.length) return null;
  return props.queryPreview.documents[currentDocumentIndex.value] || null;
});

// Watch for prop changes
watch(() => props.query, (newVal) => {
  if (newVal !== editableQuery.value) {
    editableQuery.value = newVal;
  }
});

watch(() => props.fields, (newVal) => {
  editableFields.value = JSON.parse(JSON.stringify(newVal));
}, { deep: true });

watch(() => props.extractors, (newVal) => {
  editableExtractors.value = JSON.parse(JSON.stringify(newVal));
}, { deep: true });

// Watch for local changes and emit updates
watch(editableFields, (newVal) => {
  emit('update:fields', newVal);
}, { deep: true });

watch(editableExtractors, (newVal) => {
  emit('update:extractors', newVal);
}, { deep: true });

// Handle query updates with debouncing
let queryUpdateTimeout: NodeJS.Timeout | null = null;
const handleQueryUpdate = (value: string) => {
  editableQuery.value = value;
  
  // Clear existing timeout
  if (queryUpdateTimeout) {
    clearTimeout(queryUpdateTimeout);
  }
  
  // Debounce the update to avoid excessive API calls
  queryUpdateTimeout = setTimeout(() => {
    emit('update:query', value);
  }, 500);
};

const handleConfigUpdate = (newConfig: { fields: FieldOption[], extractors: Extractor[] }) => {
  editableFields.value = newConfig.fields;
  editableExtractors.value = newConfig.extractors;
};

// Document navigation methods
const nextDocument = () => {
  if (!props.queryPreview?.documents?.length) return;
  if (currentDocumentIndex.value < props.queryPreview.documents.length - 1) {
    currentDocumentIndex.value++;
    // Trigger prefetching for the next batch of documents
    prefetchNextDocuments();
  }
};

const previousDocument = () => {
  if (currentDocumentIndex.value > 0) {
    currentDocumentIndex.value--;
    // Trigger prefetching for the previous batch of documents
    prefetchNextDocuments();
  }
};

// Prefetch the next 10 documents starting from current position
const prefetchNextDocuments = () => {
  if (!props.queryPreview?.documents?.length) return;
  
  const startIndex = currentDocumentIndex.value + 1;
  const endIndex = Math.min(startIndex + 10, props.queryPreview.documents.length);
  
  const documentsToPreffetch = props.queryPreview.documents
    .slice(startIndex, endIndex)
    .map(doc => doc.id || (doc.gcsPath ? doc.gcsPath.split('/').pop() : null))
    .filter(Boolean) as string[];
  
  if (documentsToPreffetch.length > 0) {
    console.log(`🔄 Prefetching ${documentsToPreffetch.length} documents from index ${startIndex}`);
    documentPrefetcher.prefetchBatch(documentsToPreffetch);
  }
};

// Reset document index when query preview changes and prefetch initial batch
watch(() => props.queryPreview, () => {
  currentDocumentIndex.value = 0;
  prefetchNextDocuments();
}, { immediate: true });
</script>

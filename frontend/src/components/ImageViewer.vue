<template>
  <div class="image-viewer h-full w-full relative bg-gray-50 overflow-hidden">
    <!-- Image Controls Bar -->
    <div class="absolute top-4 left-1/2 transform -translate-x-1/2 z-20 bg-white/90 backdrop-blur-sm rounded-lg shadow-lg px-4 py-2">
      <div class="flex items-center space-x-2">
        <!-- Zoom Controls -->
        <button 
          @click="zoomOut" 
          :disabled="scale <= minScale"
          class="p-2 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
          title="Zoom Out"
        >
          <ZoomOut class="w-4 h-4" />
        </button>
        
        <span class="text-sm text-gray-600 min-w-[60px] text-center font-medium">
          {{ Math.round(scale * 100) }}%
        </span>
        
        <button 
          @click="zoomIn" 
          :disabled="scale >= maxScale"
          class="p-2 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
          title="Zoom In"
        >
          <ZoomIn class="w-4 h-4" />
        </button>
        
        <div class="w-px h-6 bg-gray-300 mx-2"></div>
        
        <!-- Fit Controls -->
        <button 
          @click="fitToScreen" 
          class="p-2 rounded hover:bg-gray-100"
          title="Fit to Screen"
        >
          <Maximize class="w-4 h-4" />
        </button>
        
        <button 
          @click="actualSize" 
          class="p-2 rounded hover:bg-gray-100"
          title="Actual Size (100%)"
        >
          <Minimize class="w-4 h-4" />
        </button>
        
        <div class="w-px h-6 bg-gray-300 mx-2"></div>
        
        <!-- Rotation Controls -->
        <button 
          @click="rotateLeft" 
          class="p-2 rounded hover:bg-gray-100"
          title="Rotate Left"
        >
          <RotateCcw class="w-4 h-4" />
        </button>
        
        <button 
          @click="rotateRight" 
          class="p-2 rounded hover:bg-gray-100"
          title="Rotate Right"
        >
          <RotateCw class="w-4 h-4" />
        </button>
        
        <div class="w-px h-6 bg-gray-300 mx-2"></div>
        
        <!-- Fullscreen Toggle -->
        <button 
          @click="toggleFullscreen" 
          class="p-2 rounded hover:bg-gray-100"
          title="Toggle Fullscreen"
        >
          <Expand v-if="!isFullscreen" class="w-4 h-4" />
          <Shrink v-else class="w-4 h-4" />
        </button>
      </div>
    </div>
    
    <!-- Image Container -->
    <div 
      ref="containerRef"
      class="image-container h-full w-full overflow-hidden cursor-grab active:cursor-grabbing"
      :class="{ 'cursor-zoom-in': scale < maxScale && !isDragging, 'cursor-zoom-out': scale > minScale && !isDragging }"
      @mousedown="startDrag"
      @mousemove="drag"
      @mouseup="endDrag"
      @mouseleave="endDrag"
      @wheel="handleWheel"
      @dblclick="handleDoubleClick"
    >
      <div 
        class="image-wrapper flex items-center justify-center h-full w-full"
        :style="{ 
          transform: `translate(${panX}px, ${panY}px)`,
          transition: isAnimating ? 'transform 0.3s ease-out' : 'none'
        }"
      >
        <img 
          ref="imageRef"
          :src="imageUrl" 
          :alt="alt"
          class="max-w-none select-none transition-transform duration-200"
          :style="{ 
            transform: `scale(${scale}) rotate(${rotation}deg)`,
            transformOrigin: 'center center'
          }"
          @load="onImageLoad"
          @error="onImageError"
          @dragstart.prevent
        />
      </div>
    </div>
    
    <!-- Loading State -->
    <div v-if="loading" class="absolute inset-0 flex items-center justify-center bg-gray-50">
      <div class="flex flex-col items-center space-y-4">
        <Loader2 class="w-8 h-8 animate-spin text-blue-500" />
        <p class="text-gray-600">Loading image...</p>
      </div>
    </div>
    
    <!-- Error State -->
    <div v-if="error" class="absolute inset-0 flex items-center justify-center bg-gray-50">
      <div class="text-center max-w-md px-4">
        <AlertCircle class="w-12 h-12 text-red-500 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-900 mb-2">Failed to load image</h3>
        <p class="text-gray-600 mb-4">{{ error }}</p>
        <button 
          @click="retryLoad"
          class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          Try Again
        </button>
      </div>
    </div>
    
    <!-- Image Info Overlay -->
    <div v-if="showInfo && !loading && !error" class="absolute bottom-4 left-4 bg-black/70 text-white px-3 py-2 rounded-lg text-sm">
      <div class="space-y-1">
        <div>{{ imageWidth }} × {{ imageHeight }}px</div>
        <div>{{ formatFileSize(fileSize) }}</div>
        <div>{{ scale.toFixed(1) }}× zoom</div>
      </div>
    </div>
    
    <!-- Toggle Info Button -->
    <button 
      v-if="!loading && !error"
      @click="showInfo = !showInfo"
      class="absolute bottom-4 right-4 p-2 bg-black/70 text-white rounded-lg hover:bg-black/80 transition-colors"
      title="Toggle Image Info"
    >
      <Info class="w-4 h-4" />
    </button>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { 
  ZoomIn, 
  ZoomOut, 
  RotateCw, 
  RotateCcw, 
  Maximize, 
  Minimize, 
  Expand, 
  Shrink, 
  Loader2, 
  AlertCircle,
  Info
} from 'lucide-vue-next'

interface Props {
  imageUrl: string
  alt?: string
  fileSize?: number
}

const props = withDefaults(defineProps<Props>(), {
  alt: 'Image',
  fileSize: 0
})

// Reactive state
const loading = ref(true)
const error = ref<string | null>(null)
const scale = ref(1)
const rotation = ref(0)
const panX = ref(0)
const panY = ref(0)
const isDragging = ref(false)
const isAnimating = ref(false)
const isFullscreen = ref(false)
const showInfo = ref(false)

// Image dimensions
const imageWidth = ref(0)
const imageHeight = ref(0)

// Container and image refs
const containerRef = ref<HTMLElement>()
const imageRef = ref<HTMLImageElement>()

// Drag state
const dragStart = ref({ x: 0, y: 0 })
const panStart = ref({ x: 0, y: 0 })

// Scale limits
const minScale = 0.1
const maxScale = 5.0

// Methods
const zoomIn = () => {
  const newScale = Math.min(scale.value * 1.2, maxScale)
  setScale(newScale)
}

const zoomOut = () => {
  const newScale = Math.max(scale.value / 1.2, minScale)
  setScale(newScale)
}

const setScale = (newScale: number, animate = true) => {
  if (animate) {
    isAnimating.value = true
    setTimeout(() => {
      isAnimating.value = false
    }, 300)
  }
  scale.value = newScale
}

const fitToScreen = () => {
  if (!containerRef.value || !imageRef.value) return
  
  const container = containerRef.value.getBoundingClientRect()
  const containerWidth = container.width - 20 // Minimal padding for UI elements
  const containerHeight = container.height - 80 // Account for controls bar at top
  
  const scaleX = containerWidth / imageWidth.value
  const scaleY = containerHeight / imageHeight.value
  const newScale = Math.min(scaleX, scaleY, 1) // Don't scale up beyond 100%
  
  setScale(newScale)
  centerImage()
}

const actualSize = () => {
  setScale(1)
  centerImage()
}

const centerImage = () => {
  isAnimating.value = true
  panX.value = 0
  panY.value = 0
  setTimeout(() => {
    isAnimating.value = false
  }, 300)
}

const rotateLeft = () => {
  rotation.value -= 90
  if (rotation.value < 0) rotation.value += 360
}

const rotateRight = () => {
  rotation.value += 90
  if (rotation.value >= 360) rotation.value -= 360
}

const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    containerRef.value?.requestFullscreen()
  } else {
    document.exitFullscreen()
  }
}

const startDrag = (event: MouseEvent) => {
  if (event.button !== 0) return // Only left mouse button
  
  isDragging.value = true
  dragStart.value = { x: event.clientX, y: event.clientY }
  panStart.value = { x: panX.value, y: panY.value }
  
  event.preventDefault()
}

const drag = (event: MouseEvent) => {
  if (!isDragging.value) return
  
  const deltaX = event.clientX - dragStart.value.x
  const deltaY = event.clientY - dragStart.value.y
  
  panX.value = panStart.value.x + deltaX
  panY.value = panStart.value.y + deltaY
}

const endDrag = () => {
  isDragging.value = false
}

const handleWheel = (event: WheelEvent) => {
  event.preventDefault()
  
  const delta = -event.deltaY
  const scaleFactor = delta > 0 ? 1.1 : 0.9
  const newScale = Math.max(minScale, Math.min(maxScale, scale.value * scaleFactor))
  
  setScale(newScale, false)
}

const handleDoubleClick = () => {
  if (scale.value === 1) {
    fitToScreen()
  } else {
    actualSize()
  }
}

const onImageLoad = () => {
  loading.value = false
  error.value = null
  
  if (imageRef.value) {
    imageWidth.value = imageRef.value.naturalWidth
    imageHeight.value = imageRef.value.naturalHeight
    
    // Auto-fit on first load
    nextTick(() => {
      fitToScreen()
    })
  }
}

const onImageError = () => {
  loading.value = false
  error.value = 'Failed to load image. The file may be corrupted or unavailable.'
}

const retryLoad = () => {
  loading.value = true
  error.value = null
  
  if (imageRef.value) {
    // Force reload by adding timestamp
    const url = new URL(props.imageUrl)
    url.searchParams.set('retry', Date.now().toString())
    imageRef.value.src = url.toString()
  }
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`
}

// Fullscreen event listeners
const handleFullscreenChange = () => {
  isFullscreen.value = !!document.fullscreenElement
}

// Keyboard shortcuts
const handleKeyDown = (event: KeyboardEvent) => {
  if (!containerRef.value?.contains(event.target as Node)) return
  
  switch (event.key) {
    case '+':
    case '=':
      event.preventDefault()
      zoomIn()
      break
    case '-':
      event.preventDefault()
      zoomOut()
      break
    case '0':
      event.preventDefault()
      actualSize()
      break
    case 'f':
    case 'F11':
      event.preventDefault()
      toggleFullscreen()
      break
    case 'r':
      event.preventDefault()
      if (event.shiftKey) {
        rotateLeft()
      } else {
        rotateRight()
      }
      break
    case 'Escape':
      if (document.fullscreenElement) {
        document.exitFullscreen()
      }
      break
  }
}

// Lifecycle
onMounted(() => {
  document.addEventListener('fullscreenchange', handleFullscreenChange)
  document.addEventListener('keydown', handleKeyDown)
})

onUnmounted(() => {
  document.removeEventListener('fullscreenchange', handleFullscreenChange)
  document.removeEventListener('keydown', handleKeyDown)
})

// Watch for image URL changes
watch(() => props.imageUrl, () => {
  loading.value = true
  error.value = null
  scale.value = 1
  rotation.value = 0
  panX.value = 0
  panY.value = 0
})
</script>

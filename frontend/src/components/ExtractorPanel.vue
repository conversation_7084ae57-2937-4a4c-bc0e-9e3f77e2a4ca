<template>
  <div class="flex flex-col items-center gap-1">
    <!-- Horizontal Layout: Logo, PercentBadge, Button -->
    <div class="flex items-center gap-2">
      <!-- Extractor Logo -->
      <div class="w-4 flex justify-center">
        <Skeleton v-if="isExtracting" class="w-4 h-4 rounded" />
        <ExtractorLogo v-else :extractor="extractor" :size="16" />
      </div>
      
      <!-- Accuracy Badge -->
      <div class="w-20 flex justify-center">
        <Skeleton v-if="isExtracting" class="w-12 h-5 rounded" />
        <PercentBadge v-else-if="accuracy?.hasGroundTruth" :percentage="accuracy.percentage" />
        <div v-else-if="hasExtraction && accuracy && !accuracy.hasGroundTruth" class="px-2 py-0.5 text-xs font-medium rounded border bg-gray-100 text-gray-500 border-gray-200">
          No GT
        </div>
        <div v-else-if="hasExtraction" class="px-2 py-0.5 text-xs font-medium rounded border bg-gray-100 text-gray-400 border-gray-200">
          No GT
        </div>
        <div v-else class="px-2 py-0.5 text-xs font-medium rounded border bg-gray-100 text-gray-400 border-gray-200">
          None
        </div>
      </div>
      
      <!-- Extraction Button -->
      <div class="w-5 flex justify-center">
        <button
          @click="$emit('extract')"
          :disabled="disabled"
          :class="{
            'bg-primary text-primary-foreground hover:bg-primary/90': !hasExtraction,
            'bg-secondary text-secondary-foreground hover:bg-secondary/80 border border-primary/20': hasExtraction
          }"
          class="w-5 h-5 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
          :title="`${hasExtraction ? 'Re-run' : 'Run'} ${extractor}`"
        >
          <div v-if="isExtracting" class="animate-spin rounded-full h-2 w-2 border-b-2 border-current"></div>
          <RotateCcw v-else-if="hasExtraction" class="h-2.5 w-2.5" />
          <Play v-else class="h-2.5 w-2.5" />
        </button>
      </div>
    </div>
    
    <!-- Progress Bar -->
    <div v-if="extractionStatus" class="w-full mt-1">
      <div class="w-full h-1 bg-gray-200 rounded-full overflow-hidden">
        <div 
          class="h-full transition-all duration-300"
          :class="{
            'bg-blue-500': extractionStatus.status !== 'failed' && extractionStatus.status !== 'completed',
            'bg-green-500': extractionStatus.status === 'completed',
            'bg-red-500': extractionStatus.status === 'failed'
          }"
          :style="{ width: `${extractionStatus.progress || 0}%` }"
        ></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import ExtractorLogo from './ExtractorLogo.vue';
import PercentBadge from './PercentBadge.vue';
import { Skeleton } from '@/components/ui/skeleton';
import { Play, RotateCcw } from 'lucide-vue-next';

interface Props {
  extractor: string;
  accuracy?: {
    correct: number;
    total: number;
    percentage: number;
    hasGroundTruth: boolean;
  } | null;
  hasExtraction: boolean;
  isExtracting: boolean;
  disabled: boolean;
  extractionStatus?: {
    status: string;
    progress: number;
    message: string;
  } | null;
}

defineProps<Props>();
defineEmits<{
  extract: [];
}>();
</script>
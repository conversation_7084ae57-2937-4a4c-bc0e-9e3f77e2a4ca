<template>
  <div class="sql-editor-container h-full flex flex-col">
    <VueMonacoEditor
      v-model:value="internalValue"
      theme="vs-dark"
      language="sql"
      :options="editorOptions"
      @change="handleChange"
      @mount="handleMount"
      class="sql-editor flex-grow"
      :style="height ? { height: typeof height === 'number' ? `${height}px` : height } : {}"
    />
    <div class="editor-info flex-shrink-0">
      <span class="text-xs text-muted-foreground">SQL Editor - Ctrl+Space for autocomplete</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { VueMonacoEditor } from '@guolao/vue-monaco-editor'
import type * as monaco from 'monaco-editor'

interface Props {
  modelValue: string
  height?: number | string
  placeholder?: string
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'change', value: string): void
}

const props = withDefaults(defineProps<Props>(), {
  height: 200,
  placeholder: 'SELECT * FROM documents WHERE...'
})

const emit = defineEmits<Emits>()

const editor = ref<monaco.editor.IStandaloneCodeEditor>()
const internalValue = ref(props.modelValue)

watch(() => props.modelValue, (newValue) => {
  if (newValue !== internalValue.value) {
    internalValue.value = newValue
  }
})

const editorOptions = {
  automaticLayout: true,
  formatOnType: true,
  formatOnPaste: true,
  minimap: {
    enabled: false
  },
  scrollBeyondLastLine: false,
  wordWrap: 'on' as const,
  lineNumbers: 'on' as const,
  glyphMargin: false,
  folding: false,
  lineDecorationsWidth: 10,
  lineNumbersMinChars: 3,
  renderLineHighlight: 'line' as const,
  selectOnLineNumbers: true,
  fontSize: 14,
  fontFamily: 'ui-monospace, SFMono-Regular, "SF Mono", Monaco, Consolas, "Liberation Mono", "Courier New", monospace',
  placeholder: props.placeholder,
  suggest: {
    showKeywords: true,
    showSnippets: true,
    showFunctions: true
  },
  quickSuggestions: {
    other: true,
    comments: false,
    strings: true
  }
}

const handleChange = (value: string) => {
  internalValue.value = value
  emit('update:modelValue', value)
  emit('change', value)
}

const handleMount = async (editorInstance: monaco.editor.IStandaloneCodeEditor) => {
  editor.value = editorInstance
  
  // Add SQL keywords and functions for better autocomplete
  const sqlKeywords = [
    'SELECT', 'FROM', 'WHERE', 'AND', 'OR', 'NOT', 'IN', 'LIKE', 'IS', 'NULL',
    'INSERT', 'UPDATE', 'DELETE', 'CREATE', 'ALTER', 'DROP', 'TABLE', 'INDEX',
    'JOIN', 'INNER', 'LEFT', 'RIGHT', 'FULL', 'OUTER', 'ON', 'GROUP', 'BY',
    'ORDER', 'HAVING', 'DISTINCT', 'COUNT', 'SUM', 'AVG', 'MIN', 'MAX',
    'LIMIT', 'OFFSET', 'UNION', 'INTERSECT', 'EXCEPT', 'CASE', 'WHEN', 'THEN',
    'ELSE', 'END', 'AS', 'ASC', 'DESC', 'documents', 'datasets'
  ]

  // Register SQL completions
  const monaco = await import('monaco-editor')
  monaco.languages.registerCompletionItemProvider('sql', {
    provideCompletionItems: (model, position) => {
      const word = model.getWordUntilPosition(position)
      const range = {
        startLineNumber: position.lineNumber,
        endLineNumber: position.lineNumber,
        startColumn: word.startColumn,
        endColumn: word.endColumn
      }
      const suggestions = sqlKeywords.map((keyword) => ({
        label: keyword,
        kind: monaco.languages.CompletionItemKind.Keyword,
        insertText: keyword,
        range: range
      }))

      return { suggestions }
    }
  })
}
</script>
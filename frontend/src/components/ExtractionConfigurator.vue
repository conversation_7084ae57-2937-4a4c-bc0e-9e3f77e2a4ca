<template>
  <div class="space-y-4">
    <!-- Fields Section -->
    <div class="bg-card border border-border rounded-lg shadow-sm">
      <div class="p-4 border-b border-border">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold text-foreground">Extraction Fields</h3>
          <button 
            @click="addField" 
            class="px-2 py-1 text-xs bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors flex items-center gap-1"
          >
            <svg class="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Add Field
          </button>
        </div>
      </div>
      <div class="p-4">
        <div v-if="editableConfig.fields.length === 0" class="text-center py-6 text-muted-foreground">
          <svg class="h-10 w-10 mx-auto mb-2 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
          <p class="text-sm">No fields configured yet</p>
          <p class="text-xs mt-1">Add fields to define what data to extract</p>
        </div>
        <div v-else class="space-y-3">
          <div 
            v-for="(field, index) in editableConfig.fields" 
            :key="index" 
            class="bg-muted/30 border border-border rounded-lg p-3"
          >
            <div class="flex items-start justify-between">
              <div class="flex-1 space-y-2">
                <div class="grid grid-cols-2 gap-2">
                  <div>
                    <label class="block text-xs font-medium text-foreground mb-1">Field Name</label>
                    <input 
                      v-model="field.name" 
                      type="text" 
                      @input="emitUpdate"
                      class="w-full px-2 py-1 text-sm border border-input rounded-md bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring"
                    />
                  </div>
                  <div>
                    <label class="block text-xs font-medium text-foreground mb-1">Type</label>
                    <select 
                      v-model="field.fieldType" 
                      @change="emitUpdate"
                      class="w-full px-2 py-1 text-sm border border-input rounded-md bg-background text-foreground focus:outline-none focus:ring-1 focus:ring-ring"
                    >
                      <option value="text">Text</option>
                      <option value="number">Number</option>
                      <option value="date">Date</option>
                      <option value="boolean">Boolean</option>
                      <option value="email">Email</option>
                      <option value="phone">Phone</option>
                      <option value="address">Address</option>
                    </select>
                  </div>
                </div>
                <div>
                  <label class="block text-xs font-medium text-foreground mb-1">Description</label>
                  <textarea 
                    v-model="field.description" 
                    rows="2"
                    @input="emitUpdate"
                    class="w-full px-2 py-1 text-sm border border-input rounded-md bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring resize-vertical"
                  ></textarea>
                </div>
              </div>
              <button 
                @click="removeField(index)" 
                class="ml-2 p-1 text-destructive hover:bg-destructive/10 rounded-md transition-colors"
              >
                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Extractors Section -->
    <div class="bg-card border border-border rounded-lg shadow-sm">
      <div class="p-4 border-b border-border">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold text-foreground">Extractors</h3>
          <button 
            @click="addExtractor" 
            class="px-2 py-1 text-xs bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors flex items-center gap-1"
          >
            <svg class="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Add Extractor
          </button>
        </div>
      </div>
      <div class="p-4">
        <div v-if="editableConfig.extractors.length === 0" class="text-center py-6 text-muted-foreground">
          <svg class="h-10 w-10 mx-auto mb-2 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
          </svg>
          <p class="text-sm">No extractors configured yet</p>
          <p class="text-xs mt-1">Add extractors to define how data should be extracted</p>
        </div>
        <div v-else class="space-y-3">
          <div 
            v-for="(extractor, index) in editableConfig.extractors" 
            :key="index" 
            class="bg-muted/30 border border-border rounded-lg p-3"
          >
            <div class="flex items-start justify-between">
              <div class="flex-1 space-y-2">
                <div class="grid grid-cols-2 gap-2">
                  <div>
                    <label class="block text-xs font-medium text-foreground mb-1">Provider</label>
                    <select 
                      v-model="extractor.provider" 
                      @change="emitUpdate"
                      class="w-full px-2 py-1 text-sm border border-input rounded-md bg-background text-foreground focus:outline-none focus:ring-1 focus:ring-ring"
                    >
                      <option value="google">Google</option>
                      <option value="mistral">Mistral</option>
                      <option value="anthropic">Anthropic</option>
                      <option value="openai">OpenAI</option>
                    </select>
                  </div>
                  <div>
                    <label class="block text-xs font-medium text-foreground mb-1">Model</label>
                    <input 
                      v-model="extractor.model" 
                      type="text" 
                      @input="emitUpdate"
                      class="w-full px-2 py-1 text-sm border border-input rounded-md bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring"
                    />
                  </div>
                </div>
                <div>
                  <label class="block text-xs font-medium text-foreground mb-1">Prompt</label>
                  <textarea 
                    v-model="extractor.prompt" 
                    rows="3"
                    @input="emitUpdate"
                    class="w-full px-2 py-1 text-sm border border-input rounded-md bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring resize-vertical"
                  ></textarea>
                </div>
              </div>
              <button 
                @click="removeExtractor(index)" 
                class="ml-2 p-1 text-destructive hover:bg-destructive/10 rounded-md transition-colors"
              >
                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>


<script setup lang="ts">
import { ref, watch, PropType } from 'vue';
import type { ExtractionConfig } from '../types';

type EditableConfig = {
  fields: { name: string; fieldType: string; description: string }[];
  extractors: { provider: string; model: string; prompt: string }[];
};

const props = defineProps({
  config: {
    type: Object as PropType<EditableConfig>,
    required: true,
  },
});

const emit = defineEmits(['update:config']);

const editableConfig = ref<EditableConfig>(JSON.parse(JSON.stringify(props.config)));

watch(() => props.config, (newConfig) => {
  editableConfig.value = JSON.parse(JSON.stringify(newConfig));
}, { deep: true });

const emitUpdate = () => {
  emit('update:config', editableConfig.value);
};

const addField = () => {
  editableConfig.value.fields.push({ name: '', fieldType: 'text', description: '' });
  emitUpdate();
};

const removeField = (index: number) => {
  editableConfig.value.fields.splice(index, 1);
  emitUpdate();
};

const addExtractor = () => {
  editableConfig.value.extractors.push({ provider: 'google', model: '', prompt: '' });
  emitUpdate();
};

const removeExtractor = (index: number) => {
  editableConfig.value.extractors.splice(index, 1);
  emitUpdate();
};
</script>

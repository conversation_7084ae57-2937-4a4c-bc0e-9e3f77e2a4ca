<template>
  <div class="p-3 rounded-lg border border-border bg-card">
    <div class="flex items-start justify-between">
      <div class="flex-1">
        <div v-if="!hideHeader" class="flex items-center space-x-2 mb-2">
          <Skeleton class="h-4 w-20" />
          <Skeleton class="w-4 h-4 rounded" />
        </div>
        
        <div class="space-y-2">
          <Skeleton class="h-5 w-3/4" />
          <Skeleton class="h-3 w-1/2" />
        </div>
      </div>
      
      <div class="flex flex-col items-end space-y-1">
        <Skeleton class="h-5 w-12 rounded" />
        <Skeleton class="h-3 w-16" />
      </div>
    </div>
    
    <div v-if="showMetadata" class="mt-3 pt-3 border-t border-border">
      <div class="grid grid-cols-2 gap-2">
        <Skeleton class="h-3 w-16" />
        <Skeleton class="h-3 w-20" />
        <Skeleton class="h-3 w-24 col-span-2" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Skeleton } from '@/components/ui/skeleton'

interface Props {
  showMetadata?: boolean
  hideHeader?: boolean
}

withDefaults(defineProps<Props>(), {
  showMetadata: false,
  hideHeader: false,
})
</script>
</template>
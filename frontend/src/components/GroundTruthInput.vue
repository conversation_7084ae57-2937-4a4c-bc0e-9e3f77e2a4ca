<template>
  <div class="space-y-2">
    <div 
      class="p-2 rounded-md border border-dashed"
      :class="{
        'bg-blue-50/50 border-blue-200': hasAnnotationOrChanges && !isMarkedNotExtractable,
        'bg-red-50/50 border-red-200': isMarkedNotExtractable,
        'bg-muted/50 border-border': !hasAnnotationOrChanges && !isMarkedNotExtractable
      }"
    >
      <label class="block text-xs font-medium mb-1" 
        :class="{
          'text-blue-700': hasAnnotationOrChanges && !isMarkedNotExtractable,
          'text-red-700': isMarkedNotExtractable,
          'text-muted-foreground': !hasAnnotationOrChanges && !isMarkedNotExtractable
        }"
      >
        {{ statusLabel }}
      </label>
      
      <!-- Not Extractable State -->
      <div v-if="isMarkedNotExtractable" class="flex items-center justify-between">
        <div class="flex-1 px-3 py-2 text-xs bg-red-50 border border-red-200 rounded text-red-700">
          Model should return null.
        </div>
        <button 
          @click="clearNotExtractableMarking" 
          class="ml-2 p-1.5 hover:bg-green-50 hover:text-green-600 rounded-md border border-green-200 text-green-500"
          title="Clear not extractable marking"
        >
          <RotateCcw class="h-3 w-3" />
        </button>
      </div>
      
      <!-- Input State -->
      <div v-else class="flex items-center gap-2">
        <input
          :value="currentValue"
          @input="updateValue($event.target.value)"
          @blur="saveAnnotation"
          :type="inputType"
          :placeholder="placeholder"
          class="w-full px-2 py-1 text-sm bg-background border rounded focus:outline-none focus:ring-1 focus:ring-primary"
          :class="{ 
            'border-blue-300': hasAnnotationOrChanges, 
            'border-border': !hasAnnotationOrChanges 
          }"
        />
        <button 
          v-if="hasAnnotationOrChanges" 
          @click="revertAnnotation" 
          class="p-1.5 hover:bg-muted rounded-md" 
          :title="hasLocalChanges && !hasUserAnnotation ? 'Clear changes' : 'Revert to database value'"
        >
          <RotateCcw class="h-3 w-3 text-muted-foreground" />
        </button>
        <button 
          @click="markAsNotExtractable" 
          class="p-1.5 hover:bg-red-50 hover:text-red-600 rounded-md border border-red-200 text-red-500"
          title="Mark field as not extractable from this document"
        >
          <X class="h-3 w-3" />
        </button>
      </div>
      
      
      <!-- Database Value Display - Always show if we have groundTruthData -->
      <div v-if="groundTruthData" class="mt-2 text-xs text-muted-foreground">
        DB Value: 
        <span class="font-mono bg-gray-200 text-gray-600 px-1 py-0.5 rounded">
          {{ formatDbValue(groundTruthData.extractedValue) }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onUnmounted } from 'vue'
import { RotateCcw, X } from 'lucide-vue-next'

interface Props {
  fieldName: string
  fieldType: string
  groundTruthData?: {
    extractedValue: any
    userAnnotation?: any
    markedNotExtractable?: boolean
    fieldType?: string
  }
}

interface Emits {
  (e: 'update-annotation', fieldName: string, value: any, isNotExtractable?: boolean): void
  (e: 'remove-annotation', fieldName: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const currentValue = ref<string>('')
const originalValue = ref<string>('')  // Track the original value from props
let saveTimeout: ReturnType<typeof setTimeout> | null = null

// Check if user has made an annotation (userAnnotation property exists)
const hasUserAnnotation = computed(() => {
  return props.groundTruthData && props.groundTruthData.hasOwnProperty('userAnnotation') && props.groundTruthData.userAnnotation !== null
})

// Check if user has made local changes (different from original)
const hasLocalChanges = computed(() => {
  return currentValue.value !== originalValue.value
})

// Combined check: has annotation OR has local changes (for immediate visual feedback)
const hasAnnotationOrChanges = computed(() => {
  return hasUserAnnotation.value || hasLocalChanges.value
})

// No special marker needed - we use the boolean flag directly

// Check if marked as not extractable (using the boolean flag)
const isMarkedNotExtractable = computed(() => {
  return props.groundTruthData?.markedNotExtractable === true
})

// Get the effective ground truth value (user annotation takes precedence)
const effectiveGroundTruth = computed(() => {
  if (isMarkedNotExtractable.value) {
    // If marked as not extractable, return null for display purposes
    return null
  }
  if (hasUserAnnotation.value) {
    return props.groundTruthData?.userAnnotation
  }
  return props.groundTruthData?.extractedValue
})

// Status label based on current state (with immediate feedback)
const statusLabel = computed(() => {
  if (isMarkedNotExtractable.value) {
    return 'Marked as Not Extractable'
  }
  if (hasLocalChanges.value && !hasUserAnnotation.value) {
    return 'Typing...'
  }
  if (hasUserAnnotation.value) {
    return 'Overridden by Annotation'
  }
  return 'Ground Truth (Database)'
})

// Format value for display
const formatValue = (value: any): string => {
  if (value === null || value === undefined) return ''
  if (typeof value === 'string') return value
  if (typeof value === 'number') return String(value)
  if (typeof value === 'boolean') return String(value)
  if (typeof value === 'object') return JSON.stringify(value)
  return String(value)
}

// Format database value for display
const formatDbValue = (value: any): string => {
  if (value === null || value === undefined) return 'null'
  return formatValue(value)
}

// Set current value from effective ground truth
const initializeValue = () => {
  const newValue = isMarkedNotExtractable.value ? '' : formatValue(effectiveGroundTruth.value) || ''
  currentValue.value = newValue
  originalValue.value = newValue  // Track the original value
}

// Initialize value when component mounts or props change
initializeValue()

// Watch for changes in currentValue and debounce save
watch(currentValue, (newValue, oldValue) => {
  // Only trigger debounced save if:
  // 1. Value actually changed
  // 2. Component is not in "marked as not extractable" state
  // 3. We're not just initializing (oldValue exists)
  if (newValue !== oldValue && !isMarkedNotExtractable.value && oldValue !== undefined) {
    debouncedSave()
  }
})

// Watch for changes in ground truth data (WebSocket updates) and reinitialize
watch(() => props.groundTruthData, (newData, oldData) => {
  if (newData !== oldData) {
    initializeValue()
  }
}, { deep: true })

// Input type based on field type
const inputType = computed(() => {
  switch (props.fieldType?.toLowerCase()) {
    case 'number':
      return 'number'
    case 'date':
      return 'date'
    case 'boolean':
      return 'text' // We'll handle boolean as text input
    default:
      return 'text'
  }
})

// Placeholder based on field type
const placeholder = computed(() => {
  switch (props.fieldType?.toLowerCase()) {
    case 'number':
      return 'Override...'
    case 'date':
      return 'Override...'
    case 'boolean':
      return 'Override...'
    default:
      return 'Override...'
  }
})

// Convert input value to appropriate type
const parseValue = (inputValue: string): any => {
  if (inputValue.trim() === '') return null
  
  switch (props.fieldType?.toLowerCase()) {
    case 'number':
      const num = parseFloat(inputValue)
      return isNaN(num) ? inputValue : num
    case 'boolean':
      const lower = inputValue.toLowerCase()
      if (lower === 'true') return true
      if (lower === 'false') return false
      return inputValue
    default:
      return inputValue
  }
}

// Update local value
const updateValue = (value: string) => {
  currentValue.value = value
}

// Debounced save annotation
const debouncedSave = () => {
  // Clear existing timeout
  if (saveTimeout) {
    clearTimeout(saveTimeout)
  }
  
  // Set new timeout for 500ms
  saveTimeout = setTimeout(() => {
    saveAnnotation()
  }, 500)
}

// Save annotation
const saveAnnotation = () => {
  // Clear any pending debounced save
  if (saveTimeout) {
    clearTimeout(saveTimeout)
    saveTimeout = null
  }
  
  const parsedValue = parseValue(currentValue.value)
  emit('update-annotation', props.fieldName, parsedValue) // Regular annotation, no isNotExtractable flag
}

// Revert annotation or clear local changes
const revertAnnotation = () => {
  if (hasLocalChanges.value && !hasUserAnnotation.value) {
    // Just local changes, revert to original value
    currentValue.value = originalValue.value
    // Clear any pending debounced save
    if (saveTimeout) {
      clearTimeout(saveTimeout)
      saveTimeout = null
    }
  } else {
    // Has saved annotation, remove it via API
    emit('remove-annotation', props.fieldName)
    // Note: initializeValue will be called when WebSocket updates arrive
  }
}

// Mark as not extractable (use boolean flag)
const markAsNotExtractable = () => {
  emit('update-annotation', props.fieldName, null, true) // null annotation, true for isNotExtractable
  currentValue.value = ''
}

// Clear not extractable marking
const clearNotExtractableMarking = () => {
  emit('remove-annotation', props.fieldName)
  initializeValue()
}

// Cleanup timeout on unmount
onUnmounted(() => {
  if (saveTimeout) {
    clearTimeout(saveTimeout)
  }
})
</script>
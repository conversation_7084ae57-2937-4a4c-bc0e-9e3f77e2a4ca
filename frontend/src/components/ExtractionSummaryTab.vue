<template>
  <Card>
    <CardHeader>
      <CardTitle>Extraction Summary</CardTitle>
      <CardDescription>
        Overall extraction performance across {{ filteredDocuments.length }} document{{ filteredDocuments.length !== 1 ? 's' : '' }}
      </CardDescription>
    </CardHeader>

    <CardContent v-if="isLoading" class="space-y-8 min-h-64">
      <!-- Skeleton for Overall Coverage Stats -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card v-for="i in 3" :key="i" class="bg-muted/30">
          <CardContent class="p-4">
            <div class="flex items-center gap-2 mb-3">
              <Skeleton class="w-5 h-5 rounded" />
              <Skeleton class="h-4 w-16" />
              <Skeleton class="flex-1 h-2 rounded-full" />
            </div>
            
            <div class="space-y-2">
              <div class="flex items-center justify-between">
                <Skeleton class="h-3 w-24" />
                <Skeleton class="h-3 w-16" />
              </div>
              <div class="flex items-center justify-between">
                <Skeleton class="h-3 w-28" />
                <Skeleton class="h-3 w-8" />
              </div>
              <div class="flex items-center justify-between">
                <Skeleton class="h-3 w-20" />
                <Skeleton class="h-3 w-8" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
      
      <!-- Skeleton for Field-by-Field Analysis -->
      <div class="space-y-4">
        <Skeleton class="h-5 w-48" />
        <div class="space-y-3">
          <Card v-for="i in 4" :key="i" class="bg-muted/30">
            <CardContent class="p-4">
              <div class="flex items-center justify-between mb-2">
                <div class="flex items-center space-x-2">
                  <Skeleton class="w-4 h-4 rounded" />
                  <Skeleton class="h-4 w-20" />
                </div>
                <Skeleton class="h-5 w-12 rounded" />
              </div>
              <div class="grid grid-cols-2 gap-4">
                <div v-for="j in 2" :key="j" class="space-y-1">
                  <Skeleton class="h-3 w-16" />
                  <Skeleton class="h-3 w-12" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </CardContent>

    <CardContent v-else class="space-y-8">
      <!-- Overall Coverage Stats -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card v-for="extractor in availableExtractors" :key="extractor" class="bg-muted/30">
          <CardContent class="p-4">
            <div class="flex items-center gap-2 mb-3">
              <ExtractorLogo :extractor="extractor" :size="20" />
              <h3 class="font-semibold text-foreground capitalize">{{ extractor }}</h3>

              <div class="w-full bg-gray-200 rounded-full h-2">
                <div 
                  class="bg-primary h-2 rounded-full transition-all duration-300"
                  :style="{ width: `${getExtractorStats(extractor).coveragePercentage}%` }"
                ></div>
              </div>
            </div>
            
            <div class="space-y-2">
              <div class="flex items-center justify-between">
                <span class="text-sm text-muted-foreground">Documents Processed</span>
                <span class="font-medium">
                  {{ getExtractorStats(extractor).processed }}/{{ filteredDocuments.length }}
                  <span class="text-xs text-muted-foreground ml-1">
                    ({{ getExtractorStats(extractor).coveragePercentage }}%)
                  </span>
                </span>
              </div>
              
              <div class="flex items-center justify-between">
                <span class="text-sm text-muted-foreground">Successful Extractions</span>
                <span class="font-medium text-green-600">
                  {{ getExtractorStats(extractor).successful }}
                </span>
              </div>
              
              <div class="flex items-center justify-between">
                <span class="text-sm text-muted-foreground">Failed Extractions</span>
                <span class="font-medium text-red-600">
                  {{ getExtractorStats(extractor).failed }}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- Field-by-Field Analysis Data Table -->
      <div class="space-y-4">
        <div class="flex items-center space-x-2">
          <FileText class="w-5 h-5" />
          <h3 class="text-lg font-semibold">Field Analysis</h3>
        </div>
        
        <Card>
          <CardContent class="p-0">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead class="w-[200px]">Field</TableHead>
                  <TableHead class="text-left">Type</TableHead>
                  <TableHead class="text-center w-32">Ground Truth Coverage</TableHead>
                  <TableHead class="text-center w-32">Extraction Coverage</TableHead>
                  <TableHead 
                    v-for="extractor in availableExtractors" 
                    :key="extractor" 
                    class="text-center min-w-[150px]"
                  >
                    <div class="flex items-center justify-center space-x-2">
                      <ExtractorLogo :extractor="extractor" :size="16" />
                      <span class="capitalize">{{ extractor }}</span>
                    </div>
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow v-for="field in fieldAnalysis" :key="field.fieldName">
                  <!-- Field Name -->
                  <TableCell class="font-medium">
                    <div class="flex items-center space-x-2">
                      <FieldTypeIcon v-if="field.field_type" :type="field.field_type" />
                      <span>{{ field.fieldName }}</span>
                    </div>
                  </TableCell>
                  
                  <!-- Field Type -->
                  <TableCell class="text-left">
                    <div class="flex items-center space-x-2">
                      <FieldTypeIcon v-if="field.field_type" :type="field.field_type" />
                      <span class="text-xs capitalize">{{ field.field_type || 'Unknown' }}</span>
                    </div>
                  </TableCell>
                  
                  <!-- Ground Truth Coverage -->
                  <TableCell class="text-center w-32">
                    <div class="space-y-1">
                      <div class="w-full bg-gray-200 rounded-full h-1.5">
                        <div 
                          class="bg-blue-600 h-1.5 rounded-full transition-all duration-300"
                          :style="{ width: `${(field.groundTruth.total / filteredDocuments.length) * 100}%` }"
                        ></div>
                      </div>
                      <div class="text-xs text-muted-foreground">
                        {{ field.groundTruth.total }}/{{ filteredDocuments.length }} 
                        ({{ Math.round((field.groundTruth.total / filteredDocuments.length) * 100) }}%)
                      </div>
                    </div>
                  </TableCell>
                  
                  <!-- Extraction Coverage -->
                  <TableCell class="text-center w-32">
                    <div class="space-y-1">
                      <div class="w-full bg-gray-200 rounded-full h-1.5">
                        <div 
                          class="bg-green-600 h-1.5 rounded-full transition-all duration-300"
                          :style="{ width: `${field.extractionCoverage}%` }"
                        ></div>
                      </div>
                      <div class="text-xs text-muted-foreground">
                        {{ field.totalExtracted }}/{{ field.groundTruth.total }} 
                        ({{ field.extractionCoverage }}%)
                      </div>
                    </div>
                  </TableCell>
                  
                  <!-- Extractor Performance -->
                  <TableCell 
                    v-for="extractor in availableExtractors" 
                    :key="extractor" 
                    class="text-center"
                  >
                    <div class="space-y-2">
                      <!-- Accuracy Badge -->
                      <PercentBadge :percentage="field.extractors[extractor]?.accuracyPercentage || 0" />
                      
                      <!-- Accuracy Details -->
                      <div class="space-y-1">
                        <div class="text-xs text-muted-foreground">
                          {{ field.extractors[extractor]?.correct || 0 }}/{{ field.extractors[extractor]?.withGroundTruth || 0 }} correct
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-1">
                          <div 
                            class="h-1 rounded-full transition-all duration-300"
                            :class="getAccuracyBarColor(field.extractors[extractor]?.accuracyPercentage || 0)"
                            :style="{ width: `${field.extractors[extractor]?.accuracyPercentage || 0}%` }"
                          ></div>
                        </div>
                      </div>
                    </div>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>

      <!-- Document Types Distribution -->
      <div class="space-y-4" v-if="documentTypeStats.length > 0">
        <div class="flex items-center space-x-2">
          <FileText class="w-5 h-5" />
          <h3 class="text-lg font-semibold">Document Types Distribution</h3>
        </div>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardContent class="p-4">
              <VChart 
                :option="documentTypeChartOption" 
                style="height: 300px; width: 100%; min-height: 300px;" 
                v-if="isBootstrapped"
                autoresize
                :init-options="{ renderer: 'canvas' }"
              />
            </CardContent>
          </Card>
          
          <Card>
            <CardContent class="p-4">
              <div class="space-y-3">
                <div v-for="stat in documentTypeStats" :key="stat.type" 
                     class="flex items-center justify-between p-2 rounded bg-muted/50">
                  <div class="flex items-center space-x-3">
                    <div class="w-3 h-3 rounded-full" :style="{ backgroundColor: stat.color }"></div>
                    <span class="font-medium">{{ stat.type }}</span>
                  </div>
                  <div class="text-right">
                    <div class="font-semibold">{{ stat.count }}</div>
                    <div class="text-xs text-muted-foreground">{{ stat.percentage }}%</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <!-- Extraction Heatmaps -->
      <div class="space-y-4" v-if="availableExtractors.length > 0 && extractionConfig?.data?.fields">
        <div class="flex items-center space-x-2">
          <Zap class="w-5 h-5" />
          <h3 class="text-lg font-semibold">Extraction Performance Heatmaps</h3>
        </div>
        
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <Card v-for="extractor in availableExtractors" :key="extractor">
            <CardHeader>
              <CardTitle class="flex items-center space-x-2">
                <ExtractorLogo :extractor="extractor" :size="20" />
                <span class="capitalize">{{ extractor }} Performance</span>
              </CardTitle>
              <CardDescription>
                Success rate by field and document type
              </CardDescription>
            </CardHeader>
            <CardContent>
              <VChart 
                :option="getHeatmapOption(extractor)" 
                style="height: 400px; width: 100%; min-height: 400px;" 
                v-if="heatmapData[extractor] && heatmapData[extractor].matrix.length > 0 && isBootstrapped"
                autoresize
                :init-options="{ renderer: 'canvas' }"
              />
              <div v-else class="h-96 flex items-center justify-center text-muted-foreground">
                No data available for heatmap
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <!-- Ground Truth Statistics -->
      <div class="space-y-4">
        <div class="flex items-center space-x-2">
          <Database class="w-5 h-5" />
          <h3 class="text-lg font-semibold">Ground Truth Statistics</h3>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardContent class="p-4 text-center">
              <div class="text-2xl font-bold text-blue-600">{{ groundTruthStats.withGroundTruth }}</div>
              <div class="text-sm text-muted-foreground">Documents with Ground Truth</div>
              <div class="text-xs text-muted-foreground mt-1">
                {{ Math.round((groundTruthStats.withGroundTruth / filteredDocuments.length) * 100) }}% coverage
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent class="p-4 text-center">
              <div class="text-2xl font-bold text-green-600">{{ groundTruthStats.annotated }}</div>
              <div class="text-sm text-muted-foreground">User Annotated</div>
              <div class="text-xs text-muted-foreground mt-1">
                {{ groundTruthStats.withGroundTruth > 0 ? Math.round((groundTruthStats.annotated / groundTruthStats.withGroundTruth) * 100) : 0 }}% of GT
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent class="p-4 text-center">
              <div class="text-2xl font-bold text-purple-600">{{ groundTruthStats.approved }}</div>
              <div class="text-sm text-muted-foreground">Approved Documents</div>
              <div class="text-xs text-muted-foreground mt-1">
                {{ Math.round((groundTruthStats.approved / filteredDocuments.length) * 100) }}% of total
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </CardContent>
  </Card>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useResourcePool } from '../services/resourcePool';
import type { Document, ExtractionConfig } from '../types';
import ExtractorLogo from './ExtractorLogo.vue';
import FieldTypeIcon from './FieldTypeIcon.vue';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { BarChart3, Database, FileText, Zap } from 'lucide-vue-next';
import { calculateCorrectness } from '../utils/correctness';
import PercentBadge from './PercentBadge.vue';
import VChart from 'vue-echarts';
import { use } from 'echarts/core';
import { PieChart, HeatmapChart } from 'echarts/charts';
import { TitleComponent, TooltipComponent, LegendComponent, GridComponent, VisualMapComponent } from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';

use([PieChart, HeatmapChart, TitleComponent, TooltipComponent, LegendComponent, GridComponent, VisualMapComponent, CanvasRenderer]);

interface Props {
  datasetId: string;
}

interface ExtractorStats {
  processed: number;
  successful: number;
  failed: number;
  coveragePercentage: number;
}

interface FieldAnalytics {
  fieldName: string;
  fieldType: string;
  groundTruth: {
    total: number;
    database: number;
    annotated: number;
  };
  extractionCoverage: number;
  totalExtracted: number;
  extractors: Record<string, {
    withGroundTruth: number;
    correct: number;
    wrong: number;
    accuracyPercentage: number;
    correctlyExtracted: number;
    correctlyNull: number;
    missedField: number;
    incorrectValue: number;
    falsePositive: number;
  }>;
}

const props = defineProps<Props>();

// Use resourcePool for all data - no more props!
const { 
  documents, 
  datasets, 
  extractionConfigs, 
  extractions, 
  groundTruths, 
  isBootstrapped, 
  isLoading 
} = useResourcePool();

// Get the current dataset
const currentDataset = computed(() => {
  return datasets.value.find(d => d.id === props.datasetId);
});

// Get the extraction config for this dataset
const extractionConfig = computed((): ExtractionConfig | null => {
  if (!currentDataset.value?.extractionConfigId) return null;
  return extractionConfigs.value.find(c => c.id === currentDataset.value.extractionConfigId) || null;
});

// Filter documents for this dataset (only approved documents for metrics)
const filteredDocuments = computed(() => {
  return documents.value.filter(doc => doc.datasetId === props.datasetId && doc.isApproved);
});

// Filter extractions for this dataset's documents
const datasetExtractions = computed(() => {
  const docIds = new Set(filteredDocuments.value.map(d => d.id));
  return extractions.value.filter(e => docIds.has(e.documentId));
});

// Filter ground truths for this dataset's documents
const datasetGroundTruths = computed(() => {
  const docIds = new Set(filteredDocuments.value.map(d => d.id));
  return groundTruths.value.filter(gt => docIds.has(gt.documentId));
});

// Get available extractors from extractions
const availableExtractors = computed(() => {
  const extractors = new Set<string>();
  datasetExtractions.value.forEach(extraction => {
    extractors.add(extraction.model);
  });
  return Array.from(extractors).sort();
});

// Calculate extractor statistics
const getExtractorStats = computed(() => (extractor: string): ExtractorStats => {
  const extractorExtractions = datasetExtractions.value.filter(e => e.model === extractor);
  const processed = extractorExtractions.length;
  const successful = extractorExtractions.filter(e => e.status === 'completed').length;
  const failed = extractorExtractions.filter(e => e.status === 'failed').length;
  
  return {
    processed,
    successful,
    failed,
    coveragePercentage: filteredDocuments.value.length > 0 ? Math.round((processed / filteredDocuments.value.length) * 100) : 0
  };
});

// Field-by-field analysis
const fieldAnalysis = computed((): FieldAnalytics[] => {
  if (!extractionConfig.value?.data?.fields) return [];

  return extractionConfig.value.data.fields.map(field => {
    const fieldName = field.name;
    const fieldType = field.field_type;

    // Ground truth analysis - only count documents with ground truth for this field
    const fieldGroundTruths = datasetGroundTruths.value.filter(gt => gt.fieldName === fieldName);
    const annotatedCount = fieldGroundTruths.filter(gt => gt.userAnnotation !== null).length;


    
    // Get document IDs that have ground truth for this field
    const documentsWithGroundTruth = new Set(fieldGroundTruths.map(gt => gt.documentId));
    
    // Calculate total extractions for documents with ground truth
    let totalExtracted = 0;
    documentsWithGroundTruth.forEach(docId => {
      const hasAnyExtraction = datasetExtractions.value.some(e => {
        if (e.documentId !== docId || e.status !== 'completed' || !e.data) {
          return false;
        }
        
        if (typeof e.data === 'object' && e.data !== null) {
          return Object.prototype.hasOwnProperty.call(e.data, fieldName);
        }
        
        return false;
      });
      if (hasAnyExtraction) totalExtracted++;
    });

    // Extractor analysis - only consider documents with ground truth
    const extractorAnalysis: Record<string, any> = {};
    
    availableExtractors.value.forEach(extractor => {
      let correct = 0;
      let withGroundTruth = 0;

      // Debug logging for Creation Date field
      let debugInfo: any[] = [];

      // Only look at extractions for documents that have ground truth for this field
      fieldGroundTruths.forEach(groundTruth => {
        const extraction = datasetExtractions.value.find(e => 
          e.documentId === groundTruth.documentId && 
          e.model === extractor && 
          e.status === 'completed'
        );
        
        if (!extraction) {
          if (fieldName === 'Creation Date') {
            debugInfo.push({
              docId: groundTruth.documentId,
              hasExtraction: false,
              reason: 'No completed extraction found'
            });
          }
          return;
        }

        withGroundTruth++;
        
        // Get extracted value - handle both object structure and direct value
        let extractedValue;
        if (extraction.data && typeof extraction.data === 'object') {
          const fieldData = extraction.data[fieldName];
          if (fieldData && typeof fieldData === 'object' && 'value' in fieldData) {
            extractedValue = fieldData.value;
          } else {
            extractedValue = fieldData; // Direct value
          }
        }
        
        const groundTruthValue = groundTruth.userAnnotation ?? groundTruth.extractedValue;

        let isCorrect = false;
        let correctnessScore = 0;

        // Use the calculateCorrectness function for all comparisons
        if (extractedValue !== undefined || groundTruthValue !== undefined) {
          correctnessScore = calculateCorrectness(extractedValue, groundTruthValue, field.fieldType);
          if (correctnessScore === 100) {  // Only 100% matches are correct
            correct++;
            isCorrect = true;
          }
        } else {
          // Both undefined - this shouldn't happen but treat as 0
          correctnessScore = 0;
        }


      });



      extractorAnalysis[extractor] = {
        withGroundTruth,
        correct,
        wrong: withGroundTruth - correct,
        accuracyPercentage: withGroundTruth > 0 ? Math.round((correct / withGroundTruth) * 100) : 0,
        correctlyExtracted: 0, // TODO: implement detailed breakdown
        correctlyNull: 0,
        missedField: 0,
        incorrectValue: 0,
        falsePositive: 0
      };


    });

    // Calculate extraction coverage (how many documents with GT have been extracted)
    const extractionCoverage = fieldGroundTruths.length > 0 ? 
      Math.round((totalExtracted / fieldGroundTruths.length) * 100) : 0;



    return {
      fieldName,
      fieldType,
      groundTruth: {
        total: fieldGroundTruths.length,
        database: fieldGroundTruths.filter(gt => gt.extractedValue !== null).length,
        annotated: annotatedCount
      },
      extractionCoverage,
      totalExtracted,
      extractors: extractorAnalysis
    };
  });
});

// Document type statistics
const documentTypeStats = computed(() => {
  if (filteredDocuments.value.length === 0) return [];
  
  const typeCounts = new Map<string, number>();
  
  filteredDocuments.value.forEach(doc => {
    const extension = doc.path.split('.').pop()?.toLowerCase() || 'unknown';
    const type = extension === 'unknown' ? 'No Extension' : `.${extension}`;
    typeCounts.set(type, (typeCounts.get(type) || 0) + 1);
  });
  
  const colors = [
    '#3B82F6', '#EF4444', '#10B981', '#F59E0B', '#8B5CF6',
    '#EC4899', '#6B7280', '#14B8A6', '#F97316', '#84CC16'
  ];
  
  return Array.from(typeCounts.entries())
    .map(([type, count], index) => ({
      type,
      count,
      percentage: Math.round((count / filteredDocuments.value.length) * 100),
      color: colors[index % colors.length]
    }))
    .sort((a, b) => b.count - a.count);
});

// Document type chart option
const documentTypeChartOption = computed(() => ({
  title: {
    text: 'Document Types',
    left: 'center',
    textStyle: {
      fontSize: 14,
      fontWeight: 'bold'
    }
  },
  tooltip: {
    trigger: 'item',
    formatter: '{b}: {c} ({d}%)'
  },
  legend: {
    orient: 'vertical',
    left: 'left',
    top: 'middle',
    textStyle: {
      fontSize: 11
    }
  },
  series: [
    {
      name: 'Document Types',
      type: 'pie',
      radius: ['35%', '60%'],
      center: ['50%', '50%'],
      avoidLabelOverlap: false,
      label: {
        show: true,
        position: 'outside',
        formatter: '{b}\n{d}%',
        fontSize: 11,
        fontWeight: 'bold',
        distanceToLabelLine: 5
      },
      emphasis: {
        label: {
          show: true,
          fontSize: 13,
          fontWeight: 'bold'
        },
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },
      labelLine: {
        show: true,
        length: 10,
        length2: 8,
        smooth: true
      },
      data: documentTypeStats.value.map(stat => ({
        value: stat.count,
        name: stat.type,
        itemStyle: {
          color: stat.color
        }
      }))
    }
  ]
}));

// Ground truth statistics
const groundTruthStats = computed(() => {
  const total = filteredDocuments.value.length;
  let withGroundTruth = 0;
  let annotated = 0;
  let approved = 0;

  filteredDocuments.value.forEach(doc => {
    const docGroundTruths = datasetGroundTruths.value.filter(gt => gt.documentId === doc.id);
    
    if (docGroundTruths.length > 0) {
      const hasGroundTruth = docGroundTruths.some(gt => 
        gt.userAnnotation !== null || gt.extractedValue !== null
      );
      
      if (hasGroundTruth) {
        withGroundTruth++;
        
        const hasAnnotation = docGroundTruths.some(gt => gt.userAnnotation !== null);
        if (hasAnnotation) {
          annotated++;
        }
      }
    }

    // Check if document is approved (this might need adjustment based on your approval system)
    if (doc.isApproved) {
      approved++;
    }
  });

  return {
    total,
    withGroundTruth,
    annotated,
    approved
  };
});

// Helper functions for styling
const getAccuracyColor = (percentage: number): string => {
  if (percentage >= 90) return 'text-green-600';
  if (percentage >= 70) return 'text-yellow-600';
  return 'text-red-600';
};

const getAccuracyBarColor = (percentage: number): string => {
  if (percentage >= 90) return 'bg-green-600';
  if (percentage >= 70) return 'bg-yellow-600';
  return 'bg-red-600';
};

// Heatmap data computation
const heatmapData = computed(() => {
  const data: Record<string, any> = {};
  
  if (!extractionConfig.value?.data?.fields) return data;
  
  // Only get file types for documents that have ground truth
  const documentsWithGroundTruth = Array.from(new Set(
    datasetGroundTruths.value.map(gt => gt.documentId)
  )).map(docId => filteredDocuments.value.find(doc => doc.id === docId)).filter(Boolean);
  
  const fileTypes = Array.from(new Set(
    documentsWithGroundTruth.map(doc => {
      const extension = doc!.path.split('.').pop()?.toLowerCase() || 'unknown';
      return extension === 'unknown' ? 'No Extension' : `.${extension}`;
    })
  )).sort();
  
  const fields = extractionConfig.value.data.fields.map(f => f.name);
  
  availableExtractors.value.forEach(extractor => {
    const heatMapMatrix: Array<[number, number, number]> = [];
    
    fileTypes.forEach((fileType, fileTypeIndex) => {
      fields.forEach((fieldName, fieldIndex) => {
        const fieldConfig = extractionConfig.value?.data?.fields.find(f => f.name === fieldName);
        if (!fieldConfig) return;
        
        // Get documents of this file type that have ground truth for this field
        const documentsOfTypeWithGT = documentsWithGroundTruth.filter(doc => {
          const extension = doc!.path.split('.').pop()?.toLowerCase() || 'unknown';
          const type = extension === 'unknown' ? 'No Extension' : `.${extension}`;
          return type === fileType && datasetGroundTruths.value.some(gt => 
            gt.documentId === doc!.id && gt.fieldName === fieldName
          );
        });
        
        let totalCorrect = 0;
        let totalProcessed = 0;
        
        documentsOfTypeWithGT.forEach(doc => {
          const extraction = datasetExtractions.value.find(e => 
            e.documentId === doc!.id && 
            e.model === extractor && 
            e.status === 'completed'
          );
          
          const groundTruth = datasetGroundTruths.value.find(gt => 
            gt.documentId === doc!.id && 
            gt.fieldName === fieldName
          );
          
          if (extraction && groundTruth) {
            totalProcessed++;
            
            // Get extracted value - handle both object structure and direct value
            let extractedValue;
            if (extraction.data && typeof extraction.data === 'object') {
              const fieldData = extraction.data[fieldName];
              if (fieldData && typeof fieldData === 'object' && 'value' in fieldData) {
                extractedValue = fieldData.value;
              } else {
                extractedValue = fieldData; // Direct value
              }
            }
            
            const groundTruthValue = groundTruth.userAnnotation ?? groundTruth.extractedValue;
            
            // Use the calculateCorrectness function consistently
            const correctnessScore = calculateCorrectness(extractedValue, groundTruthValue, fieldConfig.fieldType);
            if (correctnessScore === 100) {  // Only 100% matches are correct
              totalCorrect++;
            }
          }
        });
        
        const successRate = totalProcessed > 0 ? Math.round((totalCorrect / totalProcessed) * 100) : -1;
        // Only add to matrix if we have data
        if (totalProcessed > 0) {
          heatMapMatrix.push([fieldIndex, fileTypeIndex, successRate]);
        }
      });
    });
    
    data[extractor] = {
      matrix: heatMapMatrix,
      fileTypes,
      fields
    };
  });
  
  return data;
});

// Heatmap chart options
const getHeatmapOption = (extractor: string) => {
  const data = heatmapData.value[extractor];
  if (!data) return {};
  
  const { matrix, fileTypes, fields } = data;
  
  return {
    tooltip: {
      position: 'top',
      formatter: function (params: any) {
        const fieldName = fields[params.data[0]];
        const fileType = fileTypes[params.data[1]];
        const successRate = params.data[2];
        return `${fieldName}<br/>${fileType}<br/>Success Rate: ${successRate}%`;
      }
    },
    grid: {
      height: '70%',
      top: '10%',
      left: '20%',
      right: '5%',
      bottom: '20%'
    },
    xAxis: {
      type: 'category',
      data: fields,
      splitArea: {
        show: true
      },
      axisLabel: {
        rotate: 45,
        fontSize: 10,
        interval: 0,
        overflow: 'truncate',
        width: 80
      }
    },
    yAxis: {
      type: 'category',
      data: fileTypes,
      splitArea: {
        show: true
      },
      axisLabel: {
        fontSize: 10,
        overflow: 'truncate',
        width: 100
      }
          },
      visualMap: {
        min: 0,
        max: 100,
        calculable: true,
        orient: 'horizontal',
        left: 'center',
        bottom: '5%',
        inRange: {
          color: ['#d73027', '#f46d43', '#fdae61', '#fee08b', '#d9ef8b', '#a6d96a', '#66bd63', '#1a9850']
        },
        text: ['High Success', 'Low Success'],
        textStyle: {
          fontSize: 11
        }
      },
      series: [{
        name: 'Extraction Success Rate',
        type: 'heatmap',
        data: matrix,
        label: {
          show: true,
          formatter: function(params: any) {
            return params.data[2] + '%';
          },
          fontSize: 10,
          fontWeight: 'bold'
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }]
  };
};
</script>
 <template>
 <div 
    v-if="extractors.length > 0"
    class="grid gap-4 pb-2 border-b border-border sticky top-0 bg-card z-10"
    :style="{ gridTemplateColumns: `repeat(${extractors.length + 1}, minmax(0, 1fr))` }"
        >
          <div class="col-span-1"></div>
          <div v-for="extractor in extractors" :key="extractor.id" class="text-center col-span-1">
            <div class="flex items-center justify-center space-x-3 mb-3">
              <!-- Provider Logo -->
              <ExtractorLogo :extractor="extractor.model || 'unknown'" :size="24" />
              
              <h3 class="font-semibold text-sm text-foreground">{{ extractor.model }}</h3>
              
              <!-- Status Indicator -->
              <div class="flex items-center">
                <Skeleton v-if="extractorStatus[extractor.id] === 'loading'" class="w-5 h-5 rounded animate-pulse" />
                <CheckCircle2 v-else-if="extractorStatus[extractor.id] === 'success'" class="h-5 w-5 text-green-500" />
                <XCircle v-else-if="extractorStatus[extractor.id] === 'error'" class="h-5 w-5 text-red-500" />
                <div v-else class="h-5 w-5"></div>
              </div>
            </div>
            
            <!-- Individual run/rerun button for each extractor -->
            <div class="flex items-center justify-center mb-2">
              <button
                @click="runSingleExtraction(extractor.id)"
                :disabled="isInitializing || extractorStatus[extractor.id] === 'loading' || !extractionConfig"
                class="p-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed transition-colors shadow-sm group"
                :title="`${extractions[extractor.id] ? 'Rerun' : 'Run'} ${extractor.name || extractor.model} extractor`"
              >
                <Play v-if="!extractions[extractor.id]" class="h-4 w-4" />
                <RotateCcw v-else class="h-4 w-4 group-hover:rotate-180 transition-transform duration-300" />
              </button>
            </div>
            
            <p class="text-xs text-muted-foreground mt-1 h-4">
              <span v-if="extractions[extractor.id]?.processing_time">
                {{ Number(extractions[extractor.id]?.processing_time || 0).toFixed(1) }}s processing time
              </span>
            </p>
          </div>
        </div>

</template>

<script setup lang="ts">
import { Extractor } from '@/types'
import { useResourcePool } from '@/services/resourcePool.js'
import { computed } from 'vue'

const {
  extractions
} = useResourcePool()
</script>
<template>
  <span
    :class="[
      'px-2 py-1 text-xs font-medium rounded-md border flex items-center gap-1 w-16 justify-center',
      statusStyles
    ]"
    :title="statusTooltip"
  >
    <div v-if="status === 'running'" class="animate-spin rounded-full h-2 w-2 border-b-2 border-current"></div>
    <div v-else-if="status === 'pending'" class="w-2 h-2 rounded-full bg-current opacity-60"></div>
    <CheckCircle v-else-if="status === 'completed'" class="h-3 w-3" />
    <XCircle v-else-if="status === 'failed'" class="h-3 w-3" />
    <Circle v-else class="h-3 w-3" />
    
    {{ statusText }}
  </span>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { CheckCircle, XCircle, Circle } from 'lucide-vue-next';

interface Props {
  status: 'pending' | 'running' | 'completed' | 'failed' | null;
}

const props = defineProps<Props>();

const statusStyles = computed(() => {
  switch (props.status) {
    case 'pending':
      return 'bg-gray-100 text-gray-700 border-gray-200';
    case 'running':
      return 'bg-blue-100 text-blue-700 border-blue-200';
    case 'completed':
      return 'bg-green-100 text-green-700 border-green-200';
    case 'failed':
      return 'bg-red-100 text-red-700 border-red-200';
    default:
      return 'bg-gray-100 text-gray-500 border-gray-200';
  }
});

const statusText = computed(() => {
  switch (props.status) {
    case 'pending':
      return 'Queued';
    case 'running':
      return 'Running';
    case 'completed':
      return 'Done';
    case 'failed':
      return 'Failed';
    default:
      return 'None';
  }
});

const statusTooltip = computed(() => {
  switch (props.status) {
    case 'pending':
      return 'Extraction is queued and waiting to start';
    case 'running':
      return 'Extraction is currently running';
    case 'completed':
      return 'Extraction completed successfully';
    case 'failed':
      return 'Extraction failed with an error';
    default:
      return 'No extraction has been run for this model';
  }
});
</script>
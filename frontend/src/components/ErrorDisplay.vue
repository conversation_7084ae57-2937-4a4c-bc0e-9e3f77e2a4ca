<template>
  <div class="flex items-center justify-center min-h-96">
    <div class="text-center max-w-md mx-auto">
      <!-- Network Error -->
      <div v-if="errorInfo && errorInfo.type === 'network'" class="p-6 bg-red-50 border border-red-200 rounded-lg">
        <div class="h-12 w-12 mx-auto text-red-400 mb-4">🔌</div>
        <h2 class="text-xl font-semibold text-red-600 mb-2">{{ networkTitle || 'Backend Server Unreachable' }}</h2>
        <p class="text-red-600 mb-4">{{ errorInfo.message }}</p>
        <div class="space-y-2">
          <button 
            v-if="showRetry"
            @click="$emit('retry')"
            class="flex items-center gap-2 mx-auto px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700 transition-colors"
          >
            {{ retryText || 'Try Again' }}
          </button>
          <p class="text-xs text-red-500">Make sure the backend server is running on localhost:8000</p>
        </div>
      </div>

      <!-- Not Found Error -->
      <div v-else-if="errorInfo && errorInfo.type === 'not_found'" class="p-6 bg-gray-50 border border-gray-200 rounded-lg">
        <AlertCircle class="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h2 class="text-xl font-semibold text-gray-600 mb-2">{{ notFoundTitle || 'Resource Not Found' }}</h2>
        <p class="text-gray-600 mb-4">{{ errorInfo.message }}</p>
        <button 
          v-if="showBack"
          @click="$emit('goBack')"
          class="flex items-center gap-2 mx-auto px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors"
        >
          {{ backText || 'Go Back' }}
        </button>
      </div>

      <!-- Server Error -->
      <div v-else-if="errorInfo && errorInfo.type === 'server'" class="p-6 bg-yellow-50 border border-yellow-200 rounded-lg">
        <div class="h-12 w-12 mx-auto text-yellow-400 mb-4">⚠️</div>
        <h2 class="text-xl font-semibold text-yellow-600 mb-2">{{ serverTitle || 'Server Error' }}</h2>
        <p class="text-yellow-600 mb-4">{{ errorInfo.message }}</p>
        <button 
          v-if="showRetry"
          @click="$emit('retry')"
          class="flex items-center gap-2 mx-auto px-4 py-2 text-sm font-medium text-white bg-yellow-600 rounded-md hover:bg-yellow-700 transition-colors"
        >
          {{ retryText || 'Try Again' }}
        </button>
      </div>

      <!-- Generic Error -->
      <div v-else class="p-6 bg-gray-50 border border-gray-200 rounded-lg">
        <AlertCircle class="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h2 class="text-xl font-semibold text-gray-600 mb-2">{{ genericTitle || 'Error' }}</h2>
        <p class="text-gray-600 mb-4">{{ errorInfo?.message || 'An unexpected error occurred.' }}</p>
        <div class="flex gap-2 justify-center">
          <button 
            v-if="showRetry"
            @click="$emit('retry')"
            class="flex items-center gap-2 px-4 py-2 text-sm font-medium text-white bg-gray-600 rounded-md hover:bg-gray-700 transition-colors"
          >
            {{ retryText || 'Try Again' }}
          </button>
          <button 
            v-if="showBack"
            @click="$emit('goBack')"
            class="flex items-center gap-2 px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors"
          >
            {{ backText || 'Go Back' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { AlertCircle } from 'lucide-vue-next';
import type { ErrorInfo } from '../utils/errorUtils';

interface Props {
  errorInfo: ErrorInfo | null;
  networkTitle?: string;
  notFoundTitle?: string;
  serverTitle?: string;
  genericTitle?: string;
  retryText?: string;
  backText?: string;
  showRetry?: boolean;
  showBack?: boolean;
}

withDefaults(defineProps<Props>(), {
  showRetry: true,
  showBack: true,
});

defineEmits<{
  retry: [];
  goBack: [];
}>();
</script> 
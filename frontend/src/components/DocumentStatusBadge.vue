<template>
  <span
    :class="[
      'px-2 py-1 text-xs font-medium rounded-md border flex items-center gap-1 w-auto justify-center',
      statusStyles
    ]"
    :title="statusTooltip"
  >
    <CheckCircle v-if="status === 'approved'" class="h-3 w-3" />
    <XCircle v-else-if="status === 'unapproved'" class="h-3 w-3" />
    <AlertCircle v-else-if="status === 'needs-ground-truth'" class="h-3 w-3" />
    <Clock v-else-if="status === 'pending-review'" class="h-3 w-3" />
    <HelpCircle v-else class="h-3 w-3" />
    
    {{ statusText }}
  </span>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { CheckCircle, XCircle, AlertCircle, Clock, HelpCircle } from 'lucide-vue-next';

interface Props {
  status: 'approved' | 'unapproved' | 'needs-ground-truth' | 'pending-review' | 'unknown';
}

const props = defineProps<Props>();

const statusStyles = computed(() => {
  switch (props.status) {
    case 'approved':
      return 'bg-green-100 text-green-700 border-green-200';
    case 'unapproved':
      return 'bg-red-100 text-red-700 border-red-200';
    case 'needs-ground-truth':
      return 'bg-orange-100 text-orange-700 border-orange-200';
    case 'pending-review':
      return 'bg-yellow-100 text-yellow-700 border-yellow-200';
    default:
      return 'bg-gray-100 text-gray-600 border-gray-200';
  }
});

const statusText = computed(() => {
  switch (props.status) {
    case 'approved':
      return 'Approved';
    case 'unapproved':
      return 'Unapproved';
    case 'needs-ground-truth':
      return 'Needs GT';
    case 'pending-review':
      return 'Pending';
    default:
      return 'Unknown';
  }
});

const statusTooltip = computed(() => {
  switch (props.status) {
    case 'approved':
      return 'Document has been approved and will be included in metrics';
    case 'unapproved':
      return 'Document has been marked as unapproved and excluded from metrics';
    case 'needs-ground-truth':
      return 'Document needs ground truth data before it can be approved';
    case 'pending-review':
      return 'Document has ground truth data and is ready for review';
    default:
      return 'Document status is unknown';
  }
});
</script>
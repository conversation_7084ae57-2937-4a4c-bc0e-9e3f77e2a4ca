<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import { trpc } from '../services';
import type { FieldOption } from '../../../backend/src/services/fieldDiscoveryService';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import FieldTypeIcon from './FieldTypeIcon.vue';

const props = defineProps<{
  documentId: string;
  eventType: 'emission_log' | 'step_execution' | 'delivery';
}>();

const metadata = ref<FieldOption[]>([]);
const inputs = ref<FieldOption[]>([]);
const error = ref<any>(null);
const loading = ref(false);

async function fetchDocumentFields() {
  if (!props.documentId) return;

  loading.value = true;
  error.value = null;
  try {
    const result = await trpc.resources.discoverFieldsForDocument.query({
      documentId: props.documentId,
      eventType: props.eventType,
    });
    metadata.value = result.metadata;
    inputs.value = result.inputs;
  } catch (e) {
    error.value = e;
  } finally {
    loading.value = false;
  }
}

onMounted(fetchDocumentFields);
watch(() => props.documentId, fetchDocumentFields);

</script>

<template>
  <Card>
    <CardHeader>
      <CardTitle>
        Document: {{ documentId }}
        <span class="text-sm text-gray-500 ml-2">({{ eventType }})</span>
      </CardTitle>
    </CardHeader>
    <CardContent>
      <div v-if="loading" class="text-center">
        <p>Loading...</p>
      </div>
      <div v-else-if="error" class="text-red-500">
        <p>An error occurred:</p>
        <pre class="whitespace-pre-wrap break-all">{{ error }}</pre>
      </div>
      <div v-else class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <h3 class="font-semibold mb-2">Metadata Fields</h3>
          <ul v-if="metadata.length" class="space-y-2">
            <li v-for="field in metadata" :key="field.name" class="flex items-center p-2 border rounded-md bg-gray-50/50">
              <FieldTypeIcon :type="field.fieldType" class="mr-2" />
              <span class="font-mono text-sm">{{ field.name }}</span>
            </li>
          </ul>
          <p v-else class="text-gray-500 text-sm">No metadata fields found.</p>
        </div>
        <div>
          <h3 class="font-semibold mb-2">Input Fields</h3>
          <ul v-if="inputs.length" class="space-y-2">
            <li v-for="field in inputs" :key="field.name" class="flex items-center p-2 border rounded-md bg-gray-50/50">
              <FieldTypeIcon :type="field.fieldType" class="mr-2" />
              <span class="font-mono text-sm">{{ field.name }}</span>
            </li>
          </ul>
          <p v-else class="text-gray-500 text-sm">No input fields found.</p>
        </div>
      </div>
    </CardContent>
  </Card>
</template>

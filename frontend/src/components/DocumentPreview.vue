<template>
<div class="w-full h-full flex flex-col bg-gray-50">
  <!-- Content Area -->
  <div class="flex-1 flex items-center justify-center overflow-auto">
    <!-- Loading State -->
    <div v-if="loading" class="flex flex-col items-center gap-4">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
      <p class="text-gray-600">Loading document...</p>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="flex flex-col items-center gap-4 text-center max-w-md">
      <div class="p-4 bg-red-50 rounded-lg border border-red-200">
        <AlertCircle class="w-8 h-8 text-red-500 mx-auto mb-2" />
        <p class="text-red-700 font-medium mb-2">Failed to load document</p>
        <p class="text-red-600 text-sm mb-4">{{ error }}</p>
        <button 
          @click="retryLoad" 
          class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
        >
          Retry
        </button>
      </div>
    </div>
    
    <!-- PDF Content -->
    <div v-else-if="isPDF && pdf" class="w-full h-full">
      <div class="h-full flex flex-col">
        <!-- PDF Controls -->
        <div class="flex items-center justify-between p-4 bg-white border-b border-gray-200">
          <div class="flex items-center space-x-4">
            <!-- Copy Document ID Button -->
            <button 
              v-if="document?.id"
              @click="copyDocumentId" 
              class="p-2 rounded hover:bg-gray-100 transition-colors text-gray-500 hover:text-gray-700"
              title="Copy Document ID"
            >
              <Copy class="w-4 h-4" />
            </button>
            <!-- Show navigation buttons only for multi-page documents -->
            <button 
              v-if="(pages || 0) > 1"
              @click="previousPage" 
              :disabled="currentPage <= 1"
              class="p-2 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
              title="Previous Page"
            >
              <ChevronLeft class="w-4 h-4" />
            </button>
            
            <!-- Always show page indicator -->
            <span class="text-sm text-gray-600 font-medium">
              Page {{ currentPage }} of {{ pages || '...' }}
            </span>
            
            <button 
              v-if="(pages || 0) > 1"
              @click="nextPage" 
              :disabled="currentPage >= (pages || 1)"
              class="p-2 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
              title="Next Page"
            >
              <ChevronRight class="w-4 h-4" />
            </button>
          </div>
          
          <div class="flex items-center space-x-2">
            <button 
              @click="zoomOut" 
              class="p-2 rounded hover:bg-gray-100"
              title="Zoom Out"
            >
              <ZoomOut class="w-4 h-4" />
            </button>
            <span class="text-sm text-gray-600 min-w-[60px] text-center">
              {{ Math.round(scale * 100) }}%
            </span>
            <button 
              @click="zoomIn" 
              class="p-2 rounded hover:bg-gray-100"
              title="Zoom In"
            >
              <ZoomIn class="w-4 h-4" />
            </button>
            <button 
              @click="fitToWidth" 
              class="p-2 rounded hover:bg-gray-100 ml-1"
              title="Fit to Width"
            >
              <span class="text-xs font-medium">Width</span>
            </button>
            <button 
              @click="fitToScreen" 
              class="p-2 rounded hover:bg-gray-100 ml-1"
              title="Fit to Screen"
            >
              <span class="text-xs font-medium">Fit</span>
            </button>
          </div>
        </div>
        
        <!-- PDF Viewer -->
        <div ref="pdfContainerRef" class="flex-1 bg-gray-100 overflow-auto">
          <div class="flex items-center justify-center h-full">
            <VuePDF
              :pdf="pdf"
              :scale="scale"
              :page="currentPage"
              text-layer
              annotation-layer
              @loaded="onPdfLoaded"
              class="max-w-full max-h-full overflow-hidden"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- Image Content -->
    <div v-else-if="isImage && documentUrl" class="h-full flex flex-col">
      <!-- Image Controls (only showing copy button) -->
      <div v-if="document?.id" class="flex items-center justify-between p-4 bg-white border-b border-gray-200">
        <div class="flex items-center space-x-4">
          <!-- Copy Document ID Button -->
          <button 
            @click="copyDocumentId" 
            class="p-2 rounded hover:bg-gray-100 transition-colors text-gray-500 hover:text-gray-700"
            title="Copy Document ID"
          >
            <Copy class="w-4 h-4" />
          </button>
        </div>
        <div class="flex items-center space-x-2">
          <!-- Empty space for symmetry, ImageViewer has its own controls -->
        </div>
      </div>
      
      <div class="flex-1">
        <ImageViewer 
          :image-url="documentUrl"
          :alt="document?.fileName || document?.name || document?.gcsPath || 'Document'"
          :file-size="document?.size || 0"
          @error="onImageError"
        />
      </div>
    </div>

    <!-- Unsupported File Type -->
    <div v-else-if="!loading && document" class="flex flex-col items-center gap-4 text-center p-4">
      <FileText class="w-16 h-16 text-gray-400" />
      <div>
        <p class="text-gray-700 font-semibold mb-2">Could not display '{{ document.fileName || document.name }}'</p>
        <h3 class="text-lg font-medium text-gray-900 mb-2">
          Preview not available for {{ (document.fileName || document.name || '').split('.').pop() }} files
        </h3>
        <p class="text-gray-600 mb-4">
          Supported formats: PDF, PNG, JPG, JPEG, GIF, WebP
        </p>
      </div>
    </div>

    <!-- No Document -->
    <div v-else-if="!document" class="flex flex-col items-center gap-4 text-center">
      <FileText class="w-16 h-16 text-gray-400" />
      <p class="text-gray-600">No document selected</p>
    </div>
  </div>
</div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'
import { 
  FileText, 
  ChevronLeft, 
  ChevronRight, 
  ZoomIn, 
  ZoomOut, 
  AlertCircle,
  Copy
} from 'lucide-vue-next'
import { VuePDF, usePDF } from '@tato30/vue-pdf'
import '@tato30/vue-pdf/style.css'
import ImageViewer from './ImageViewer.vue'
import type { Document } from '@/types'
import { getDocumentStreamingUrl } from '@/services'
import { documentPrefetcher } from '@/services/documentPrefetcher'

// Props
interface Props {
  document: Document | null
  defaultZoom?: 'auto-fit' | 'fit-width' | 'fit-height' | number
}
const props = withDefaults(defineProps<Props>(), {
  defaultZoom: 'auto-fit'
})

// Emits
const emit = defineEmits<{
  'pdf-loaded': [pages: number]
}>()

// Reactive state
const currentPage = ref(1)
const scale = ref(1)
const loading = ref(false)
const error = ref<string | null>(null)
const isManualZoom = ref(false) // Track if user is manually zooming

// PDF container reference
const pdfContainerRef = ref<HTMLElement>()

// Computed properties
const documentUrl = computed(() => {
  if (!props.document) return null
  
  // The document ID (either UUID from DB or filename from GCS)
  const documentIdentifier = props.document.id || (props.document.gcsPath ? props.document.gcsPath.split('/').pop() : null)
  
  if (!documentIdentifier) return null
  
  // Check if we have a prefetched blob URL first
  const cachedBlobUrl = documentPrefetcher.getBlobUrl(documentIdentifier)
  if (cachedBlobUrl) {
    console.log(`🚀 Using prefetched document: ${documentIdentifier}`)
    return cachedBlobUrl
  }
  
  // Fallback to streaming URL
  return getDocumentStreamingUrl(documentIdentifier)
})

const isPDF = computed(() => {
  if (!props.document) return false
  const filename = props.document.fileName || props.document.name || props.document.gcsPath || ''
  return filename.toLowerCase().endsWith('.pdf') || props.document.type === 'application/pdf'
})

const isImage = computed(() => {
  if (!props.document) return false
  // Check multiple possible sources for the filename
  const filename = props.document.fileName || props.document.name || props.document.gcsPath || ''
  const ext = filename.split('.').pop()?.toLowerCase() || ''
  return ['png', 'jpg', 'jpeg', 'gif', 'webp', 'bmp', 'svg'].includes(ext)
})

// PDF Loading
const pdfSource = computed(() => {
  if (isPDF.value && documentUrl.value) {
    return documentUrl.value;
  }
  return null;
});

// Use usePDF hook with conditional source
const { pdf, pages, info } = usePDF(pdfSource);

// PDF Loading state management - only watch when we have valid refs
watch(() => pdf?.value, (p) => {
  if (p && isPDF.value) {
    loading.value = false;
    error.value = null; // Clear error on success
  }
}, { flush: 'sync' });

// Handle PDF loading errors
watch(() => pdfSource.value, (newSource, oldSource) => {
  if (newSource !== oldSource) {
    // Reset error state when source changes
    error.value = null;
    if (newSource && isPDF.value) {
      loading.value = true;
    }
  }
}, { immediate: true });

// Watch for PDF loading errors by monitoring when source exists but pdf doesn't load
let pdfLoadTimeout: ReturnType<typeof setTimeout> | null = null;

watch([() => pdfSource.value, () => pdf?.value], ([source, pdfInstance]) => {
  if (pdfLoadTimeout) {
    clearTimeout(pdfLoadTimeout);
    pdfLoadTimeout = null;
  }
  
  if (source && isPDF.value && !pdfInstance) {
    // Set a timeout to detect failed PDF loading
    pdfLoadTimeout = setTimeout(() => {
      if (!pdf?.value && pdfSource.value && isPDF.value) {
        console.warn('PDF loading timeout or cancelled:', pdfSource.value);
        loading.value = false;
        error.value = 'Failed to load PDF. The file may be corrupted or unavailable.';
      }
    }, 10000); // 10 second timeout
  } else if (pdfInstance) {
    // PDF loaded successfully
    if (pdfLoadTimeout) {
      clearTimeout(pdfLoadTimeout);
      pdfLoadTimeout = null;
    }
    loading.value = false;
    error.value = null;
  }
}, { immediate: true });

// Cleanup timeout on unmount
onUnmounted(() => {
  if (pdfLoadTimeout) {
    clearTimeout(pdfLoadTimeout);
  }
});

// Global error handler to catch PDF errors and handle them gracefully
if (typeof window !== 'undefined') {
  const originalError = console.error;
  const originalWarn = console.warn;
  
  const handlePdfError = (...args: any[]) => {
    const error = args[0];
    const message = typeof error === 'string' ? error : error?.message || '';
    
    // Check for PDF-related errors that can be safely ignored
    if (
      // TextLayer cancellation errors
      (error && error.name === 'AbortException' && 
       (message.includes('TextLayer task cancelled') || message.includes('cancelled'))) ||
      // String-based TextLayer errors
      message.includes('TextLayer task cancelled') ||
      message.includes('Uncought in Promise TextLayer task cancelled') ||
      // Undefined function errors from PDF.js
      message.includes('TT: undefined function') ||
      message.includes('undefined function: 32') ||
      // Other PDF.js internal warnings
      (typeof error === 'string' && error.includes('TT: undefined function')) ||
      // Document prefetching 404 errors (expected when files don't exist in GCS)
      message.includes('Failed to prefetch document') && message.includes('404 Not Found')
    ) {
      // These are expected PDF.js internal issues or missing files, suppress them
      return;
    }
    
    // Call original error handler for other errors
    originalError(...args);
  };

  const handlePdfWarn = (...args: any[]) => {
    const warning = args[0];
    const message = typeof warning === 'string' ? warning : warning?.message || '';
    
    // Suppress PDF.js warnings about undefined functions
    if (message.includes('TT: undefined function') || 
        message.includes('undefined function: 32')) {
      return;
    }
    
    // Call original warn handler for other warnings
    originalWarn(...args);
  };

  // Handle unhandled promise rejections (for TextLayer cancellation)
  const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
    const reason = event.reason;
    const message = typeof reason === 'string' ? reason : reason?.message || '';
    
    if (
      message.includes('TextLayer task cancelled') ||
      message.includes('TT: undefined function') ||
      (reason && reason.name === 'AbortException' && message.includes('cancelled'))
    ) {
      // Prevent the error from appearing in console
      event.preventDefault();
      return;
    }
  };

  // Handle general window errors
  const handleWindowError = (event: ErrorEvent) => {
    const message = event.message || '';
    
    if (
      message.includes('TextLayer task cancelled') ||
      message.includes('TT: undefined function') ||
      message.includes('undefined function: 32')
    ) {
      // Prevent the error from appearing in console
      event.preventDefault();
      return false;
    }
    return true;
  };
  
  // Override console methods and add event listeners
  console.error = handlePdfError;
  console.warn = handlePdfWarn;
  window.addEventListener('unhandledrejection', handleUnhandledRejection);
  window.addEventListener('error', handleWindowError);
  
  onUnmounted(() => {
    // Restore original console methods and remove event listeners
    console.error = originalError;
    console.warn = originalWarn;
    window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    window.removeEventListener('error', handleWindowError);
  });
}


// Methods
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`
}

const onImageLoad = () => {
  loading.value = false
  error.value = null
}

const onImageError = (event: Event) => {
  const target = event.target as HTMLImageElement
  const src = target?.src || documentUrl.value
  
  console.error('Image failed to load:', src)
  
  loading.value = false
}

const previousPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--
  }
}

const nextPage = () => {
  const maxPages = pages.value || 1
  if (currentPage.value < maxPages) {
    currentPage.value++
  }
}

const retryLoad = () => {
  error.value = null
  // The usePDF hook should retry automatically when the URL is valid again.
  // For images, we can add a cache-buster.
  if (isImage.value) {
    const img = document.querySelector('.document-preview-image') as HTMLImageElement
    if (img) {
      img.src = `${documentUrl.value}?retry=${Date.now()}`
    }
  }
}

const copyDocumentId = async () => {
  if (!props.document?.id) return
  
  try {
    await navigator.clipboard.writeText(props.document.id)
    // Could add a toast notification here if available
    console.log('Document ID copied to clipboard:', props.document.id)
  } catch (err) {
    console.error('Failed to copy document ID:', err)
    // Fallback for older browsers
    const textArea = document.createElement('textarea')
    textArea.value = props.document.id
    document.body.appendChild(textArea)
    textArea.focus()
    textArea.select()
    try {
      document.execCommand('copy')
      console.log('Document ID copied to clipboard (fallback):', props.document.id)
    } catch (fallbackErr) {
      console.error('Failed to copy with fallback:', fallbackErr)
    }
    document.body.removeChild(textArea)
  }
}

// Zoom functions
const zoomIn = () => {
  isManualZoom.value = true
  scale.value = Math.min(scale.value + 0.1, 3.0)
}

const zoomOut = () => {
  isManualZoom.value = true
  scale.value = Math.max(scale.value - 0.1, 0.4)
}

const fitToWidth = () => {
  isManualZoom.value = false // Reset manual zoom flag
  autoFitPdf('fit-width') // Trigger fit-width
}

const fitToScreen = () => {
  isManualZoom.value = false // Reset manual zoom flag
  autoFitPdf('auto-fit') // Trigger auto-fit
}

// Auto-fit PDF to container
const autoFitPdf = async (mode?: 'auto-fit' | 'fit-width' | 'fit-height') => {
  // Don't auto-fit if user has manually zoomed
  if (!isPDF.value || !pdf.value || !pdfContainerRef.value || isManualZoom.value) {
    return
  }
  
  // Use provided mode or default from props
  const fitMode = mode || props.defaultZoom
  
  // Wait for the next tick to ensure DOM is updated
  await nextTick()
  
  try {
    // Get container dimensions (subtract padding and controls height)
    const container = pdfContainerRef.value
    const containerRect = container.getBoundingClientRect()
    const availableWidth = containerRect.width
    const availableHeight = containerRect.height
    
    // Use a heuristic approach since we can't directly get PDF dimensions
    // Standard PDF page is typically 595x842 points (A4 at 72 DPI)
    // We'll use these default dimensions for scaling calculation
    const defaultPdfWidth = 595
    const defaultPdfHeight = 842
    
    // Calculate scale based on fit mode
    const scaleX = availableWidth / defaultPdfWidth
    const scaleY = availableHeight / defaultPdfHeight
    
    let optimalScale: number
    if (typeof fitMode === 'number') {
      optimalScale = fitMode // Use explicit scale value
    } else if (fitMode === 'fit-width') {
      optimalScale = Math.min(scaleX, 2.0) // Fit to width, don't scale beyond 200%
    } else if (fitMode === 'fit-height') {
      optimalScale = Math.min(scaleY, 2.0) // Fit to height, don't scale beyond 200%
    } else {
      optimalScale = Math.min(scaleX, scaleY, 2.0) // Fit both dimensions (auto-fit)
    }
    
    // Set minimum scale to ensure readability
    const finalScale = Math.max(optimalScale, 0.3)
    
    scale.value = finalScale
  } catch (error) {
    console.warn('Failed to auto-fit PDF:', error)
    // Fallback to default scale
    scale.value = 1
  }
}

// Handle PDF loaded event
const onPdfLoaded = () => {
  emit('pdf-loaded', pages.value || 0)
  // Use the default zoom setting from props
  autoFitPdf()
}

// Reset state when document changes
watch(() => props.document, (newDoc, oldDoc) => {
  // Clear any pending PDF load timeout when document changes
  if (pdfLoadTimeout) {
    clearTimeout(pdfLoadTimeout);
    pdfLoadTimeout = null;
  }
  
  currentPage.value = 1
  scale.value = 1
  isManualZoom.value = false // Reset manual zoom flag for new documents
  error.value = null
  
  if (newDoc) {
    // Only set loading for PDFs, images handle their own loading state
    if (isPDF.value) {
      loading.value = true
    } else {
      loading.value = false
    }
  } else {
    loading.value = false
  }
}, { immediate: true })

// ResizeObserver to re-fit PDF when container size changes
let resizeObserver: ResizeObserver | null = null
let resizeTimeout: ReturnType<typeof setTimeout> | null = null

onMounted(() => {
  // Watch for container changes and set up resize observer
  watch(() => pdfContainerRef.value, (container) => {
    if (container && 'ResizeObserver' in window) {
      resizeObserver = new ResizeObserver(() => {
        if (isPDF.value && pdf.value) {
          // Debounce the auto-fit to avoid excessive calls during resize
          if (resizeTimeout) clearTimeout(resizeTimeout)
          resizeTimeout = setTimeout(() => {
            autoFitPdf()
          }, 150)
        }
      })
      resizeObserver.observe(container)
    }
  }, { immediate: true })
})

onUnmounted(() => {
  if (resizeObserver) {
    resizeObserver.disconnect()
  }
  if (resizeTimeout) {
    clearTimeout(resizeTimeout)
  }
})

</script>

 

// Import types from backend tRPC router (these will be auto-generated)
export type { AppRouter } from '../../../backend/src/routers/index.js';

// Re-export the Resource types from backend
export type {
  Resource,
  Dataset,
  Document,
  ExtractionConfig,
  ExtractionField,
  Extraction,
  GroundTruth,
  UserAnnotation,
  Extractor,
  ExtractionConfigData,
  ExtractedValue
} from '../../../backend/src/types/resource.js';

<template>
  <div v-if="isLoading" class="flex items-center justify-center min-h-96">
    <div class="text-center">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
      <p class="text-muted-foreground">Loading dataset details...</p>
    </div>
  </div>
  
  <div v-else-if="error" class="text-center py-12">
    <div class="text-destructive mb-4">
      <svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
      </svg>
    </div>
    <h2 class="text-xl font-semibold text-foreground mb-2">Error Loading Dataset</h2>
    <p class="text-muted-foreground mb-4">{{ error }}</p>
    <button 
      @click="router.push('/')"
      class="px-4 py-2 bg-primary text-primary-foreground rounded hover:bg-primary/90 transition-colors"
    >
      Back to Datasets
    </button>
  </div>

  <div v-else-if="dataset && extractionConfig" class="space-y-8">
    <!-- Header Section -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold text-foreground">{{ dataset.name }}</h1>
        <p class="text-muted-foreground mt-2 text-lg">{{ dataset.description || 'Dataset configuration and document management' }}</p>
      </div>
    </div>

    <!-- Tab Navigation -->
    <div class="border-b border-border">
      <nav class="-mb-px flex space-x-8">
        <button
          @click="activeTab = 'documents'"
          :class="[
            'py-2 px-1 border-b-2 font-medium text-sm transition-colors',
            activeTab === 'documents'
              ? 'border-primary text-primary'
              : 'border-transparent text-muted-foreground hover:text-foreground hover:border-border'
          ]"
        >
          <div class="flex items-center gap-2">
            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            Documents
            <span v-if="datasetDocuments.length > 0" class="bg-muted text-muted-foreground px-2 py-0.5 rounded-full text-xs">
              {{ datasetDocuments.length }}
            </span>
          </div>
        </button>
        <button
          @click="activeTab = 'summary'"
          :class="[
            'py-2 px-1 border-b-2 font-medium text-sm transition-colors',
            activeTab === 'summary'
              ? 'border-primary text-primary'
              : 'border-transparent text-muted-foreground hover:text-foreground hover:border-border'
          ]"
        >
          <div class="flex items-center gap-2">
            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
            Summary
          </div>
        </button>
        <button
          @click="activeTab = 'configuration'"
          :class="[
            'py-2 px-1 border-b-2 font-medium text-sm transition-colors',
            activeTab === 'configuration'
              ? 'border-primary text-primary'
              : 'border-transparent text-muted-foreground hover:text-foreground hover:border-border'
          ]"
        >
          <div class="flex items-center gap-2">
            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            </svg>
            Configuration
            <div v-if="isConfigInvalid" class="w-2 h-2 rounded-full bg-destructive" title="Configuration is invalid"></div>
            <div v-if="saveStatus" :class="[
              'w-2 h-2 rounded-full',
              saveStatus === 'saving' ? 'bg-yellow-500 animate-pulse' : 
              saveStatus === 'saved' ? 'bg-green-500' : 
              saveStatus === 'error' ? 'bg-red-500' : 'bg-gray-300'
            ]" :title="getSaveStatusText()"></div>
          </div>
        </button>
      </nav>
    </div>

    <!-- Tab Content -->
    <div class="tab-content">
      <!-- Documents Tab -->
      <div v-show="activeTab === 'documents'" class="tab-panel">
        <DocumentsDataTable
          :documents="datasetDocuments"
          :config="extractionConfig as any"
          :available-extractors="availableExtractors"
          :extracting-docs="extractingDocs"
          :dataset-id="dataset.id"
          :is-loading-docs="isLoadingDocs"
          :extracted-count="extractedCount"
          :extraction-tasks-to-start="extractionTasksToStart"
          :approval-summary="approvalSummary"
          :run-batch-extraction="runBatchExtraction"
          :get-approval-status-text="getApprovalStatusText"
          :get-document-accuracies="getDocumentAccuracies"
          :has-extractor-run="hasExtractorRun"
          :get-extraction-status="getExtractionStatus"
        />
      </div>

      <!-- Summary Tab -->
      <div v-show="activeTab === 'summary'" class="tab-panel">
        <ExtractionSummaryTab
          v-if="activeTab === 'summary'"
          :dataset-id="route.params.id as string"
        />
      </div>

      <!-- Configuration Tab -->
      <div v-show="activeTab === 'configuration'" class="tab-panel h-[calc(100vh-12rem)]">
        <div v-if="isConfigInvalid" class="bg-destructive/10 border border-destructive text-destructive text-sm rounded-lg p-4 mb-4">
          <p>The configuration is invalid. Please ensure all extractors have a provider and model, and all fields have a name.</p>
        </div>
        <div class="flex gap-8 h-full">
          <!-- Dataset Information - Fixed height -->
          <div class="w-1/2 flex-shrink-0">
            <div class="bg-card border border-border rounded-lg shadow-sm h-full flex flex-col">
              <div class="p-6 border-b border-border flex-shrink-0">
                <h2 class="text-xl font-semibold text-foreground">Dataset Information</h2>
              </div>
              <div class="p-6 space-y-4 flex-grow">
                <div>
                  <label class="block text-sm font-medium text-foreground mb-1">Name</label>
                  <input 
                    v-model="editableDataset.name" 
                    type="text" 
                    @input="debouncedSave"
                    class="w-full px-3 py-2 border border-input rounded-md bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-input"
                  />
                </div>
                <div class="flex-grow flex flex-col">
                  <label class="block text-sm font-medium text-foreground mb-1">SQL Query</label>
                  <SqlEditor
                    v-model="editableDataset.query"
                    :height="600"
                    placeholder="SELECT * FROM documents WHERE..."
                    @change="debouncedSave"
                  />
                </div>
              </div>
            </div>
          </div>
          
          <!-- Extraction Configuration - Scrollable -->
          <div class="w-1/2 flex-shrink-0 overflow-y-auto pr-2">
            <ExtractionConfigurator
              :config="editableConfig.data"
              @update:config="handleConfigUpdate"
            />
          </div>
        </div>
      </div>
    </div>
  </div>

  <div v-else class="text-center py-12">
    <div class="text-muted-foreground mb-4">
      <svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
      </svg>
    </div>
    <h3 class="text-lg font-medium text-foreground mb-2">Dataset not found</h3>
    <p class="text-muted-foreground mb-4">The requested dataset could not be found.</p>
    <button 
      @click="router.push('/')"
      class="px-4 py-2 bg-primary text-primary-foreground rounded hover:bg-primary/90 transition-colors"
    >
      Back to Datasets
    </button>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useResourcePool } from '../services/resourcePool.js';
import type { Dataset, Document, ExtractionConfig } from '../types/index.js';
import DocumentsDataTable from '@/components/DocumentsDataTable.vue';
import ExtractionSummaryTab from '@/components/ExtractionSummaryTab.vue';
import SqlEditor from '@/components/SqlEditor.vue';
import ExtractionConfigurator from '@/components/ExtractionConfigurator.vue';

const route = useRoute();
const router = useRouter();

// Get ResourcePool composable
const {
  datasets,
  extractionConfigs,
  documents,
  extractions,
  groundTruths,
  isBootstrapped,
  isLoading: poolIsLoading,
  error: poolError,
  getDocumentsByDataset,
  getExtractionsByDocument,
  refreshDataset,
  updateDataset,
  updateExtractionConfig,
  getDataset,
  getExtractionConfig,
  runExtractions,
} = useResourcePool();

// Component state
const activeTab = ref<'documents' | 'summary' | 'configuration'>('documents');
const isSaving = ref(false);
const saveStatus = ref<'saving' | 'saved' | 'error' | null>(null);
const saveTimeout = ref<number | null>(null);

// Get dataset and config from the pool
const datasetId = computed(() => route.params.id as string);

const dataset = computed(() => {
  if (!isBootstrapped.value) return null;
  return getDataset(datasetId.value);
});

const extractionConfig = computed(() => {
  if (!isBootstrapped.value || !dataset.value?.extractionConfigId) return null;
  return getExtractionConfig(dataset.value.extractionConfigId);
});

const datasetDocuments = ref<Document[]>([]);

const getSaveStatusText = () => {
  switch (saveStatus.value) {
    case 'saving': return 'Saving changes...';
    case 'saved': return 'All changes saved';
    case 'error': return 'Error saving changes';
    default: return '';
  }
};

// Editable copies of data
const editableDataset = ref<Dataset>({
  id: '',
  name: '',
  description: null,
  query: '',
  version: 1,
  documentCount: 0,
  status: 'ready',
  createdAt: '',
  updatedAt: ''
});

const editableConfig = ref<ExtractionConfig>({
  id: '',
  data: {
    fields: [],
    extractors: []
  },
  version: 1,
  createdAt: '',
  updatedAt: ''
});

// Loading and error state
const isLoading = computed(() => {
  return !isBootstrapped.value || poolIsLoading.value;
});

const error = computed(() => {
  if (poolError.value) return poolError.value;
  if (isBootstrapped.value && !dataset.value) return 'Dataset not found';
  if (isBootstrapped.value && dataset.value && !extractionConfig.value) return 'Extraction configuration not found';
  return null;
});

// Watch for dataset updates (e.g., after a query change and re-sync)
watch(() => dataset.value?.updatedAt, (newVal, oldVal) => {
  if (newVal && newVal !== oldVal) {
    console.log('Dataset has been updated, re-fetching documents...');
    loadDocuments(true);
  }
});

// Initialize editable data when resource pool data changes
watch([dataset, extractionConfig], ([newDataset, newConfig]) => {
  if (newDataset) {
    editableDataset.value = { ...newDataset };
  }
  if (newConfig) {
    editableConfig.value = JSON.parse(JSON.stringify(newConfig));
  }
}, { immediate: true });

const handleConfigUpdate = (newConfigData: any) => {
  if (editableConfig.value) {
    editableConfig.value.data = newConfigData;
    debouncedSave();
  }
};


// ...

const loadDocuments = async (force = false) => {
  if (!datasetId.value) return;
  isLoadingDocs.value = true;
  try {
    // Use getDocumentsByDataset from resourcePool
    datasetDocuments.value = await getDocumentsByDataset(datasetId.value, force);
  } catch (err) {
    console.error('Failed to fetch documents:', err);
    datasetDocuments.value = [];
  } finally {
    isLoadingDocs.value = false;
  }
};

// Computed properties for document statistics
const approvalSummary = computed(() => {
  const summary = {
    approved: 0,
    pending: 0,
    notReviewed: 0,
  };
  
  datasetDocuments.value.forEach(doc => {
    if (doc.isApproved === true) {
      summary.approved++;
    } else if (doc.isApproved === false) {
      summary.pending++;
    } else {
      summary.notReviewed++;
    }
  });
  
  return summary;
});

const extractedCount = computed(() => {
  return datasetDocuments.value.filter(doc => {
    // Check if document has any extractions
    const docExtractions = getExtractionsByDocument(doc.id);
    return docExtractions.length > 0;
  }).length;
});

const extractionTasksToStart = computed(() => {
  if (!extractionConfig.value?.data?.extractors) return 0;
  
  let tasksToStart = 0;
  const configExtractors = extractionConfig.value.data.extractors.map(e => e.model);
  
  datasetDocuments.value.forEach(doc => {
    configExtractors.forEach(extractor => {
      const status = getExtractionStatus(doc.id, extractor);
      if (status === null || status === 'failed') {
        tasksToStart++;
      }
    });
  });
  
  return tasksToStart;
});

const isLoadingDocs = ref(false);
const extractingDocs = ref(new Set<string>());

// Helper functions for document data
const getApprovalStatusText = (docId: string): string => {
  const doc = datasetDocuments.value.find(d => d.id === docId);
  if (!doc) return 'Unknown';
  
  if (doc.isApproved === true) return 'Approved';
  if (doc.isApproved === false) return 'Unapproved';
  return 'Not Reviewed';
};

const getDocumentAccuracies = (docId: string): Array<{ extractor: string; accuracy: number }> => {
  const docExtractions = getExtractionsByDocument(docId);
  const docGroundTruths = groundTruths.value.filter(gt => gt.documentId === docId);
  
  if (!extractionConfig.value?.data?.fields) return [];
  
  const totalFields = extractionConfig.value.data.fields.length;
  if (totalFields === 0) return [];
  
  return docExtractions
    .filter(ext => ext.status === 'completed')
    .map(ext => {
      let perfectFields = 0;
      
      // Count how many fields were extracted with perfect accuracy (100% match)
      extractionConfig.value!.data.fields.forEach(field => {
        // Handle different extraction data structures
        let extractedValue;
        const fieldData = ext.data[field.name];
        
        if (fieldData === null || fieldData === undefined) {
          extractedValue = null;
        } else if (typeof fieldData === 'object' && fieldData.hasOwnProperty('value')) {
          extractedValue = fieldData.value;
        } else {
          extractedValue = fieldData; // Direct value storage
        }
        
        const groundTruth = docGroundTruths.find(gt => gt.fieldName === field.name);
        let isFieldPerfect = false;
        
        // If there's a ground truth, compare against it for perfect match
        if (groundTruth) {
          // If field is marked as not extractable, null/empty extraction is correct
          if (groundTruth.markedNotExtractable) {
            if (extractedValue === null || extractedValue === undefined || extractedValue === '') {
              isFieldPerfect = true;
            }
          } else {
            // Normal comparison for extractable fields
            const gtValue = groundTruth.userAnnotation !== undefined ? groundTruth.userAnnotation : groundTruth.extractedValue;
            
            // Perfect match: exact equality
            if (extractedValue === gtValue) {
              isFieldPerfect = true;
            } 
            // Both null/empty
            else if ((extractedValue === null || extractedValue === '') && 
                     (gtValue === null || gtValue === '')) {
              isFieldPerfect = true;
            } 
            // String comparison for type differences (must be exact match)
            else {
              const extractedStr = extractedValue?.toString() || '';
              const gtStr = gtValue?.toString() || '';
              if (extractedStr === gtStr && extractedStr !== '') {
                isFieldPerfect = true;
              }
            }
          }
        } else {
          // If no ground truth, consider perfect if we extracted a non-null/non-empty value
          if (extractedValue !== null && extractedValue !== undefined && extractedValue !== '') {
            isFieldPerfect = true;
          }
        }
        
        if (isFieldPerfect) {
          perfectFields++;
        }
      });
      
      // Return percentage of fields that were perfectly extracted
      const accuracy = (perfectFields / totalFields) * 100;
      return { extractor: ext.model, accuracy: Math.round(accuracy) };
    });
};

const hasExtractorRun = (docId: string, extractor: string): boolean => {
  const docExtractions = getExtractionsByDocument(docId);
  return docExtractions.some(ext => ext.model === extractor);
};

const getExtractionStatus = (docId: string, extractor: string): 'pending' | 'running' | 'completed' | 'failed' | null => {
  const docExtractions = getExtractionsByDocument(docId);
  const extraction = docExtractions.find(ext => ext.model === extractor);
  
  if (!extraction) return null;
  
  return extraction.status as 'pending' | 'running' | 'completed' | 'failed';
};

const availableExtractors = computed(() => {
  return extractionConfig.value?.data?.extractors?.map(e => e.model) || [];
});

const runBatchExtraction = async (includeCompleted: boolean) => {
  if (!extractionConfig.value?.id) return;
  
  const documentIds = datasetDocuments.value.map(doc => doc.id);
  const extractors = extractionConfig.value.data.extractors?.map(e => e.model) || [];
  
  try {
    await runExtractions(documentIds, extractionConfig.value.id, extractors);
  } catch (error) {
    console.error('Failed to run batch extraction:', error);
  }
};

// Configuration validation
const isConfigInvalid = computed(() => {
  if (!extractionConfig.value?.data) return false;
  
  const { extractors = [], fields = [] } = extractionConfig.value.data;
  
  // Check if all extractors have required properties
  const hasInvalidExtractors = extractors.some(extractor => 
    !extractor.model || typeof extractor.model !== 'string'
  );
  
  // Check if all fields have required properties
  const hasInvalidFields = fields.some(field => 
    !field.name || typeof field.name !== 'string'
  );
  
  return hasInvalidExtractors || hasInvalidFields;
});

// Debounced save function
const debouncedSave = (() => {
  let timeout: number | null = null;
  return () => {
    if (timeout) clearTimeout(timeout);
    timeout = window.setTimeout(async () => {
      try {
        saveStatus.value = 'saving';
        
        // Save dataset changes
        if (dataset.value && JSON.stringify(editableDataset.value) !== JSON.stringify(dataset.value)) {
          await updateDataset(dataset.value.id, editableDataset.value);
        }
        
        // Save config changes
        if (extractionConfig.value && JSON.stringify(editableConfig.value) !== JSON.stringify(extractionConfig.value)) {
          await updateExtractionConfig(extractionConfig.value.id, editableConfig.value.data);
        }
        
        saveStatus.value = 'saved';
        
        // Clear saved status after 2 seconds
        if (saveTimeout.value) clearTimeout(saveTimeout.value);
        saveTimeout.value = window.setTimeout(() => {
          saveStatus.value = null;
        }, 2000);
        
      } catch (error) {
        console.error('Save failed:', error);
        saveStatus.value = 'error';
      }
    }, 500);
  };
})();

onMounted(() => {
  loadDocuments();
});

// ...

watch(() => dataset.value?.updatedAt, (newVal, oldVal) => {
  if (newVal && newVal !== oldVal) {
    console.log('Dataset has been updated, re-fetching documents...');
    loadDocuments(true);
  }
});
</script>
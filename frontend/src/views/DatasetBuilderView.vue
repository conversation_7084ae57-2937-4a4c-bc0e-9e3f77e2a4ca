<template>
  <div class="h-screen bg-gray-50 flex flex-col">
    <!-- Header -->
    <div class="bg-white border-b flex-shrink-0">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center py-4">
          <div class="flex items-center space-x-4">
            <button
              @click="$router.push('/datasets')"
              class="text-gray-500 hover:text-gray-700 transition-colors"
            >
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
              </svg>
            </button>
            <h1 class="text-2xl font-bold text-gray-900">Configure Dataset Query</h1>
          </div>
          <div class="flex items-center space-x-4">
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 flex-grow flex flex-col w-full overflow-y-auto pb-24">
      <!-- Step 1: Site Selection -->
      <div v-if="currentStep === 1" class="space-y-6 py-8">
        <h2 class="text-xl font-semibold">Select Site</h2>
        <div v-if="isLoadingSites" class="flex justify-center py-12">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
        <div v-else-if="sites.length === 0" class="text-center py-12 text-gray-500">
          No sites available
        </div>
        <div v-else class="bg-white rounded-lg shadow overflow-hidden">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead @click="sortBy('name')" class="cursor-pointer">
                  Name
                  <span v-if="sortKey === 'name'">{{ sortOrder === 'asc' ? ' ▲' : ' ▼' }}</span>
                </TableHead>
                <TableHead @click="sortBy('stepExecutions')" class="cursor-pointer">
                  Step Executions
                  <span v-if="sortKey === 'stepExecutions'">{{ sortOrder === 'asc' ? ' ▲' : ' ▼' }}</span>
                </TableHead>
                <TableHead @click="sortBy('deliveries')" class="cursor-pointer">
                  Deliveries
                  <span v-if="sortKey === 'deliveries'">{{ sortOrder === 'asc' ? ' ▲' : ' ▼' }}</span>
                </TableHead>
                <TableHead @click="sortBy('emissionLogs')" class="cursor-pointer">
                  Emission Logs
                  <span v-if="sortKey === 'emissionLogs'">{{ sortOrder === 'asc' ? ' ▼' : ' ▲' }}</span>
                </TableHead>
                <TableHead @click="sortBy('total')" class="cursor-pointer">
                  Total Events
                  <span v-if="sortKey === 'total'">{{ sortOrder === 'asc' ? ' ▲' : ' ▼' }}</span>
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow 
                v-for="site in sortedSites" 
                :key="site.id" 
                @click="selectedSite = site"
                class="cursor-pointer"
                :class="{ 'bg-blue-50': selectedSite?.id === site.id }"
              >
                <TableCell class="font-medium">{{ site.name }}</TableCell>
                <TableCell>{{ site.eventCounts.stepExecutions }}</TableCell>
                <TableCell>{{ site.eventCounts.deliveries }}</TableCell>
                <TableCell>{{ site.eventCounts.emissionLogs }}</TableCell>
                <TableCell class="font-semibold">{{ site.eventCounts.stepExecutions + site.eventCounts.deliveries + site.eventCounts.emissionLogs }}</TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>
      </div>

      <!-- Step 2: Event Type & Sub-type Selection -->
      <div v-if="currentStep === 2" class="space-y-6">
        <h2 class="text-xl font-semibold">Select Event Type</h2>
        <div v-if="isLoadingEventTypes" class="flex justify-center py-12">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
        <div v-else-if="eventTypes.length === 0" class="text-center py-12 text-gray-500">
          No event types available for this site
        </div>
        <div v-else class="space-y-6">
          <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            <div 
              v-for="eventType in eventTypes" 
              :key="eventType.type"
              @click="selectedEventType = eventType"
              class="bg-white border rounded-lg p-6 cursor-pointer transition-all duration-200 ease-in-out"
              :class="{
                'border-blue-600 bg-blue-50 ring-2 ring-blue-500': selectedEventType?.type === eventType.type,
                'border-gray-200 hover:border-blue-400 hover:shadow-md': selectedEventType?.type !== eventType.type
              }"
            >
              <h3 class="text-lg font-semibold">{{ eventType.displayName }}</h3>
              <p class="text-sm text-gray-600 mt-2">
                {{ eventType.configCount }} {{ eventType.type === 'step_execution' ? 'configurations' : 'items' }} available
                <span v-if="eventType.fileCount" class="text-gray-500">• {{ eventType.fileCount }} files</span>
              </p>
              <div class="mt-4">
                <p class="text-xs text-gray-500 mb-2">Sample configurations:</p>
                <div class="space-y-1">
                  <div v-for="config in eventType.sampleConfigs.slice(0, 3)" :key="config.id" class="text-xs text-gray-600">
                    • {{ config.name }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Step Execution Type Selection -->
          <div v-if="selectedEventType?.type === 'step_execution'" class="bg-white border rounded-lg p-6">
            <h3 class="text-lg font-semibold mb-4">Select Step Execution Types</h3>
            <div v-if="isLoadingStepExecutionTypes" class="flex justify-center py-6">
              <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            </div>
            <div v-else-if="stepExecutionTypes.length === 0" class="text-center py-6 text-gray-500">
              No step execution types available for this site
            </div>
            <div v-else class="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              <div
                v-for="stepType in stepExecutionTypes"
                :key="stepType.type"
                @click="toggleStepExecutionType(stepType)"
                class="bg-white border rounded-lg p-4 cursor-pointer transition-all duration-200 ease-in-out"
                :class="{
                  'border-blue-600 bg-blue-50 ring-2 ring-blue-500': selectedStepExecutionTypes.some(t => t.type === stepType.type),
                  'border-gray-200 hover:border-blue-400 hover:shadow-md': !selectedStepExecutionTypes.some(t => t.type === stepType.type)
                }"
              >
                <h4 class="font-semibold">{{ stepType.displayName }}</h4>
                <p class="text-sm text-gray-600 mt-1">{{ stepType.documentCount }} documents</p>
              </div>
            </div>
          </div>


          <!-- Delivery Config Selection -->
          <div v-if="selectedEventType?.type === 'delivery'" class="bg-white border rounded-lg p-6">
            <h3 class="text-lg font-semibold mb-4">Select Delivery Configurations</h3>
            <div v-if="isLoadingDeliveryConfigs" class="flex justify-center py-6">
              <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            </div>
            <div v-else-if="deliveryConfigs.length === 0" class="text-center py-6 text-gray-500">
              No delivery configurations available
            </div>
            <div v-else class="grid gap-3 md:grid-cols-2 lg:grid-cols-3">
              <div
                v-for="config in deliveryConfigs"
                :key="config.id"
                @click="toggleDeliveryConfig(config)"
                class="bg-white border rounded-lg p-4 cursor-pointer transition-all duration-200 ease-in-out"
                :class="{
                  'border-blue-600 bg-blue-50 ring-2 ring-blue-500': isDeliveryConfigSelected(config),
                  'border-gray-200 hover:border-blue-400 hover:shadow-md': !isDeliveryConfigSelected(config)
                }"
              >
                <div class="flex items-center justify-between">
                  <div class="flex-1 min-w-0">
                    <h4 class="font-semibold truncate">{{ config.name }}</h4>
                    <p class="text-sm text-gray-600 mt-1">{{ config.documentCount }} documents</p>
                  </div>
                  <div v-if="isDeliveryConfigSelected(config)" class="ml-2 text-blue-600">
                    ✓
                  </div>
                </div>
              </div>
            </div>
            <div v-if="selectedDeliveryConfigs.length > 0" class="mt-4 p-3 bg-blue-50 rounded-lg">
              <p class="text-sm text-blue-800">
                Selected {{ selectedDeliveryConfigs.length }} configuration{{ selectedDeliveryConfigs.length === 1 ? '' : 's' }}
                ({{ selectedDeliveryConfigs.reduce((sum, config) => sum + config.documentCount, 0) }} total documents)
              </p>
            </div>
          </div>

          <!-- Document Type Selection -->
          <div v-if="selectedEventType" class="bg-white border rounded-lg p-6">
            <h3 class="text-lg font-semibold mb-4">Select Document Types</h3>
            <div v-if="isLoadingDocumentTypes" class="flex justify-center py-6">
              <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            </div>
            <div v-else-if="documentTypes.length === 0" class="text-center py-6 text-gray-500">
              <p>No document types found for this selection</p>
              <p class="text-xs mt-2">Site: {{ selectedSite?.name }}, Event: {{ selectedEventType?.type }}</p>
            </div>
            <div v-else class="space-y-3">
              <p class="text-sm text-gray-600">
                Choose which file types to include in your dataset.
              </p>
              <div class="flex items-center justify-between mb-3">
                <button
                  @click="toggleAllDocumentTypes"
                  class="text-sm text-blue-600 hover:text-blue-800 font-medium"
                >
                  {{ selectedDocumentTypes.length === documentTypes.length ? 'Deselect All' : 'Select All' }}
                </button>
                <span class="text-xs text-gray-500">
                  {{ selectedDocumentTypes.length }} of {{ documentTypes.length }} selected
                </span>
              </div>
              <div class="flex flex-wrap gap-2">
                <button
                  v-for="docType in documentTypes"
                  :key="docType.extension"
                  @click="toggleDocumentType(docType.extension)"
                  class="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium transition-colors"
                  :class="{
                    'bg-blue-600 text-white': selectedDocumentTypes.includes(docType.extension),
                    'bg-gray-100 text-gray-700 hover:bg-gray-200': !selectedDocumentTypes.includes(docType.extension)
                  }"
                >
                  <span class="uppercase">{{ docType.extension }}</span>
                  <span class="ml-1 text-xs opacity-75">({{ docType.count }})</span>
                </button>
              </div>
              <div v-if="selectedDocumentTypes.length > 0" class="bg-blue-50 rounded-lg p-3">
                <p class="text-sm text-blue-800">
                  Selected {{ selectedDocumentTypes.length }} document type{{ selectedDocumentTypes.length !== 1 ? 's' : '' }}: 
                  <span class="font-medium">{{ selectedDocumentTypes.map(ext => ext.toUpperCase()).join(', ') }}</span>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Step 3: Field Selection with Document Preview -->
      <div v-if="currentStep === 3" class="space-y-6 flex-grow flex flex-col">
        <h2 class="text-xl font-semibold">Select Fields</h2>
        <div v-if="isLoadingFields" class="flex justify-center py-12">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
        <div v-else class="flex gap-6 flex-grow min-h-0 pb-20">
          <!-- Document Preview Panel -->
          <div class="flex-1 bg-white rounded-lg shadow overflow-hidden flex flex-col">
            <div class="border-b p-4 flex items-center justify-between flex-shrink-0">
              <div class="flex items-center space-x-3">
                <h3 class="font-semibold">Document Preview</h3>
                <div v-if="currentDocument" class="flex items-center space-x-2">
                  <span class="text-xs text-gray-400 font-mono">{{ currentDocument.id }}</span>
                  <button
                    @click="copyDocumentId"
                    class="p-1 rounded hover:bg-gray-100 transition-colors"
                    :title="copyButtonTitle"
                  >
                    <svg v-if="!showCopiedState" class="w-3 h-3 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                    </svg>
                    <svg v-else class="w-3 h-3 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                  </button>
                </div>
              </div>
              <div class="flex items-center space-x-2">
                <span class="text-sm text-gray-500">{{ currentDocumentIndex + 1 }} of {{ documents.length }}</span>
                <button
                  @click="previousDocument"
                  :disabled="currentDocumentIndex === 0 || isLoadingDocuments"
                  class="p-1 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                  </svg>
                </button>
                <button
                  @click="nextDocument"
                  :disabled="currentDocumentIndex === documents.length - 1 || isLoadingDocuments"
                  class="p-1 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                  </svg>
                </button>
              </div>
            </div>
            <div class="h-full flex-grow">
              <div v-if="isLoadingDocuments" class="flex items-center justify-center h-full">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              </div>
              <div v-else-if="currentDocument" class="h-full">
                <DocumentPreview :document="currentDocument" :default-zoom="'fit-width'" />
              </div>
              <div v-else class="flex items-center justify-center h-full text-gray-500 text-center p-8">
                <div>
                  <svg class="w-16 h-16 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                  </svg>
                  <h3 class="text-lg font-medium text-gray-600 mb-2">No Preview Available</h3>
                  <p class="text-sm text-gray-400 max-w-md">
                    Documents for this event type are not available for preview. 
                    You can still proceed with field selection to create your dataset.
                  </p>
                </div>
              </div>
            </div>
          </div>

          <!-- Field Selection Panel -->
          <div class="w-96 bg-white rounded-lg shadow overflow-hidden flex flex-col">
            <div class="border-b p-4 flex-shrink-0">
              <h3 class="font-semibold">Available Fields</h3>
              <div v-if="selectedFields.length > 0" class="mt-2 text-sm text-blue-600">
                {{ selectedFields.length }} field{{ selectedFields.length !== 1 ? 's' : '' }} selected
              </div>
            </div>
            <div class="overflow-y-auto flex-grow">
              <div v-for="(fields, category) in availableFields" :key="category" class="border-b">
                <div class="p-4">
                  <h4 class="font-semibold capitalize text-sm text-gray-700 mb-3 flex items-center justify-between">
                    {{ category }}
                    <span class="text-xs font-normal text-gray-500">({{ fields.length }})</span>
                  </h4>
                  <div class="space-y-3">
                    <div v-for="field in fields" :key="field.name" class="flex items-start space-x-2">
                      <input 
                        type="checkbox" 
                        :id="`field-${field.name}`" 
                        :value="field" 
                        v-model="selectedFields" 
                        class="mt-1 h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      >
                      <label :for="`field-${field.name}`" class="flex-1 text-sm cursor-pointer">
                        <div class="font-medium">{{ field.name }}</div>
                        <div class="text-gray-500 text-xs mt-1">{{ field.description }}</div>
                        <div class="text-gray-400 text-xs mt-1">
                          {{ field.fieldType }} • {{ field.frequency }} occurrences
                        </div>
                        <!-- Ground Truth Preview -->
                        <div v-if="isLoadingFieldPreviews" class="text-xs text-gray-400 mt-1">Loading preview...</div>
                        <div v-else-if="fieldPreviews[field.name]" class="text-xs text-gray-600 mt-1 bg-gray-100 p-1 rounded">
                          <strong>Preview:</strong> {{ formatPreviewValue(field, fieldPreviews[field.name]) }}
                        </div>
                        <div v-else class="text-xs text-gray-400 mt-1">No preview available</div>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Step 4: Query Preview -->
      <div v-if="currentStep === maxSteps" class="flex-grow flex flex-col">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-xl font-semibold">Review & Generate Query</h2>
          <button
            @click="generateQuery"
            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center"
            :disabled="isGeneratingQuery || isPreviewingQuery"
          >
            <span v-if="isGeneratingQuery || isPreviewingQuery" class="mr-2">
              <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            </span>
            <span>Execute Query</span>
          </button>
        </div>
        <div v-if="isGeneratingQuery || isPreviewingQuery" class="flex justify-center py-12 flex-grow">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
        <QueryConfigPreview
          v-else
          :dataset-name="generatedDatasetName"
          :query="generatedQuery"
          :fields="selectedFields"
          :extractors="defaultExtractors"
          :query-preview="queryPreview"
          @update:query="handleQueryUpdate"
          @update:fields="selectedFields = $event"
          @update:extractors="defaultExtractors = $event"
        />
      </div>
    </div>

    <!-- Footer Navigation -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t p-4">
      <div class="max-w-7xl mx-auto flex justify-between items-center">
        <button
          @click="cancel"
          class="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
        >
          Cancel
        </button>

        <div class="flex items-center space-x-3">
          <button
            v-if="currentStep > 1"
            @click="previousStep"
            class="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
          >
            Previous
          </button>
          <div class="flex flex-col items-center">
            <!-- Step indicators -->
            <div class="flex space-x-2">
              <div 
                v-for="step in maxSteps" 
                :key="step"
                class="w-3 h-3 rounded-full"
                :class="{
                  'bg-blue-600': step <= currentStep,
                  'bg-gray-300': step > currentStep
                }"
              ></div>
            </div>
            <span class="text-xs text-gray-500 mt-1">Step {{ currentStep }} of {{ maxSteps }}</span>
          </div>
          <button
            v-if="currentStep < maxSteps"
            @click="nextStep"
            :disabled="!canProceed"
            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Next
          </button>
          <button
            v-else
            @click="confirm"
            :disabled="!generatedQuery"
            class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Create Dataset
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import { trpc } from '../lib/trpc.js';
import { useResourcePool } from '../services/resourcePool.js';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import DocumentPreview from '../components/DocumentPreview.vue';
import QueryConfigPreview from '../components/QueryConfigPreview.vue';
import { documentPrefetcher } from '@/services/documentPrefetcher';

interface SiteOption {
  id: string;
  name: string;
  eventCounts: {
    stepExecutions: number;
    deliveries: number;
    emissionLogs: number;
  };
}

interface EventTypeConfig {
  type: 'step_execution' | 'delivery' | 'emission_log';
  displayName: string;
  configCount: number;
  fileCount?: number;
  sampleConfigs: Array<{
    id: string;
    name: string;
    description?: string;
  }>;
}

interface StepExecutionType {
  type: string;
  displayName: string;
  documentCount: number;
}

interface DeliveryConfig {
  id: string;
  name: string;
  documentCount: number;
}

export interface FieldOption {
  name: string;
  description: string;
  fieldType: string;
  category: 'metadata' | 'input';
  frequency: number;
}

interface Document {
  id: string;
  name: string;
  fileName?: string;
  gcsPath?: string;
  type?: string;
  size?: number;
  path?: string;
}

interface QueryPreviewResult {
  count: number;
  documents: Partial<Document>[];
}

const router = useRouter();
const { getDataset, refresh } = useResourcePool();

// State
const currentStep = ref(1);
const sites = ref<SiteOption[]>([]);
const eventTypes = ref<EventTypeConfig[]>([]);
const stepExecutionTypes = ref<StepExecutionType[]>([]);
const documentTypes = ref<Array<{extension: string, count: number}>>([]);
const availableFields = ref<{
  metadata: FieldOption[];
  inputs: FieldOption[];
}>({ metadata: [], inputs: [] });

const selectedSite = ref<SiteOption | null>(null);
const selectedEventType = ref<EventTypeConfig | null>(null);
const selectedStepExecutionTypes = ref<StepExecutionType[]>([]);
const selectedDeliveryConfigs = ref<DeliveryConfig[]>([]);
const deliveryConfigs = ref<DeliveryConfig[]>([]);
const selectedDocumentTypes = ref<string[]>([]);
const selectedFields = ref<FieldOption[]>([]);
const generatedQuery = ref('');
const defaultExtractors = ref([
  {
    "model": "mistral-ocr-latest",
    "prompt": "You are a document extraction expert analysing incoming biomass delivery documents (invoices and pictures) for Block Bio Kohle. You extract the speciefied JSON fields from the document if possible. If a field does not match the exact description of what we wan to extract you return null for that field.",
    "provider": "mistral"
  },
  {
    "model": "gemini-2.5-pro",
    "prompt": "You are a document extraction expert analysing incoming documents. You extract the specified JSON fields from the document if possible. If a field does not match the exact description of what we want to extract you return null for that field.",
    "provider": "google"
  },
  {
    "model": "gemini-2.5-flash",
    "prompt": "You are a document extraction expert analysing incoming documents. You extract the specified JSON fields from the document if possible. If a field does not match the exact description of what we want to extract you return null for that field.",
    "provider": "google"
  }
]);

// Document preview state
const documents = ref<Document[]>([]);
const currentDocumentIndex = ref(0);
const isLoadingDocuments = ref(false);
const queryPreview = ref<QueryPreviewResult | null>(null);
const isPreviewingQuery = ref(false);

// Ground truth preview state
const fieldPreviews = ref<Record<string, any>>({});
const isLoadingFieldPreviews = ref(false);

// Copy state
const showCopiedState = ref(false);
const copyButtonTitle = computed(() => showCopiedState.value ? 'Copied!' : 'Copy document ID');


// Loading states
const isLoadingSites = ref(false);
const isLoadingEventTypes = ref(false);
const isLoadingStepExecutionTypes = ref(false);
const isLoadingDeliveryConfigs = ref(false);
const isLoadingDocumentTypes = ref(false);
const isLoadingFields = ref(false);
const isGeneratingQuery = ref(false);

// Sorting state
const sortKey = ref<keyof SiteOption | 'stepExecutions' | 'deliveries' | 'emissionLogs' | 'total'>('total');
const sortOrder = ref<'asc' | 'desc'>('desc');

// Computed
const maxSteps = computed(() => 4);

const generatedDatasetName = computed(() => {
  if (!selectedSite.value || !selectedEventType.value) return '';
  return `${selectedEventType.value.displayName} - ${selectedSite.value.name} - ${new Date().toLocaleDateString()}`;
});

const canProceed = computed(() => {
  switch (currentStep.value) {
    case 1: return selectedSite.value !== null;
    case 2:
      if (!selectedEventType.value || selectedDocumentTypes.value.length === 0) {
        return false;
      }
      if (selectedEventType.value.type === 'step_execution') {
        return selectedStepExecutionTypes.value.length > 0;
      }
      return true;
    case 3: return selectedFields.value.length > 0;
    case 4: return generatedQuery.value !== '';
    default: return false;
  }
});

const sortedSites = computed(() => {
  return [...sites.value].sort((a, b) => {
    let aValue, bValue;

    if (sortKey.value === 'name') {
      aValue = a.name;
      bValue = b.name;
    } else if (sortKey.value === 'total') {
      aValue = a.eventCounts.stepExecutions + a.eventCounts.deliveries + a.eventCounts.emissionLogs;
      bValue = b.eventCounts.stepExecutions + b.eventCounts.deliveries + b.eventCounts.emissionLogs;
    } else {
      aValue = a.eventCounts[sortKey.value as keyof typeof a.eventCounts];
      bValue = b.eventCounts[sortKey.value as keyof typeof b.eventCounts];
    }

    if (aValue < bValue) return sortOrder.value === 'asc' ? -1 : 1;
    if (aValue > bValue) return sortOrder.value === 'asc' ? 1 : -1;
    return 0;
  });
});

const currentDocument = computed(() => {
  return documents.value[currentDocumentIndex.value] || null;
});

watch(currentDocumentIndex, () => {
  loadFieldPreviews();
});

// Watchers
watch(currentStep, (newStep) => {
  if (newStep === maxSteps.value) {
    generateQuery();
  }
});

watch(selectedEventType, (newEventType) => {
  if (newEventType) {
    if (newEventType.type === 'step_execution') {
      loadStepExecutionTypes();
      loadDocumentTypes();
    } else if (newEventType.type === 'delivery') {
      loadDeliveryConfigs();
      loadDocumentTypes();
    } else {
      loadDocumentTypes();
    }
  }
});

watch(selectedStepExecutionTypes, () => {
  if (selectedEventType.value?.type === 'step_execution') {
    loadDocumentTypes();
  }
}, { deep: true });

watch(selectedDeliveryConfigs, () => {
  // Reload document types when delivery configs change
  if (selectedEventType.value?.type === 'delivery') {
    loadDocumentTypes();
  }
  // Reload fields and documents if already on step 3
  if (currentStep.value === 3) {
    loadFields();
  }
}, { deep: true });


// Methods
const sortBy = (key: keyof SiteOption | 'stepExecutions' | 'deliveries' | 'emissionLogs' | 'total') => {
  if (sortKey.value === key) {
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc';
  } else {
    sortKey.value = key;
    sortOrder.value = 'asc';
  }
};

const loadSites = async () => {
  isLoadingSites.value = true;
  try {
    sites.value = await trpc.resources.queryConfig.getSites.query();
  } catch (error) {
    console.error('Failed to load sites:', error);
  } finally {
    isLoadingSites.value = false;
  }
};

const loadEventTypes = async () => {
  if (!selectedSite.value) return;
  
  isLoadingEventTypes.value = true;
  try {
    eventTypes.value = await trpc.resources.queryConfig.getEventTypes.query({
      siteId: selectedSite.value.id,
    });
  } catch (error) {
    console.error('Failed to load event types:', error);
  } finally {
    isLoadingEventTypes.value = false;
  }
};

const loadStepExecutionTypes = async () => {
  if (!selectedSite.value || selectedEventType.value?.type !== 'step_execution') return;
  
  isLoadingStepExecutionTypes.value = true;
  try {
    stepExecutionTypes.value = await trpc.resources.queryConfig.getStepExecutionTypes.query({
      siteId: selectedSite.value.id
    });
  } catch (error) {
    console.error('Failed to load step execution types:', error);
  } finally {
    isLoadingStepExecutionTypes.value = false;
  }
};

const loadDeliveryConfigs = async () => {
  if (!selectedSite.value || selectedEventType.value?.type !== 'delivery') {
    deliveryConfigs.value = [];
    return;
  }
  
  isLoadingDeliveryConfigs.value = true;
  try {
    deliveryConfigs.value = await trpc.resources.queryConfig.getDeliveryConfigs.query({
      siteId: selectedSite.value.id,
    });
    
    // Clear selected configs when delivery configs change
    selectedDeliveryConfigs.value = [];
  } catch (error) {
    console.error('Failed to load delivery configs:', error);
    deliveryConfigs.value = [];
  } finally {
    isLoadingDeliveryConfigs.value = false;
  }
};

const toggleDeliveryConfig = (config: DeliveryConfig) => {
  const index = selectedDeliveryConfigs.value.findIndex(c => c.id === config.id);
  if (index === -1) {
    selectedDeliveryConfigs.value.push(config);
  } else {
    selectedDeliveryConfigs.value.splice(index, 1);
  }
};

const isDeliveryConfigSelected = (config: DeliveryConfig): boolean => {
  return selectedDeliveryConfigs.value.some(c => c.id === config.id);
};

const loadDocumentTypes = async () => {
  if (!selectedSite.value || !selectedEventType.value) {
    return;
  }
  
  isLoadingDocumentTypes.value = true;
  try {
    documentTypes.value = await trpc.resources.queryConfig.getDocumentTypes.query({
      siteId: selectedSite.value.id,
      eventType: selectedEventType.value.type,
      stepExecutionTypes: selectedEventType.value.type === 'step_execution' 
        ? selectedStepExecutionTypes.value.map(t => t.type) 
        : undefined,
      deliveryConfigIds: selectedEventType.value.type === 'delivery'
        ? selectedDeliveryConfigs.value.map(c => c.id)
        : undefined,
    });
    
    // Start with no document types selected by default
    selectedDocumentTypes.value = [];
  } catch (error) {
    console.error('Failed to load document types:', error);
    documentTypes.value = [];
  } finally {
    isLoadingDocumentTypes.value = false;
  }
};

const loadFields = async () => {
  if (!selectedSite.value || !selectedEventType.value) return;
  
  isLoadingFields.value = true;
  try {
    availableFields.value = await trpc.resources.queryConfig.getFieldOptions.query({
      siteId: selectedSite.value.id,
      eventType: selectedEventType.value.type,
      stepExecutionTypes: selectedEventType.value.type === 'step_execution' 
        ? selectedStepExecutionTypes.value.map(t => t.type) 
        : undefined,
      deliveryConfigIds: selectedEventType.value.type === 'delivery'
        ? selectedDeliveryConfigs.value.map(c => c.id)
        : undefined,
      fileTypes: selectedDocumentTypes.value
    });
    
    await loadDocuments();
  } catch (error) {
    console.error('Failed to load fields:', error);
  } finally {
    isLoadingFields.value = false;
  }
};

const loadFieldPreviews = async () => {
  if (!documents.value.length) {
    fieldPreviews.value = {};
    return;
  }
  
  // Get all field names from available fields
  const allFields = [...availableFields.value.metadata, ...availableFields.value.inputs];
  const fieldNames = allFields.map(f => f.name);
  
  if (fieldNames.length === 0) return;
  
  isLoadingFieldPreviews.value = true;
  try {
    const previews: Record<string, any> = {};
    
    // Sample up to 10 documents to find field previews
    const maxSamples = Math.min(10, documents.value.length);
    const docIndicesToSample = [];
    
    // Always include current document if valid
    if (currentDocumentIndex.value < documents.value.length) {
      docIndicesToSample.push(currentDocumentIndex.value);
    }
    
    // Add random sampling of other documents
    const remainingSamples = maxSamples - docIndicesToSample.length;
    for (let i = 0; i < remainingSamples; i++) {
      const randomIndex = Math.floor(Math.random() * documents.value.length);
      if (!docIndicesToSample.includes(randomIndex)) {
        docIndicesToSample.push(randomIndex);
      }
    }
    
    // Query each sampled document until we have previews for all fields or exhaust samples
    const fieldsStillNeeded = new Set(fieldNames);
    
    for (const docIndex of docIndicesToSample) {
      if (fieldsStillNeeded.size === 0) break;
      
      const doc = documents.value[docIndex];
      if (!doc?.id) continue;
      
      const docFieldNames = Array.from(fieldsStillNeeded);
      const docPreviews = await trpc.resources.groundTruths.fieldPreview.query({
        documentId: doc.id,
        fieldNames: docFieldNames
      });
      
      // Add found previews and remove from needed set
      for (const [fieldName, fieldValue] of Object.entries(docPreviews)) {
        if (fieldValue !== null && fieldValue !== undefined) {
          previews[fieldName] = fieldValue;
          fieldsStillNeeded.delete(fieldName);
        }
      }
    }
    
    fieldPreviews.value = previews;
  } catch (error) {
    console.error('Failed to load field previews:', error);
    fieldPreviews.value = {};
  } finally {
    isLoadingFieldPreviews.value = false;
  }
};

const loadDocuments = async () => {
  if (!selectedSite.value || !selectedEventType.value) return;
  
  isLoadingDocuments.value = true;
  try {
    const documentsResult = await trpc.resources.queryConfig.getDocumentsForEventType.query({
      siteId: selectedSite.value.id,
      eventType: selectedEventType.value.type,
      fileTypes: selectedDocumentTypes.value,
      stepExecutionTypes: selectedEventType.value.type === 'step_execution' 
        ? selectedStepExecutionTypes.value.map(t => t.type) 
        : undefined,
      deliveryConfigIds: selectedEventType.value.type === 'delivery'
        ? selectedDeliveryConfigs.value.map(c => c.id)
        : undefined,
      timestamp: Date.now()
    });
    
    documents.value = documentsResult;
    currentDocumentIndex.value = 0;
    
    // Start prefetching the first batch of documents
    prefetchNextDocuments();
    
    // Load field previews for the current document
    await loadFieldPreviews();
  } catch (error) {
    console.error('Failed to load documents:', error);
    documents.value = [];
  } finally {
    isLoadingDocuments.value = false;
  }
};

const generateQuery = async () => {
  if (!selectedSite.value || !selectedEventType.value) return;
  
  isGeneratingQuery.value = true;
  isPreviewingQuery.value = true;
  try {
    const result = await trpc.resources.queryConfig.generateQuery.mutate({
      siteId: selectedSite.value.id,
      eventType: selectedEventType.value.type,
      stepExecutionTypes: selectedStepExecutionTypes.value.map(t => t.type),
      fileTypes: selectedDocumentTypes.value,
      deliveryConfigIds: selectedEventType.value.type === 'delivery' ? selectedDeliveryConfigs.value.map(c => c.id) : undefined,
    });
    generatedQuery.value = result.query;

    // Fetch preview
    queryPreview.value = await trpc.resources.queryConfig.previewQuery.query({
      query: result.query,
    });

  } catch (error) {
    console.error('Failed to generate or preview query:', error);
    queryPreview.value = { count: 0, documents: [] };
  } finally {
    isGeneratingQuery.value = false;
    isPreviewingQuery.value = false;
  }
};

const handleQueryUpdate = async (newQuery: string) => {
  generatedQuery.value = newQuery;
  
  if (!newQuery.trim()) {
    queryPreview.value = { count: 0, documents: [] };
    return;
  }
  
  isPreviewingQuery.value = true;
  try {
    queryPreview.value = await trpc.resources.queryConfig.previewQuery.query({
      query: newQuery,
    });
  } catch (error) {
    console.error('Failed to preview updated query:', error);
    queryPreview.value = { count: 0, documents: [] };
  } finally {
    isPreviewingQuery.value = false;
  }
};

const nextStep = async () => {
  if (!canProceed.value) return;

  if (currentStep.value === 1) {
    await loadEventTypes();
  } else if (currentStep.value === 2) {
    await loadFields();
  }

  if (currentStep.value < maxSteps.value) {
    currentStep.value++;
  }
};

const previousStep = () => {
  if (currentStep.value > 1) {
    currentStep.value--;
  }
};

const toggleStepExecutionType = (stepType: StepExecutionType) => {
  const index = selectedStepExecutionTypes.value.findIndex(t => t.type === stepType.type);
  if (index > -1) {
    selectedStepExecutionTypes.value.splice(index, 1);
  } else {
    selectedStepExecutionTypes.value.push(stepType);
  }
};

const toggleDocumentType = (extension: string) => {
  const index = selectedDocumentTypes.value.indexOf(extension);
  if (index > -1) {
    selectedDocumentTypes.value.splice(index, 1);
  } else {
    selectedDocumentTypes.value.push(extension);
  }
  
  if (currentStep.value === 3) { // Field selection step
    loadFields(); // Reload fields and documents to reflect new document type selection
  }
  
  if (currentStep.value === maxSteps.value && generatedQuery.value) {
    generateQuery();
  }
};

const toggleAllDocumentTypes = () => {
  if (selectedDocumentTypes.value.length === documentTypes.value.length) {
    // All are selected, deselect all
    selectedDocumentTypes.value = [];
  } else {
    // Not all are selected, select all
    selectedDocumentTypes.value = documentTypes.value.map(dt => dt.extension);
  }
  
  if (currentStep.value === 3) { // Field selection step
    loadFields(); // Reload fields and documents to reflect new document type selection
  }
  
  if (currentStep.value === maxSteps.value && generatedQuery.value) {
    generateQuery();
  }
};

const nextDocument = () => {
  if (currentDocumentIndex.value < documents.value.length - 1) {
    currentDocumentIndex.value++;
    prefetchNextDocuments();
  }
};

const previousDocument = () => {
  if (currentDocumentIndex.value > 0) {
    currentDocumentIndex.value--;
    prefetchNextDocuments();
  }
};

const prefetchNextDocuments = () => {
  if (!documents.value.length) return;
  
  const startIndex = currentDocumentIndex.value + 1;
  const endIndex = Math.min(startIndex + 10, documents.value.length);
  
  const documentsToPreffetch = documents.value
    .slice(startIndex, endIndex)
    .map(doc => doc.id || (doc.gcsPath ? doc.gcsPath.split('/').pop() : null))
    .filter(Boolean) as string[];
  
  if (documentsToPreffetch.length > 0) {
    documentPrefetcher.prefetchBatch(documentsToPreffetch);
  }
};

const formatPreviewValue = (field: FieldOption, value: any): string => {
  // Handle JSON objects in text fields - extract the value or label
  if (typeof value === 'object' && value !== null) {
    if (value.value !== undefined) {
      return String(value.value);
    } else if (value.label !== undefined) {
      return String(value.label);
    } else {
      return JSON.stringify(value);
    }
  }
  
  // Check if this is a date/timestamp field or if the value looks like a timestamp
  const isDateField = field.fieldType === 'date' || 
                      field.name.toLowerCase().includes('timestamp') || 
                      field.name.toLowerCase().includes('date');
  
  if ((typeof value === 'string' || typeof value === 'number')) {
    const numericValue = typeof value === 'string' ? parseInt(value) : value;
    
    // Check if it looks like a Unix timestamp (either seconds or milliseconds)
    const isTimestamp = !isNaN(numericValue) && (
      (numericValue > 946684800000) || // After year 2000 in milliseconds
      (numericValue > 946684800 && numericValue < 9999999999) // After year 2000 in seconds, before year 2286
    );
    
    if (isDateField || isTimestamp) {
      let date: Date;
      
      if (numericValue > 946684800000) { // Milliseconds
        date = new Date(numericValue);
      } else if (numericValue > 946684800) { // Seconds
        date = new Date(numericValue * 1000);
      } else {
        return String(value);
      }
      
      if (!isNaN(date.getTime())) {
        const day = date.getDate().toString().padStart(2, '0');
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const year = date.getFullYear();
        const hours = date.getHours().toString().padStart(2, '0');
        const minutes = date.getMinutes().toString().padStart(2, '0');
        return `${value} (${day}.${month}.${year} ${hours}:${minutes})`;
      }
    }
  }
  
  return String(value);
};

const copyDocumentId = async () => {
  if (!currentDocument.value?.id) return;
  
  try {
    await navigator.clipboard.writeText(currentDocument.value.id);
    showCopiedState.value = true;
    setTimeout(() => {
      showCopiedState.value = false;
    }, 2000);
  } catch (error) {
    console.error('Failed to copy document ID:', error);
    // Fallback for older browsers
    const textArea = document.createElement('textarea');
    textArea.value = currentDocument.value.id;
    document.body.appendChild(textArea);
    textArea.select();
    document.execCommand('copy');
    document.body.removeChild(textArea);
    
    showCopiedState.value = true;
    setTimeout(() => {
      showCopiedState.value = false;
    }, 2000);
  }
};

const cancel = () => {
  router.push('/datasets');
};

const confirm = async () => {
  if (!selectedSite.value || !selectedEventType.value || !generatedQuery.value) return;
  
  try {
    const newDataset = await trpc.resources.datasets.create.mutate({
      name: generatedDatasetName.value,
      description: `Auto-generated dataset for ${selectedEventType.value.displayName} with ${selectedFields.value.length} fields`,
      query: generatedQuery.value,
      version: 1,
      selectedFields: selectedFields.value,
      selectedExtractors: defaultExtractors.value
    });
    
    await refresh();
    
    const waitForDataset = async (datasetId: string, maxAttempts = 10) => {
      for (let i = 0; i < maxAttempts; i++) {
        const dataset = getDataset(datasetId);
        if (dataset && dataset.extractionConfigId) {
          router.push(`/datasets/${datasetId}`);
          return;
        }
        await new Promise(resolve => setTimeout(resolve, 500));
      }
      router.push(`/datasets/${newDataset.id}`);
    };
    
    await waitForDataset(newDataset.id);
    
  } catch (error) {
    console.error('❌ Failed to create dataset:', error);
  }
};

onMounted(() => {
  loadSites();
});

onUnmounted(() => {
  documentPrefetcher.clearCache();
});
</script>



<style scoped>
/* Custom scrollbar for panels */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #555;
}
</style>
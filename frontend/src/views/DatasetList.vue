<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Datasets</h1>
        <p class="text-gray-600">Manage your document datasets and extraction configurations</p>
      </div>
      <div class="flex space-x-3">
        <button
          @click="$router.push('/datasets/builder')"
          class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
          </svg>
          <span>Smart Dataset</span>
        </button>
        <button
          @click="showSimpleModal = true"
          class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors flex items-center space-x-2"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
          </svg>
          <span>Simple Dataset</span>
        </button>
      </div>
    </div>

    <!-- Loading state -->
    <div v-if="isLoading" class="text-center py-12">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
      <p class="text-gray-600">Loading datasets...</p>
    </div>

    <!-- Error state -->
    <div v-else-if="error" class="bg-red-50 border border-red-200 rounded-lg p-4">
      <div class="flex items-center space-x-2">
        <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <span class="text-red-800 font-medium">Failed to load datasets</span>
      </div>
      <p class="text-red-700 mt-1">{{ error }}</p>
      <button 
        @click="refresh"
        class="mt-2 px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700 transition-colors"
      >
        Retry
      </button>
    </div>

    <!-- Empty state -->
    <div v-else-if="datasets.length === 0" class="text-center py-12 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
      <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
      </svg>
      <h3 class="text-lg font-medium text-gray-900 mb-2">No datasets found</h3>
      <p class="text-gray-600 mb-4">Get started by creating your first dataset</p>
      <div class="flex space-x-3">
        <button
          @click="$router.push('/datasets/builder')"
          class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          Smart Dataset
        </button>
        <button
          @click="showSimpleModal = true"
          class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
        >
          Simple Dataset
        </button>
      </div>
    </div>

    <!-- Dataset grid -->
    <div v-else class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      <div
        v-for="dataset in datasets"
        :key="dataset.id"
        class="bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow cursor-pointer"
        @click="navigateToDataset(dataset.id)"
      >
        <div class="p-6">
          <!-- Dataset header -->
          <div class="flex justify-between items-start mb-4">
            <div class="flex-1">
              <h3 class="text-lg font-semibold text-gray-900 mb-1">{{ dataset.name }}</h3>
              <p v-if="dataset.description" class="text-gray-600 text-sm line-clamp-2">{{ dataset.description }}</p>
            </div>
            <div class="flex items-center space-x-2 ml-4">
              <!-- Status badge -->
              <span 
                class="px-2 py-1 text-xs font-medium rounded-full"
                :class="{
                  'bg-green-100 text-green-800': dataset.status === 'ready',
                  'bg-yellow-100 text-yellow-800': dataset.status === 'syncing',
                  'bg-red-100 text-red-800': dataset.status === 'error'
                }"
              >
                {{ dataset.status }}
              </span>
              
              <!-- Actions dropdown -->
              <div class="relative">
                <button
                  @click.stop="toggleDropdown(dataset.id)"
                  class="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                  </svg>
                </button>
                
                <div
                  v-if="activeDropdown === dataset.id"
                  class="absolute right-0 mt-1 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10"
                >
                  <div class="py-1">
                    <button
                      @click.stop="editDataset(dataset)"
                      class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                    >
                      Edit Dataset
                    </button>
                    <button
                      @click.stop="confirmDelete(dataset)"
                      class="block w-full text-left px-4 py-2 text-sm text-red-700 hover:bg-red-50"
                    >
                      Delete Dataset
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Dataset stats -->
          <div class="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span class="text-gray-500">Documents:</span>
              <span class="font-medium ml-1">{{ getDocumentCount(dataset.id) }}</span>
            </div>
            <div>
              <span class="text-gray-500">Version:</span>
              <span class="font-medium ml-1">{{ dataset.version }}</span>
            </div>
          </div>

          <!-- Creation date -->
          <div class="mt-4 pt-4 border-t border-gray-100">
            <span class="text-xs text-gray-500">
              Created {{ formatDate(dataset.createdAt) }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Simple Create/Edit Modal for existing datasets -->
    <div v-if="showSimpleModal || editingDataset" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 w-full max-w-2xl mx-4">
        <h2 class="text-xl font-semibold mb-4">
          {{ editingDataset ? 'Edit Dataset' : 'Create Simple Dataset' }}
        </h2>
        
        <form @submit.prevent="saveDataset">
          <div class="space-y-4">
            <div>
              <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Name</label>
              <input
                id="name"
                v-model="datasetForm.name"
                type="text"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter dataset name"
                required
              >
            </div>
            
            <div>
              <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
              <textarea
                id="description"
                v-model="datasetForm.description"
                rows="3"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter dataset description"
              ></textarea>
            </div>
            
            <div>
              <label for="query" class="block text-sm font-medium text-gray-700 mb-1">Query</label>
              <textarea
                id="query"
                v-model="datasetForm.query"
                rows="4"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
                placeholder="SELECT * FROM documents WHERE..."
                required
              ></textarea>
            </div>
          </div>
          
          <div class="flex justify-end space-x-3 mt-6">
            <button
              type="button"
              @click="cancelEdit"
              class="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              :disabled="isSubmitting"
              class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50"
            >
              {{ isSubmitting ? 'Saving...' : (editingDataset ? 'Update' : 'Create') }}
            </button>
          </div>
        </form>
      </div>
    </div>


    <!-- Delete confirmation modal -->
    <div v-if="datasetToDelete" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 w-full max-w-md mx-4">
        <h2 class="text-xl font-semibold mb-4 text-red-900">Confirm Deletion</h2>
        <p class="text-gray-700 mb-6">
          Are you sure you want to delete "<strong>{{ datasetToDelete.name }}</strong>"? 
          This action cannot be undone and will also delete all related documents and extractions.
        </p>
        
        <div class="flex justify-end space-x-3">
          <button
            @click="datasetToDelete = null"
            class="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
          >
            Cancel
          </button>
          <button
            @click="deleteDataset"
            :disabled="isDeleting"
            class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors disabled:opacity-50"
          >
            {{ isDeleting ? 'Deleting...' : 'Delete' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { useResourcePool } from '../services/resourcePool.js';
import type { Dataset } from '../types/index.js';
import { trpc } from '../lib/trpc.js';

interface FieldOption {
  name: string;
  description: string;
  fieldType: string;
  category: string;
  frequency: number;
}

const router = useRouter();

// Get ResourcePool composable
const {
  datasets,
  documents,
  isLoading,
  error,
  createDataset,
  updateDataset,
  deleteDataset: deleteDatasetFromPool,
  refresh
} = useResourcePool();

// Component state
const showSimpleModal = ref(false);
const editingDataset = ref<Dataset | null>(null);
const activeDropdown = ref<string | null>(null);
const datasetToDelete = ref<Dataset | null>(null);
const isSubmitting = ref(false);
const isDeleting = ref(false);

// Form data
const datasetForm = ref({
  name: '',
  description: '',
  query: '',
});

onMounted(() => {
  document.addEventListener('click', closeDropdown);
});

onUnmounted(() => {
  document.removeEventListener('click', closeDropdown);
});


// Helper functions
const documentCounts = computed(() => {
  return documents.value.reduce((acc, doc) => {
    if (doc.datasetId) {
      acc[doc.datasetId] = (acc[doc.datasetId] || 0) + 1;
    }
    return acc;
  }, {} as Record<string, number>);
});

const getDocumentCount = (datasetId: string): number => {
  return documentCounts.value[datasetId] || 0;
};

const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

// Navigation
const navigateToDataset = (datasetId: string) => {
  router.push(`/datasets/${datasetId}`);
};

// Dropdown management
const toggleDropdown = (datasetId: string) => {
  activeDropdown.value = activeDropdown.value === datasetId ? null : datasetId;
};

const closeDropdown = () => {
  activeDropdown.value = null;
};

// Dataset operations
const editDataset = (dataset: Dataset) => {
  editingDataset.value = dataset;
  datasetForm.value = {
    name: dataset.name,
    description: dataset.description || '',
    query: dataset.query,
  };
  closeDropdown();
};

const confirmDelete = (dataset: Dataset) => {
  datasetToDelete.value = dataset;
  closeDropdown();
};

const cancelEdit = () => {
  showSimpleModal.value = false;
  editingDataset.value = null;
  datasetForm.value = {
    name: '',
    description: '',
    query: '',
  };
};


const saveDataset = async () => {
  isSubmitting.value = true;
  
  try {
    const datasetData = {
      name: datasetForm.value.name,
      description: datasetForm.value.description || undefined,
      query: datasetForm.value.query,
      version: 1,
      documentCount: 0,
      status: 'ready' as const,
    };

    if (editingDataset.value) {
      await updateDataset(editingDataset.value.id, datasetData);
      console.log('✅ Dataset updated successfully');
    } else {
      await createDataset(datasetData);
      console.log('✅ Dataset created successfully');
    }
    
    cancelEdit();
  } catch (error) {
    console.error('❌ Failed to save dataset:', error);
    // TODO: Show error toast
  } finally {
    isSubmitting.value = false;
  }
};

const deleteDataset = async () => {
  if (!datasetToDelete.value) return;
  
  isDeleting.value = true;
  
  try {
    await deleteDatasetFromPool(datasetToDelete.value.id);
    console.log('✅ Dataset deleted successfully');
    datasetToDelete.value = null;
  } catch (error) {
    console.error('❌ Failed to delete dataset:', error);
    // TODO: Show error toast
  } finally {
    isDeleting.value = false;
  }
};
</script>

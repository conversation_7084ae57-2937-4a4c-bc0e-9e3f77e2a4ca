<script setup lang="ts">
import { ref } from 'vue';
import { trpc } from '../services';
import type { FieldOption } from '../../../backend/src/services/fieldDiscoveryService';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

const siteId = ref('');
const eventType = ref<'step_execution' | 'delivery' | 'emission_log'>('step_execution');
const stepExecutionTypes = ref('');
const deliveryConfigIds = ref('');
const fileTypes = ref('');

const metadata = ref<FieldOption[]>([]);
const inputs = ref<FieldOption[]>([]);
const error = ref<any>(null);
const loading = ref(false);

async function discoverFields() {
  loading.value = true;
  error.value = null;
  try {
    const result = await trpc.resources.discoverFields.query({
      siteId: siteId.value || undefined,
      eventType: eventType.value,
      stepExecutionTypes: stepExecutionTypes.value ? stepExecutionTypes.value.split(',').map(s => s.trim()) : undefined,
      deliveryConfigIds: deliveryConfigIds.value ? deliveryConfigIds.value.split(',').map(s => s.trim()) : undefined,
      fileTypes: fileTypes.value ? fileTypes.value.split(',').map(s => s.trim()) : undefined,
    });
    metadata.value = result.metadata;
    inputs.value = result.inputs;
  } catch (e) {
    error.value = e;
  } finally {
    loading.value = false;
  }
}
</script>

<template>
  <div class="p-4 md:p-8">
    <h1 class="text-2xl font-bold mb-4">Field Discovery Debugger</h1>
    <Card class="max-w-2xl">
      <CardHeader>
        <CardTitle>Parameters</CardTitle>
      </CardHeader>
      <CardContent class="space-y-4">
        <div>
          <label for="siteId" class="block text-sm font-medium mb-1">Site ID</label>
          <Input id="siteId" type="text" v-model="siteId" placeholder="Enter Site ID" />
        </div>
        <div>
          <label for="eventType" class="block text-sm font-medium mb-1">Event Type</label>
          <select id="eventType" v-model="eventType" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm p-2 border">
            <option>step_execution</option>
            <option>delivery</option>
            <option>emission_log</option>
          </select>
        </div>
        <div v-if="eventType === 'step_execution'">
          <label for="stepExecutionTypes" class="block text-sm font-medium mb-1">Step Execution Types (comma-separated)</label>
          <Input id="stepExecutionTypes" type="text" v-model="stepExecutionTypes" placeholder="e.g. type1,type2" />
        </div>
        <div v-if="eventType === 'delivery'">
          <label for="deliveryConfigIds" class="block text-sm font-medium mb-1">Delivery Config IDs (comma-separated)</label>
          <Input id="deliveryConfigIds" type="text" v-model="deliveryConfigIds" placeholder="e.g. id1,id2" />
        </div>
        <div>
          <label for="fileTypes" class="block text-sm font-medium mb-1">File Types (comma-separated)</label>
          <Input id="fileTypes" type="text" v-model="fileTypes" placeholder="e.g. pdf,png" />
        </div>
        <div>
          <Button @click="discoverFields" :disabled="loading">
            {{ loading ? 'Loading...' : 'Discover Fields' }}
          </Button>
        </div>
      </CardContent>
    </Card>

    <div v-if="error" class="mt-4 p-4 bg-red-100 text-red-700 rounded-md max-w-2xl">
      <h2 class="font-bold">Error</h2>
      <pre class="whitespace-pre-wrap break-all">{{ error }}</pre>
    </div>

    <div class="mt-8 grid grid-cols-1 md:grid-cols-2 gap-8">
      <Card>
        <CardHeader>
          <CardTitle>Metadata Fields</CardTitle>
        </CardHeader>
        <CardContent>
          <ul v-if="metadata.length" class="space-y-2">
            <li v-for="field in metadata" :key="field.name" class="p-3 border rounded-md bg-gray-50">
              <strong>{{ field.name }}</strong> ({{ field.fieldType }}) - Freq: {{ field.frequency }}
              <p class="text-sm text-gray-600">{{ field.description }}</p>
            </li>
          </ul>
          <p v-else class="text-gray-500">No metadata fields found.</p>
        </CardContent>
      </Card>
      <Card>
        <CardHeader>
          <CardTitle>Input Fields</CardTitle>
        </CardHeader>
        <CardContent>
          <ul v-if="inputs.length" class="space-y-2">
            <li v-for="field in inputs" :key="field.name" class="p-3 border rounded-md bg-gray-50">
              <strong>{{ field.name }}</strong> ({{ field.fieldType }}) - Freq: {{ field.frequency }}
              <p class="text-sm text-gray-600">{{ field.description }}</p>
            </li>
          </ul>
          <p v-else class="text-gray-500">No input fields found.</p>
        </CardContent>
      </Card>
    </div>
  </div>
</template>

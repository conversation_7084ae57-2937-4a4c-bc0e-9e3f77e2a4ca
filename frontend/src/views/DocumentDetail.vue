<template>
  <div class="h-screen flex flex-col bg-background">
    <!-- Header with Navigation -->
    <div class="bg-card">
      <div class="py-4">
        <!-- Document Header -->
        <div class="flex flex-grow items-center justify-between">
          <div class="flex items-center space-x-4">
            <button 
              @click="router.push(`/datasets/${props.datasetId}`)"
              class="p-2 hover:bg-accent rounded-lg transition-colors"
            >
              <ArrowLeft class="h-5 w-5" />
            </button>
            
            <div class="flex items-center space-x-3">
              <div class="p-2 bg-primary/10 rounded-md">
                <FileText class="h-5 w-5 text-primary" />
              </div>
                <div>
                  <h1 class="text-xl font-semibold text-foreground">{{ selectedDocument?.name }}</h1>
                  <p v-if="selectedDocument?.type" class="text-sm text-muted-foreground">{{ selectedDocument?.type }}</p>
                </div>
            </div>
          </div>

          <!-- Navigation Buttons -->
          <div class="flex items-center space-x-2">
            <button
              @click="navigateToDocument(previousDocumentId)"
              :disabled="!previousDocumentId"
              class="p-2 hover:bg-accent rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              title="Previous Document"
            >
              <ChevronLeft class="h-5 w-5" />
            </button>
            
            <!-- Approval Buttons -->
            <button
              @click="toggleApproval"
              :disabled="approving"
              class="px-3 py-2 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
              :class="{
                'bg-red-100 text-red-600 hover:bg-red-200': selectedDocument?.isApproved,
                'bg-green-100 text-green-600 hover:bg-green-200': !selectedDocument?.isApproved,
                'animate-pulse': approving
              }"
              :title="selectedDocument?.isApproved ? 'Unapprove Document' : 'Approve Document'"
            >
              <X v-if="selectedDocument?.isApproved" class="h-4 w-4" />
              <Check v-else class="h-4 w-4" />
              <span class="text-sm font-medium">
                {{ selectedDocument?.isApproved ? 'Unapprove' : 'Approve' }}
              </span>
            </button>
            
            <button
              @click="goToNextUnapproved"
              class="px-3 py-2 bg-blue-100 hover:bg-blue-200 rounded-lg transition-colors text-blue-600 flex items-center gap-2"
              title="Go to Next Unapproved Document"
            >
              <SkipForward class="h-4 w-4" />
              <span class="text-sm font-medium">Next Unapproved</span>
            </button>
            
            <button
              @click="navigateToDocument(nextDocumentId)"
              :disabled="!nextDocumentId"
              class="p-2 hover:bg-accent rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              title="Next Document"
            >
              <ChevronRight class="h-5 w-5" />
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content Area -->
    <div class="flex-1 grid grid-cols-1 lg:grid-cols-3 gap-4 min-h-0">
      <!-- Left Panel: Document Preview (1/3 width) -->
      <div class="bg-card border border-border rounded-lg overflow-hidden lg:col-span-1">
        <div class="border-b border-border p-4">
          <h2 class="text-lg font-semibold text-foreground">Document Preview</h2>
          <p class="text-sm text-muted-foreground">
            Interactive document with extraction highlights
          </p>
        </div>
        
        <div class="relative h-full overflow-auto">
          <DocumentPreview
            v-if="selectedDocument"
            :document="selectedDocument"
          />
          
          <div v-else class="flex items-center justify-center h-full text-muted-foreground">
            <div class="text-center">
              <FileText class="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No document selected</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Panel: Extracted Data (2/3 width) -->
      <DocumentExtractions
        :document-id="props.documentId"
        :dataset-id="props.datasetId"
        :extraction-config="activeConfig"
        :available-extractors="computedAvailableExtractors"
        :compared-fields="comparedFields"
        :ground-truth-data="groundTruthData"
        :failed-extractions="failedExtractions"
        :extractor-status="extractorStatus"
        :is-initializing="loading"
        @run-single-extraction="runSingleExtraction"
        @run-all-extractions="runAllExtractions"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ArrowLeft, FileText, ChevronLeft, ChevronRight, Check, SkipForward, X } from 'lucide-vue-next'
import DocumentPreview from '@/components/DocumentPreview.vue'
import DocumentExtractions from '@/components/DocumentExtractionsPanel.vue'
import { calculateCorrectness } from '@/utils/correctness'
import { useResourcePool, applyPatch, resourcePool } from '@/services/resourcePool.js'
import { trpc } from '@/lib/trpc.js'
import { Extraction, GroundTruth } from '@/types'

// --- Props ---
interface Props {
  datasetId: string
  documentId: string
}

const props = defineProps<Props>()

// --- Setup ---
const route = useRoute()
const router = useRouter()

// Get ResourcePool composable - ALL data comes from here (includes WebSocket connection)
const {
  documents, datasets, extractionConfigs, extractions, groundTruths, runExtraction, runExtractions,
  wsConnected, wsError, refresh, subscribeToAdditionalChannels
} = useResourcePool()


// --- Local State ---
const loading = ref(false)
const extractorStatus = ref<Record<string, 'idle' | 'loading' | 'success' | 'error'>>({})

// --- Navigation ---
const datasetDocuments = computed(() => {
  // Use the default order from the backend, filtered by dataset
  return documents.value.filter(doc => doc.datasetId === props.datasetId);
});

const currentDocumentIndex = computed(() => {
  return datasetDocuments.value.findIndex(doc => doc.id === props.documentId);
});

const previousDocumentId = computed(() => {
  if (currentDocumentIndex.value > 0) {
    return datasetDocuments.value[currentDocumentIndex.value - 1].id;
  }
  return null;
});

const nextDocumentId = computed(() => {
  if (currentDocumentIndex.value >= 0 && currentDocumentIndex.value < datasetDocuments.value.length - 1) {
    return datasetDocuments.value[currentDocumentIndex.value + 1].id;
  }
  return null;
});

const navigateToDocument = (documentId: string | null) => {
  if (documentId) {
    router.push(`/datasets/${props.datasetId}/documents/${documentId}`);
  }
};

// --- Approval Functions ---
const approving = ref(false);

const toggleApproval = async () => {
  if (!selectedDocument.value || approving.value) return;
  
  approving.value = true;
  try {
    let updatedDocument;
    
    if (selectedDocument.value.isApproved) {
      // Unapprove the document
      updatedDocument = await trpc.resources.documents.unapprove.mutate({
        id: selectedDocument.value.id
      });
      console.log('✅ Document unapproved successfully');
    } else {
      // Approve the document
      updatedDocument = await trpc.resources.documents.approve.mutate({
        id: selectedDocument.value.id,
        approvedBy: 'user' // You could get this from auth context
      });
      console.log('✅ Document approved successfully');
    }
    
    // Properly update the document in the resourcePool state for immediate reactivity
    if (updatedDocument) {
      applyPatch('document_updated', { document: updatedDocument });
    }
    
  } catch (error) {
    console.error('❌ Failed to toggle approval:', error);
    // You could show a toast notification here
  } finally {
    approving.value = false;
  }
};

const goToNextUnapproved = async () => {
  if (!props.datasetId) return;
  
  try {
    const nextUnapproved = await trpc.resources.documents.getNextUnapproved.query({
      datasetId: props.datasetId,
      currentDocumentId: props.documentId
    });
    
    if (nextUnapproved) {
      router.push(`/datasets/${props.datasetId}/documents/${nextUnapproved.id}`);
    } else {
      console.log('No unapproved documents found');
      // You could show a toast notification here
    }
  } catch (error) {
    console.error('❌ Failed to find next unapproved document:', error);
  }
};

// Create extraction results from resource pool data
const extractionResults = computed(() => {
  const results: Record<string, any> = {}
  for (const model in extractionsByModel.value) {
    const extraction = extractionsByModel.value[model]
    if (extraction) {
      results[model] = {
        id: extraction.id,
        data: extraction.data || {},
        error: (extraction as any).error,
        failed: extraction.status === 'failed' || extraction.status === 'error'
      }
    }
  }
  return results
})
  
  
// Show WebSocket connection status in dev mode
if (process.env.NODE_ENV === 'development') {
  watch(wsConnected, (connected) => {
    console.log(`🔌 WebSocket ${connected ? 'connected' : 'disconnected'}`)
  })
  
  watch(wsError, (error) => {
    if (error) {
      console.error('🔌 WebSocket error:', error)
    }
  })
}


// --- Computed Properties ---
const selectedDocument = computed(() => {
  return documents.value.find(doc => doc.id === props.documentId) || null;
});

const computedDataset = computed(() => {
  return datasets.value.find(d => d.id === props.datasetId) || null;
});

const activeConfig = computed(() => {
  if (!computedDataset.value?.extractionConfigId) return null;
  return extractionConfigs.value.find(config => config.id === computedDataset.value.extractionConfigId) || null;
});

const documentExtractions = computed(() => {
  return extractions.value.filter(ext => ext.documentId === props.documentId);
});

const extractionsByModel = computed<Record<string, Extraction | null>>(() => {
  const results: Record<string, Extraction | null> = {};
  documentExtractions.value.forEach(extraction => {
    const modelKey = extraction.model;
    results[modelKey] = {
      id: extraction.id,
      documentId: extraction.documentId,
      provider: extraction.provider,
      model: extraction.model,
      processingTime: extraction.processingTime,
      data: extraction.data || {},
      createdAt: extraction.createdAt,
      status: extraction.status
    };
  });
  return results;
});

const groundTruthData = computed<Record<string, GroundTruth>>(() => {
  const formattedGroundTruth: Record<string, GroundTruth> = {};
  groundTruths.value.filter(gt => gt.documentId === props.documentId).forEach(gt => {
    formattedGroundTruth[gt.fieldName] = {
      extractedValue: gt.extractedValue,
      userAnnotation: gt.userAnnotation,
      fieldType: gt.fieldType
    };
  });
  return formattedGroundTruth;
});

const computedAvailableExtractors = computed(() => {
  if (!activeConfig.value?.data?.extractors) return []
  return activeConfig.value.data.extractors.map(extractor => ({
    id: extractor.model,
    model: extractor.model,
    provider: extractor.provider
  }))
})
  
const configFields = computed(() => {
  if (!activeConfig.value?.data?.fields) return [];
  return activeConfig.value.data.fields;
});


const allFieldNames = computed(() => {
  const fieldNames = new Set<string>();

  return configFields.value.map(field => field.name);
});

const failedExtractions = computed(() => {
  const failed: Array<{extractor: any, error: string}> = []
  
  for (const extractor of computedAvailableExtractors.value) {
    const extractorResult = extractionResults.value[extractor.model]
    
    // Check if extraction failed or has error status
    if (extractorStatus.value[extractor.model] === 'error' || extractorResult?.status === 'failed' || extractorResult?.status === 'error') {
      // Try to get error from various possible locations
      let error = 'Extraction failed with unknown error'
      
      if (extractorResult?.error) {
        error = extractorResult.error
      } else if (extractorResult?.data?.error) {
        // Handle case where error might be an object with value property
        const errorData = extractorResult.data.error
        error = typeof errorData === 'string' ? errorData : (errorData?.value || JSON.stringify(errorData))
      } else {
        // Get original extraction from extractions array to access full data
        const fullExtraction = extractions.value.find(ext => 
          ext.model === extractor.model && 
          ext.documentId === props.documentId
        )
        
        if (fullExtraction?.error) {
          error = fullExtraction.error
        }
      }
      
      failed.push({ extractor, error })
    }
  }
  
  return failed
})

const comparedFields = computed(() => {
  const fields: any[] = []

  for (const fieldName of allFieldNames.value) {
    const results: Record<string, any> = {}
    
    for (const extractor of computedAvailableExtractors.value) {
      const extractorResult = extractionResults.value[extractor.model]
      
      // Handle extraction errors
      if (extractorResult?.failed) {
        results[extractor.model] = {
          name: fieldName,
          value: null,
          type: 'text',
          error: true,
          errorMessage: extractorResult.error || 'Extraction failed',
          noExtractionYet: false
        }
        continue
      }
      
      // Check for errors in the extracted data
      const extractedDataError = extractorResult?.data?.error?.value || 
                                 (extractorResult as any)?.extracted_data?.error ||
                                 (extractorResult as any)?.raw_response?.error
      
      if (extractedDataError) {
        let errorMessage = extractedDataError
        if (typeof extractedDataError === 'object') {
          errorMessage = JSON.stringify(extractedDataError)
        }
        
        results[extractor.model] = {
          name: fieldName,
          value: null,
          type: 'text',
          error: true,
          errorMessage: errorMessage,
          noExtractionYet: false
        }
        continue
      }
      
      const fieldData = extractorResult?.data?.[fieldName]
      
      let fieldType: "text" | "number" | "date" | "boolean" = 'text'
      
      if (activeConfig.value?.data?.fields) {
        const fieldConfig = activeConfig.value.data.fields.find(f => f.name === fieldName)
        if (fieldConfig) {
          fieldType = fieldConfig.fieldType as any
        }
      }

      // Extract actual ground truth value from detailed structure
      const groundTruth = groundTruthData.value[fieldName]?.hasOwnProperty('user_annotation') 
        ? groundTruthData.value[fieldName].userAnnotation 
        : (groundTruthData.value[fieldName]?.extractedValue ?? undefined)
      
      // Determine if no extraction has been run yet
      const noExtractionYet = extractorResult === null || extractorResult === undefined
      
      // Check if this extractor is currently extracting
      const isCurrentlyExtracting = extractorStatus.value[extractor.model] === 'loading'
      
      // Build the result object
      const result = {
        name: fieldName,
        value: noExtractionYet ? null : (fieldData?.value ?? null),
        type: fieldType || 'text',
        groundTruth,
        noExtractionYet,
        isExtracting: isCurrentlyExtracting,
        correctnessScore: null as number | null
      }
      
      // Set correctness score based on ground truth availability
      if (!noExtractionYet && fieldData?.value !== undefined) {
        result.correctnessScore = calculateCorrectness(fieldData.value, groundTruth, fieldType)
      }
      
      results[extractor.model] = result
    }
    
    fields.push({
      name: fieldName,
      results
    })
  }

  // Sort fields alphabetically by name
  fields.sort((a, b) => a.name.localeCompare(b.name))
  
  return fields
})

// --- Methods ---
const initializeExtractorStatus = () => {
  const status: Record<string, 'idle' | 'loading' | 'success' | 'error'> = {}
  computedAvailableExtractors.value.forEach(extractor => {
    // Check if there's an existing extraction for this extractor
    const existingExtraction = documentExtractions.value.find(ext => ext.model === extractor.model)
    
    if (existingExtraction) {
      // Map extraction status to UI status
      switch (existingExtraction.status) {
        case 'pending':
        case 'running':
          status[extractor.model] = 'loading'
          break
        case 'completed':
          status[extractor.model] = 'success'
          break
        case 'failed':
        case 'error':
          status[extractor.model] = 'error'
          break
        default:
          status[extractor.model] = 'idle'
      }
    } else {
      status[extractor.model] = 'idle'
    }
  })
  extractorStatus.value = status
}

// Simplified methods - now using ResourcePool async extraction workflow
const runAllExtractions = async () => {
  if (!props.documentId || !activeConfig.value?.id) return;

  loading.value = true;
  initializeExtractorStatus();

  try {
    // Set all extractors to loading state
    computedAvailableExtractors.value.forEach(extractor => {
      extractorStatus.value[extractor.model] = 'loading'
    })

    // Trigger all extractions via tRPC (async + Redis queue)
    const extractorModels = computedAvailableExtractors.value.map(ext => ext.model);
    
    const result = await runExtractions([props.documentId], activeConfig.value.id, extractorModels)

    console.log('✅ All extractions queued:', result);
    
    // Note: Status updates will come via WebSocket and update the UI reactively
    
  } catch (error) {
    console.error('❌ Failed to queue all extractions:', error);
    // Reset status on error
    computedAvailableExtractors.value.forEach(extractor => {
      extractorStatus.value[extractor.model] = 'error'
    })
  } finally {
    loading.value = false;
  }
};

const runSingleExtraction = async (extractorId: string) => {

  console.log(`🚀 Queuing extraction for ${extractorId}`)
  extractorStatus.value[extractorId] = 'loading'

  try {
    const result = await runExtraction(props.documentId, activeConfig.value.id, extractorId)

    console.log(`✅ Extraction queued for ${extractorId}:`, result)
    
    // Note: Completion updates will come via WebSocket and update the UI reactively
    
  } catch (error) {
    console.error(`❌ Failed to queue extraction for ${extractorId}:`, error)
    extractorStatus.value[extractorId] = 'error'
    
    // Show user-friendly error message
    let errorMessage = 'Failed to queue extraction';
    if (error instanceof Error) {
      errorMessage = error.message;
    }
  
  }
};
// Watch for changes in documentExtractions to keep status in sync
watch(documentExtractions, (newExtractions, oldExtractions) => {
  // Only update specific extractors that changed, don't reset all
  newExtractions.forEach(extraction => {
    const extractorModel = extraction.model;
    const oldExtraction = oldExtractions?.find(e => e.model === extractorModel);
    
    // Only update if the extraction actually changed
    if (!oldExtraction || oldExtraction.status !== extraction.status) {
      switch (extraction.status) {
        case 'pending':
        case 'running':
          extractorStatus.value[extractorModel] = 'loading';
          break;
        case 'completed':
          extractorStatus.value[extractorModel] = 'success';
          break;
        case 'failed':
        case 'error':
          extractorStatus.value[extractorModel] = 'error';
          break;
        default:
          extractorStatus.value[extractorModel] = 'idle';
      }
      console.log(`🔄 Updated extractor status for ${extractorModel}: ${extractorStatus.value[extractorModel]} (extraction status: ${extraction.status})`);
    }
  });
}, { deep: true })

// --- Lifecycle ---
onMounted(async () => {
  // Initialize extractor status when component mounts
  initializeExtractorStatus();
  
  // Subscribe to document-specific WebSocket channel for real-time updates
  if (props.documentId) {
    console.log(`📡 DocumentDetail: Subscribing to document channel: document:${props.documentId}`);
    // Subscribe to document-specific channel to ensure we get updates for this specific document
    subscribeToAdditionalChannels([`document:${props.documentId}`]);
  }
});


</script>
<template>
  <div id="app" class="min-h-screen bg-gray-50">
    <!-- Bootstrap loading state -->
    <div
      v-if="isLoading"
      class="fixed inset-0 bg-white bg-opacity-75 flex items-center justify-center z-50"
    >
      <div class="text-center">
        <div
          class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"
        ></div>
        <p class="text-lg text-gray-700">Loading resources...</p>
      </div>
    </div>

    <!-- Error state -->
    <div
      v-else-if="error"
      class="fixed inset-0 bg-white bg-opacity-75 flex items-center justify-center z-50"
    >
      <div class="text-center p-8 bg-red-50 rounded-lg border border-red-200">
        <div class="text-red-600 mb-4">
          <svg
            class="w-12 h-12 mx-auto"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            ></path>
          </svg>
        </div>
        <h2 class="text-xl font-semibold text-red-900 mb-2">Failed to Load</h2>
        <p class="text-red-700 mb-4">{{ error }}</p>
        <button
          @click="retryBootstrap"
          class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
        >
          Retry
        </button>
      </div>
    </div>

    <!-- Main app content -->
    <div v-else-if="isBootstrapped">
      <nav class="bg-white shadow-sm border-b border-gray-200">
        <div class="mx-auto px-4 sm:px-6 lg:px-8">
          <div class="flex justify-between h-16">
            <div class="flex">
              <div class="flex-shrink-0 flex items-center">
                <h1 class="text-xl font-semibold text-gray-900">
                  Cula Extractor
                </h1>
              </div>
              <div class="ml-6 flex space-x-8">
                <router-link
                  to="/"
                  class="inline-flex items-center px-1 pt-1 text-sm font-medium border-b-2 transition-colors"
                  :class="
                    $route.path === '/'
                      ? 'border-blue-500 text-gray-900'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  "
                >
                  Datasets
                </router-link>
              </div>
            </div>
          </div>
        </div>
      </nav>

      <main class="mx-auto px-4">
        <router-view />
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from "vue";
import { useResourcePool } from "./services/resourcePool.js";

const { isBootstrapped, isLoading, error, bootstrap } = useResourcePool();

const retryBootstrap = async () => {
  try {
    await bootstrap();
  } catch (err) {
    console.error("Retry failed:", err);
  }
};

onMounted(async () => {
  console.log("🚀 App: Starting ResourcePool bootstrap...");
  await bootstrap();
});
</script>

Directory structure:
└── frontend/
    ├── components.json
    ├── Dockerfile
    ├── Dockerfile.dev
    ├── index.html
    ├── tailwind.config.js
    ├── tsconfig.app.json
    ├── tsconfig.json
    ├── tsconfig.node.json
    ├── vite.config.ts
    ├── .env.local
    └── src/
        ├── App.vue
        ├── index.css
        ├── index.html
        ├── main.ts
        ├── components/
        │   ├── AccuracyIndicator.vue
        │   ├── DatasetDocumentsOverview.vue
        │   ├── DocumentExtractionsPanel.vue
        │   ├── DocumentItem.vue
        │   ├── DocumentPreview.vue
        │   ├── DocumentsDataTable.vue
        │   ├── ErrorDisplay.vue
        │   ├── ExtractionAccuracyIndicator.vue
        │   ├── ExtractionCardSkeleton.vue
        │   ├── ExtractionField.vue
        │   ├── ExtractionSummaryTab.vue
        │   ├── ExtractorHeader.vue
        │   ├── ExtractorLogo.vue
        │   ├── ExtractorPanel.vue
        │   ├── ExtractorPanelSkeleton.vue
        │   ├── FieldComparison.vue
        │   ├── FieldTypeIcon.vue
        │   ├── ImageViewer.vue
        │   ├── PercentBadge.vue
        │   └── ui/
        │       ├── card/
        │       │   ├── Card.vue
        │       │   ├── CardAction.vue
        │       │   ├── CardContent.vue
        │       │   ├── CardDescription.vue
        │       │   ├── CardFooter.vue
        │       │   ├── CardHeader.vue
        │       │   ├── CardTitle.vue
        │       │   └── index.ts
        │       ├── skeleton/
        │       │   ├── index.ts
        │       │   └── Skeleton.vue
        │       └── table/
        │           ├── index.ts
        │           ├── Table.vue
        │           ├── TableBody.vue
        │           ├── TableCell.vue
        │           ├── TableHead.vue
        │           ├── TableHeader.vue
        │           └── TableRow.vue
        ├── composables/
        │   └── useWebSocketSync.ts
        ├── lib/
        │   ├── trpc.ts
        │   └── utils.ts
        ├── services/
        │   ├── index.ts
        │   └── resourcePool.ts
        ├── types/
        │   └── index.ts
        ├── utils/
        │   ├── correctness.ts
        │   └── errorUtils.ts
        └── views/
            ├── DatasetDetail.vue
            ├── DatasetList.vue
            └── DocumentDetail.vue

================================================
FILE: components.json
================================================
{
  "$schema": "https://shadcn-vue.com/schema.json",
  "style": "new-york",
  "typescript": true,
  "tailwind": {
    "config": "tailwind.config.js",
    "css": "src/index.css",
    "baseColor": "neutral",
    "cssVariables": true,
    "prefix": ""
  },
  "aliases": {
    "components": "@/components",
    "composables": "@/composables",
    "utils": "@/lib/utils",
    "ui": "@/components/ui",
    "lib": "@/lib"
  },
  "iconLibrary": "lucide"
}


================================================
FILE: Dockerfile
================================================
FROM node:20-slim AS base
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
RUN corepack enable
COPY . /app
WORKDIR /app

FROM base AS prod-deps
RUN --mount=type=cache,id=pnpm,target=/pnpm/store pnpm install --prod --frozen-lockfile

FROM base AS build
RUN --mount=type=cache,id=pnpm,target=/pnpm/store pnpm install --frozen-lockfile
RUN pnpm run build

FROM base
COPY --from=prod-deps /app/node_modules /app/node_modules
COPY --from=build /app/dist /app/dist
EXPOSE 3000
CMD [ "pnpm", "start" ]


================================================
FILE: Dockerfile.dev
================================================
FROM node:20-alpine

ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"

RUN npm install -g pnpm

WORKDIR /app

COPY package.json pnpm-lock.yaml* ./

RUN pnpm install

EXPOSE 3000

CMD ["pnpm", "run", "dev"]


================================================
FILE: index.html
================================================
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Cula Document Extractor</title>
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html> 


================================================
FILE: tailwind.config.js
================================================
/** @type {import('tailwindcss').Config} */
export default {
    darkMode: ["class"],
    content: [
    "./index.html",
    "./src/**/*.{vue,js,ts,jsx,tsx}",
  ],
  theme: {
  	extend: {
  		colors: {
  			background: 'hsl(var(--background))',
  			foreground: 'hsl(var(--foreground))',
  			card: {
  				DEFAULT: 'hsl(var(--card))',
  				foreground: 'hsl(var(--card-foreground))'
  			},
  			'card-foreground': 'hsl(var(--card-foreground))',
  			popover: {
  				DEFAULT: 'hsl(var(--popover))',
  				foreground: 'hsl(var(--popover-foreground))'
  			},
  			'popover-foreground': 'hsl(var(--popover-foreground))',
  			primary: {
  				DEFAULT: 'hsl(var(--primary))',
  				foreground: 'hsl(var(--primary-foreground))'
  			},
  			'primary-foreground': 'hsl(var(--primary-foreground))',
  			secondary: {
  				DEFAULT: 'hsl(var(--secondary))',
  				foreground: 'hsl(var(--secondary-foreground))'
  			},
  			'secondary-foreground': 'hsl(var(--secondary-foreground))',
  			muted: {
  				DEFAULT: 'hsl(var(--muted))',
  				foreground: 'hsl(var(--muted-foreground))'
  			},
  			'muted-foreground': 'hsl(var(--muted-foreground))',
  			accent: {
  				DEFAULT: 'hsl(var(--accent))',
  				foreground: 'hsl(var(--accent-foreground))'
  			},
  			'accent-foreground': 'hsl(var(--accent-foreground))',
  			destructive: {
  				DEFAULT: 'hsl(var(--destructive))',
  				foreground: 'hsl(var(--destructive-foreground))'
  			},
  			'destructive-foreground': 'hsl(var(--destructive-foreground))',
  			border: 'hsl(var(--border))',
  			input: 'hsl(var(--input))',
  			ring: 'hsl(var(--ring))',
  			chart: {
  				'1': 'hsl(var(--chart-1))',
  				'2': 'hsl(var(--chart-2))',
  				'3': 'hsl(var(--chart-3))',
  				'4': 'hsl(var(--chart-4))',
  				'5': 'hsl(var(--chart-5))'
  			}
  		},
  		borderRadius: {
  			lg: 'var(--radius)',
  			md: 'calc(var(--radius) - 2px)',
  			sm: 'calc(var(--radius) - 4px)'
  		}
  	}
  },
  plugins: [require("tailwindcss-animate")],
}


================================================
FILE: tsconfig.app.json
================================================
{
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@/*": [
        "./src/*"
      ]
    }
  }
}


================================================
FILE: tsconfig.json
================================================
{
  "files": [],
  "references": [
    {
      "path": "./tsconfig.app.json"
    },
    {
      "path": "./tsconfig.node.json"
    }
  ],
  "compilerOptions": {
    "moduleResolution": "bundler",
    "baseUrl": ".",
    "paths": {
      "@/*": [
        "./src/*"
      ]
    }
  }
}


================================================
FILE: tsconfig.node.json
================================================
{
  "compilerOptions": {
    "composite": true,
    "skipLibCheck": true,
    "module": "ESNext",
    "moduleResolution": "bundler",
    "allowSyntheticDefaultImports": true
  },
  "include": ["vite.config.ts"]
} 


================================================
FILE: vite.config.ts
================================================
import vue from '@vitejs/plugin-vue'
import tailwindcss from '@tailwindcss/vite'
import { defineConfig } from 'vite'
import { resolve } from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue(), tailwindcss()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },

  server: {
    port: 3000,
    host: true,
    proxy: {
      '/api': {
        target: process.env.VITE_API_BASE_URL || 'http://localhost:8000',
        changeOrigin: true,
      }
    }
  },
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    target: 'es2022',
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router'],
          codemirror: ['vue-codemirror', '@codemirror/lang-sql', '@codemirror/theme-one-dark']
        }
      }
    }
  },
  define: {
    __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: 'true'
  }
})


================================================
FILE: .env.local
================================================
VITE_API_BASE_URL=http://localhost:8000


================================================
FILE: src/App.vue
================================================
<template>
  <div id="app" class="min-h-screen bg-gray-50">
    <!-- Bootstrap loading state -->
    <div v-if="isLoading" class="fixed inset-0 bg-white bg-opacity-75 flex items-center justify-center z-50">
      <div class="text-center">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p class="text-lg text-gray-700">Loading resources...</p>
      </div>
    </div>

    <!-- Error state -->
    <div v-else-if="error" class="fixed inset-0 bg-white bg-opacity-75 flex items-center justify-center z-50">
      <div class="text-center p-8 bg-red-50 rounded-lg border border-red-200">
        <div class="text-red-600 mb-4">
          <svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <h2 class="text-xl font-semibold text-red-900 mb-2">Failed to Load</h2>
        <p class="text-red-700 mb-4">{{ error }}</p>
        <button 
          @click="retryBootstrap"
          class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
        >
          Retry
        </button>
      </div>
    </div>

    <!-- Main app content -->
    <div v-else-if="isBootstrapped">
      <nav class="bg-white shadow-sm border-b border-gray-200">
        <div class="mx-auto px-4 sm:px-6 lg:px-8">
          <div class="flex justify-between h-16">
            <div class="flex">
              <div class="flex-shrink-0 flex items-center">
                <h1 class="text-xl font-semibold text-gray-900">Cula Extractor</h1>
              </div>
              <div class="ml-6 flex space-x-8">
                <router-link
                  to="/"
                  class="inline-flex items-center px-1 pt-1 text-sm font-medium border-b-2 transition-colors"
                  :class="$route.path === '/' ? 'border-blue-500 text-gray-900' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                >
                  Datasets
                </router-link>
              </div>
            </div>
          </div>
        </div>
      </nav>

      <main class="mx-auto py-6 sm:px-6 lg:px-8">
        <router-view />
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue';
import { useResourcePool } from './services/resourcePool.js';

const { 
  isBootstrapped, 
  isLoading, 
  error, 
  bootstrap, 
} = useResourcePool();

const retryBootstrap = async () => {
  try {
    await bootstrap();
  } catch (err) {
    console.error('Retry failed:', err);
  }
};

onMounted(async () => {
  console.log('🚀 App: Starting ResourcePool bootstrap...');
  await bootstrap();
});
</script> 


================================================
FILE: src/index.css
================================================
@import "tailwindcss";

/* CSS Custom Properties for shadcn components */
:root {
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;
  --primary: 222.2 47.4% 11.2%;
  --primary-foreground: 210 40% 98%;
  --secondary: 210 40% 96%;
  --secondary-foreground: 222.2 84% 4.9%;
  --muted: 210 40% 96%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --accent: 210 40% 96%;
  --accent-foreground: 222.2 84% 4.9%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 222.2 84% 4.9%;
  --radius: 0.5rem;
}

.dark {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  --card: 222.2 84% 4.9%;
  --card-foreground: 210 40% 98%;
  --popover: 222.2 84% 4.9%;
  --popover-foreground: 210 40% 98%;
  --primary: 210 40% 98%;
  --primary-foreground: 222.2 84% 4.9%;
  --secondary: 217.2 32.6% 17.5%;
  --secondary-foreground: 210 40% 98%;
  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 215 20.2% 65.1%;
  --accent: 217.2 32.6% 17.5%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;
  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --ring: 212.7 26.8% 83.9%;
}

/* Base styles */
* {
  border-color: hsl(var(--border));
}

body {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
}

/* Default text colors for common elements */
h1, h2, h3, h4, h5, h6 {
  color: hsl(var(--foreground));
}

p, span, div {
  color: hsl(var(--foreground));
}

/* Table specific styles */
table {
  color: hsl(var(--foreground));
}

th {
  color: hsl(var(--muted-foreground));
  border-bottom: 1px solid hsl(var(--border));
}

td {
  color: hsl(var(--foreground));
  border-bottom: 1px solid hsl(var(--border));
}

tr:hover {
  background-color: hsl(var(--muted) / 0.5);
}


================================================
FILE: src/index.html
================================================
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document Extraction Viewer</title>
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html> 


================================================
FILE: src/main.ts
================================================
import { createApp } from 'vue'
import { createRouter, createWebHistory } from 'vue-router'
import App from './App.vue'
import DocumentDetail from './views/DocumentDetail.vue'
import DatasetList from './views/DatasetList.vue'
import DatasetDetail from './views/DatasetDetail.vue'
import { resourcePool } from './services/resourcePool'
import './index.css'

const routes = [
  { path: '/', redirect: '/datasets' },
  { path: '/datasets/:datasetId/documents/:documentId', component: DocumentDetail, name: 'DocumentDetail', props: true },
    { path: '/datasets', component: DatasetList, name: 'DatasetList' },
  { path: '/datasets/:id', component: DatasetDetail, name: 'DatasetDetail', props: true },
]

const router = createRouter({
  history: createWebHistory(),
  routes,
})

const app = createApp(App)
app.use(router)
app.mount('#app')

// Attach resourcePool to window for debugging
if (typeof window !== 'undefined') {
  (window as any).resourcePool = resourcePool
} 


================================================
FILE: src/components/AccuracyIndicator.vue
================================================
<template>
  <div class="flex flex-col items-center space-y-1">
    <!-- Circular Progress Indicator -->
    <div class="relative w-16 h-16">
      <!-- Background Circle -->
      <svg class="w-16 h-16 transform -rotate-90" viewBox="0 0 36 36">
        <path
          class="stroke-current text-muted/30"
          fill="none"
          stroke-width="3"
          d="M18 2.0845
            a 15.9155 15.9155 0 0 1 0 31.831
            a 15.9155 15.9155 0 0 1 0 -31.831"
        />
        <!-- Progress Circle -->
        <path
          class="stroke-current transition-all duration-300 ease-in-out"
          :class="getStrokeColorClass(percentage)"
          fill="none"
          stroke-width="3"
          stroke-linecap="round"
          :stroke-dasharray="`${circumference}, ${circumference}`"
          :stroke-dashoffset="circumference - (percentage / 100) * circumference"
          d="M18 2.0845
            a 15.9155 15.9155 0 0 1 0 31.831
            a 15.9155 15.9155 0 0 1 0 -31.831"
        />
      </svg>
      
      <!-- Percentage Text -->
      <div class="absolute inset-0 flex items-center justify-center">
        <span class="text-xs font-bold text-foreground">
          {{ Math.round(percentage) }}%
        </span>
      </div>
    </div>
    
    <!-- Stats Text -->
    <div class="text-xs text-muted-foreground text-center">
      <div class="font-medium">{{ correct }}/{{ total }}</div>
      <div>fields</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  percentage: number
  correct: number
  total: number
}

const props = withDefaults(defineProps<Props>(), {
  percentage: 0,
  correct: 0,
  total: 0
})

const circumference = computed(() => 2 * Math.PI * 15.9155)

const getStrokeColorClass = (percentage: number): string => {
  if (percentage >= 90) return 'text-green-500'
  if (percentage >= 80) return 'text-green-400'
  if (percentage >= 70) return 'text-yellow-500'
  if (percentage >= 60) return 'text-orange-500'
  if (percentage >= 50) return 'text-red-400'
  return 'text-red-500'
}
</script> 


================================================
FILE: src/components/DatasetDocumentsOverview.vue
================================================
<template>
  <div class="bg-card border border-border rounded-lg shadow-sm">
    <div class="p-6 border-b border-border">
      <div class="flex items-center justify-between">
        <div>
          <h2 class="text-xl font-semibold text-foreground">Documents</h2>
          <p class="text-sm text-muted-foreground mt-1">
            {{ documents.length }} document{{ documents.length !== 1 ? 's' : '' }} found
            <span v-if="extractedCount > 0" class="ml-2 text-green-600">
              ({{ extractedCount }} extracted)
            </span>
            <span v-if="approvalSummary.approved > 0" class="ml-2 text-green-600">
              ({{ approvalSummary.approved }} approved)
            </span>
            <span v-if="approvalSummary.pending > 0" class="ml-2 text-yellow-600">
              ({{ approvalSummary.pending }} pending)
            </span>
          </p>
        </div>
        <div class="flex items-center gap-3">
          <div v-if="isLoadingDocs" class="flex items-center gap-2 text-sm text-muted-foreground">
            <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
            Loading documents...
          </div>
          <div v-if="documents.length > 0 && config" class="flex items-center gap-2">
            <button
              @click="runBatchExtraction(false)"
              :disabled="isBatchExtracting || extractionTasksToStart === 0"
              class="px-3 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors flex items-center gap-2"
            >
              <Play class="h-4 w-4" />
              Run Missing ({{ extractionTasksToStart }})
            </button>
            <button
              @click="runBatchExtraction(true)"
              :disabled="isBatchExtracting"
              class="px-3 py-2 text-sm bg-gray-600 text-white rounded-md hover:bg-gray-700 disabled:opacity-50 transition-colors flex items-center gap-2"
            >
              <Play class="h-4 w-4" />
              Run All
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <div v-if="documents.length === 0 && !isLoadingDocs" class="p-8 text-center text-muted-foreground">
      <FileText class="h-12 w-12 mx-auto mb-3 opacity-50" />
      <p>No documents found</p>
      <p class="text-sm mt-1">Check your SQL query to ensure it returns document paths</p>
    </div>
    
    <ul v-else class="divide-y divide-border">
      <li 
        v-for="doc in documents" 
        :key="doc.id" 
        class="p-4 hover:bg-muted/50 transition-colors"
      >
        <div class="flex items-center space-x-4">
          <div class="flex-shrink-0">
            <FileText class="h-5 w-5 text-muted-foreground" />
          </div>
          <div class="flex-1 min-w-0 cursor-pointer" @click="viewDocument(doc.id)">
            <div class="font-mono text-sm font-medium text-foreground truncate">
              {{ doc.gcsPath }}
            </div>
            <div class="flex items-center gap-4 text-xs text-muted-foreground mt-1">
              <span v-if="doc.siteName" class="flex items-center gap-1">
                <MapPin class="h-3 w-3" />
                {{ doc.siteName }}
              </span>
              <span v-if="doc.objectType" class="flex items-center gap-1">
                <Tag class="h-3 w-3" />
                {{ doc.objectType }}
              </span>
              <span v-if="doc.emissionsLogId" class="flex items-center gap-1">
                <Hash class="h-3 w-3" />
                {{ doc.emissionsLogId }}
              </span>
            </div>
          </div>
          <div class="flex items-center gap-4 flex-shrink-0">
            <!-- Needs Approval Badge for individual documents -->
            <div 
              v-if="(doc.isApproved === false || doc.isApproved === null)"
              class="flex items-center gap-1 px-2 py-1 bg-yellow-100 text-yellow-800 border border-yellow-300 rounded-md text-xs font-medium"
            >
              <Clock class="h-3 w-3" />
              Needs Approval
            </div>
            
            <!-- Approval Status Indicator -->
            <div class="flex items-center">
              <span 
                v-if="doc.isApproved !== undefined"
                :class="{
                  'bg-green-100 text-green-700': doc.isApproved === true,
                  'bg-yellow-100 text-yellow-700': doc.isApproved === false,
                  'bg-gray-100 text-gray-600': doc.isApproved === null
                }"
                class="px-2 py-1 text-xs font-medium rounded-full flex items-center gap-1"
                :title="doc.isApproved === true ? `Approved${doc.approvedAt ? ' on ' + new Date(doc.approvedAt).toLocaleDateString() : ''}${doc.approvedBy ? ' by ' + doc.approvedBy : ''}` : 
                        doc.isApproved === false ? 'Pending Approval' : 'Not Reviewed'"
              >
                <CheckCircle v-if="doc.isApproved === true" class="h-3 w-3" />
                <Clock v-else-if="doc.isApproved === false" class="h-3 w-3" />
                <Circle v-else class="h-3 w-3" />
                {{ doc.isApproved === true ? 'Approved' : doc.isApproved === false ? 'Pending' : 'Not Reviewed' }}
              </span>
            </div>
            
            <!-- Ground Truth Type Indicator -->
            <div class="flex items-center">
              <span 
                :class="{
                  'bg-gray-100 text-gray-600': getGroundTruthType(doc.id).type === 'none',
                  'bg-blue-100 text-blue-700': getGroundTruthType(doc.id).type === 'database',
                  'bg-green-100 text-green-700': getGroundTruthType(doc.id).type === 'annotated',
                  'bg-purple-100 text-purple-700': getGroundTruthType(doc.id).type === 'approved'
                }"
                class="px-2 py-1 text-xs font-medium rounded-full"
              >
                {{ getGroundTruthType(doc.id).label }}
              </span>
            </div>
            
            <!-- Extractor Panels -->
            <div class="flex flex-col gap-2">
              <ExtractorPanel 
                v-for="extractor in effectiveExtractors" 
                :key="extractor"
                :extractor="extractor"
                :accuracy="documentAccuracies.get(doc.id)?.[extractor] || null"
                :has-extraction="hasExtractorRun(doc.id, extractor)"
                :is-extracting="extractingDocs.has(`${doc.id}-${extractor}`)"
                :disabled="!config || extractingDocs.has(`${doc.id}-${extractor}`)"
                :extraction-status="extractionStatuses.get(`${doc.id}-${extractor}`) || null"
                @extract="() => {}"
              />
              
              <!-- Show "No extractions" if no methods found and no extractions exist -->
              <div v-if="!documentExtractions.has(doc.id) && effectiveExtractors.length === 0" class="px-2 py-0.5 text-xs font-medium rounded border bg-gray-100 text-gray-400 border-gray-200">
                No extractors available
              </div>
            </div>
            
            <ChevronRight class="h-5 w-5 text-muted-foreground cursor-pointer" @click="viewDocument(doc.id)" />
          </div>
        </div>
      </li>
    </ul>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useResourcePool } from '../services';
import type { Document, ExtractionConfig, ExtractionField } from '../types';
import ExtractorPanel from './ExtractorPanel.vue';
import { calculateCorrectness } from '@/utils/correctness';
import { 
  FileText, 
  ChevronRight, 
  MapPin,
  Tag,
  Hash,
  Play,
  CheckCircle,
  Clock,
  Circle
} from 'lucide-vue-next';

// Business logic APIs that aren't in tRPC yet - use direct API calls
const businessAPI = {
  async runBatchExtraction(documentIds: string[], configId: string, extractors: any[], includeCompleted: boolean) {
    const response = await fetch('/api/extractions/batch', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        document_ids: documentIds, 
        extraction_config_id: configId, 
        extractors: extractors,
        include_completed: includeCompleted 
      })
    });
    return response.json();
  }
};

interface Props {
  documents: Document[];
  config: ExtractionConfig | null;
  isLoadingDocs: boolean;
  datasetId: string;
  extractionData: Map<string, any>;
  groundTruthData: Map<string, any>;
  approvalData: Map<string, any>;
  extractionStatusData: Map<string, any>;
  availableExtractors: string[];
}

const props = defineProps<Props>();
const router = useRouter();

const documentAccuracies = computed(() => {
  const allAccuracies = new Map<string, Record<string, any>>();
  for (const doc of props.documents) {
    allAccuracies.set(doc.id, getDocumentAccuracies(doc.id));
  }
  return allAccuracies;
});

const formattedConfigFields = computed(() => {
  if (!props.config?.data.fields) {
    return undefined;
  }
  return props.config.data.fields.reduce((acc: Record<string, { field_type: string }>, field: ExtractionField) => {
    acc[field.name] = { field_type: field.fieldType };
    return acc;
  }, {});
});

// Extraction status tracking
interface ExtractionStatus {
  status: string;
  progress: number;
  message: string;
}

interface DocumentExtraction {
  document_id: string;
  last_extracted: string;
  extracted_fields: number;
  extraction_results: any[];
}

// Computed property that combines backend extractors with config extractors
const effectiveExtractors = computed(() => {
  const backendExtractors = props.availableExtractors || [];
  const configExtractors = props.config?.data?.extractors?.map(e => e.model) || [];
  
  // Use config extractors if available, otherwise fall back to backend extractors
  if (configExtractors.length > 0) {
    return configExtractors;
  }
  
  return backendExtractors;
});
const extractingDocs = ref(new Set<string>()); // Key: `${doc.id}-${extractor}`
const extractionStatuses = computed(() => props.extractionStatusData);

// Remove the local reactive state and use props instead
// const documentExtractions = ref<Map<string, DocumentExtraction>>(new Map());
// const documentGroundTruth = ref<Map<string, Record<string, any>>>(new Map());
// const documentApprovals = ref<Map<string, any>>(new Map());

// Use props directly
const documentExtractions = computed(() => props.extractionData);
const documentGroundTruth = computed(() => props.groundTruthData);
const documentApprovals = computed(() => props.approvalData);

const getApprovalStatus = (documentId: string) => {
  return documentApprovals.value.get(documentId);
};

const getApprovalStatusText = (documentId: string) => {
  const status = getApprovalStatus(documentId);
  if (!status) return 'Not Reviewed';
  
  if (status.isApproved === true) return 'Approved';
  if (status.isApproved === false) return 'Pending';
  return 'Not Reviewed';
};

const getApprovalTooltip = (documentId: string) => {
  const status = getApprovalStatus(documentId);
  if (!status) return 'Not reviewed for approval';
  
  if (status.isApproved === true) {
    let tooltip = 'Approved';
    if (status.approvedAt) {
      tooltip += ` on ${new Date(status.approvedAt).toLocaleDateString()}`;
    }
    if (status.approvedBy) {
      tooltip += ` by ${status.approvedBy}`;
    }
    return tooltip;
  }
  
  if (status.isApproved === false) return 'Pending approval';
  return 'Not reviewed for approval';
};

const approvalSummary = computed(() => {
  let approved = 0;
  let pending = 0;
  let notReviewed = 0;
  
  props.documents.forEach(doc => {
    const status = getApprovalStatus(doc.id);
    if (status?.isApproved === true) {
      approved++;
    } else if (status?.isApproved === false) {
      pending++;
    } else {
      notReviewed++;
    }
  });
  
  return {
    approved,
    pending,
    notReviewed,
    total: props.documents.length
  };
});

const runBatchExtraction = async (includeCompleted: boolean) => {
  if (!props.config?.id || effectiveExtractors.value.length === 0) {
    return;
  }

  const documentIds = includeCompleted
    ? props.documents.map(doc => doc.id)
    : documentsWithoutExtractions.value.map(doc => doc.id);

  if (documentIds.length === 0) {
    return;
  }

  const extractors = props.config.data.extractors.map(e => ({
    provider: e.provider,
    model: e.model,
  }));

  try {
    await businessAPI.runBatchExtraction(
      documentIds,
      props.config.id,
      extractors,
      includeCompleted
    );
  } catch (error) {
    console.error('Failed to start batch extraction:', error);
  }
};

const isBatchExtracting = computed(() => {
  return [...extractingDocs.value].some(key => key.includes('-batch-'));
});

const extractedCount = computed(() => {
  return props.documents.filter(doc => documentExtractions.value.has(doc.id)).length;
});

const documentsWithExtractions = computed(() => {
  return props.documents.filter(doc => documentExtractions.value.has(doc.id));
});

const documentsWithoutExtractions = computed(() => {
  if (effectiveExtractors.value.length === 0) return props.documents;
  
  return props.documents.filter(doc => {
    const docExtractions = documentExtractions.value.get(doc.id);
    if (!docExtractions) return true;
    
    // Check if document has extractions for all available extractors
    const hasAllExtractions = effectiveExtractors.value.every(extractor => 
      docExtractions.extraction_results.some((r: any) => r.model === extractor)
    );
    
    // Include document if it doesn't have all extractions
    return !hasAllExtractions;
  });
});

const extractionTasksToStart = computed(() => {
  if (effectiveExtractors.value.length === 0) return 0;
  
  let totalTasks = 0;
  documentsWithoutExtractions.value.forEach(doc => {
    const docExtractions = documentExtractions.value.get(doc.id);
    if (!docExtractions) {
      // Document has no extractions, so all extractors need to run
      totalTasks += effectiveExtractors.value.length;
    } else {
      // Count missing extractors for this document
      const missingExtractors = effectiveExtractors.value.filter(extractor => 
        !docExtractions.extraction_results.some((r: any) => r.model === extractor)
      );
      totalTasks += missingExtractors.length;
    }
  });
  
  return totalTasks;
});

const hasRunningExtractions = computed(() => {
  return extractingDocs.value.size > 0;
});

const viewDocument = (docId: string) => {
  router.push({ 
    name: 'DocumentDetail', 
    params: { datasetId: props.datasetId, documentId: docId }, 
    query: { extraction_config_id: props.config?.id }
  });
};

// Remove the local loadExtractionResults function
// const loadExtractionResults = async () => {
//   if (!props.config) return;
  
//   try {
//     // Process documents in smaller batches to avoid overwhelming the API
//     const batchSize = 5; // Process 5 documents at a time
//     const batches = [];
    
//     for (let i = 0; i < props.documents.length; i += batchSize) {
//       batches.push(props.documents.slice(i, i + batchSize));
//     }
    
//     const allResults = [];
    
//     // Process each batch sequentially with a small delay
//     for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
//       const batch = batches[batchIndex];
      
//       const batchResults = await Promise.allSettled(
//         batch.map(async (doc) => {
//           try {
//             const [extractions, detailedGroundTruth, approvalStatus] = await Promise.all([
//               extractionService.getExtractions(doc.id),
//               documentService.getDetailedGroundTruth(doc.id).catch(() => ({})),
//               props.config?.id ? documentService.getApprovalStatus(doc.id, props.config.id).catch(() => null) : Promise.resolve(null)
//             ]);

//             let extractionData = null;
//             if (extractions && extractions.length > 0) {
//               const latest = extractions.sort((a, b) => 
//                 new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
//               )[0];
              
//               extractionData = {
//                 document_id: doc.id,
//                 last_extracted: latest.created_at,
//                 extracted_fields: Object.keys(latest.data || {}).length,
//                 extraction_results: extractions
//               };
//             }

//             return {
//               docId: doc.id,
//               extractions: extractionData,
//               groundTruth: detailedGroundTruth,
//               approval: approvalStatus?.approval_status || null
//             };
//           } catch (error) {
//             console.warn(`Could not load data for document ${doc.id}:`, error);
//             return { docId: doc.id, extractions: null, groundTruth: {}, approval: null };
//           }
//         })
//       );
      
//       allResults.push(...batchResults);
      
//       // Add a small delay between batches to avoid overwhelming the API
//       if (batchIndex < batches.length - 1) {
//         await new Promise(resolve => setTimeout(resolve, 200));
//       }
//     }
    
//     const results = allResults;
    
//     documentExtractions.value.clear();
//     documentGroundTruth.value.clear();
//     documentApprovals.value.clear();
    
//     results.forEach((result) => {
//       if (result.status === 'fulfilled' && result.value) {
//         const { docId, extractions, groundTruth, approval } = result.value;
//         if (extractions) {
//           documentExtractions.value.set(docId, extractions);
//         }
//         documentGroundTruth.value.set(docId, groundTruth);
//         if (approval) {
//           documentApprovals.value.set(docId, approval);
//         }
//       }
//     });
    
//   } catch (error) {
//     console.error('Failed to load extraction results:', error);
//   }
// };

const hasExtractorRun = (docId: string, extractor: string): boolean => {
  const docExtractions = documentExtractions.value.get(docId);
  if (!docExtractions) {
    return false;
  }
  return docExtractions.extraction_results.some((r: any) => r.model === extractor);
};


const getGroundTruthType = (docId: string): { type: 'none' | 'database' | 'annotated' | 'approved', label: string } => {
  // Find the document to check approval status
  const doc = props.documents.find(d => d.id === docId);
  
  // Check for approval first (highest priority)
  if (doc && doc.isApproved === true) {
    return { type: 'approved', label: 'Approved' };
  }
  
  const detailedGroundTruth = documentGroundTruth.value.get(docId);
  
  if (!detailedGroundTruth || Object.keys(detailedGroundTruth).length === 0) {
    return { type: 'none', label: 'No Ground Truth' };
  }

  const fields = Object.values(detailedGroundTruth);
  const hasAnnotation = fields.some((field: any) => field.user_annotation !== null && field.user_annotation !== undefined);
  if (hasAnnotation) {
    return { type: 'annotated', label: 'Annotated' };
  }
  
  const hasExtractedValue = fields.some((field: any) => field.extracted_value !== null && field.extracted_value !== undefined);
  if (hasExtractedValue) {
    return { type: 'database', label: 'Database GT' };
  }
  
  return { type: 'none', label: 'No Ground Truth' };
};

const getDocumentAccuracies = (docId: string) => {
  const extractionData = documentExtractions.value.get(docId);
  const groundTruthData = documentGroundTruth.value.get(docId) || {};
  
  if (!extractionData || !extractionData.extraction_results) {
    return {};
  }
  
  const accuracies: Record<string, {
    correct: number;
    total: number;
    percentage: number;
    hasGroundTruth: boolean;
  }> = {};
  
  // Group extractions by method
  const extractionsByMethod: Record<string, any> = {};
  extractionData.extraction_results.forEach((extraction: any) => {
    // Find the matching extractor name using includes logic
    const matchingExtractor = effectiveExtractors.value.find(extractor => 
      extraction.model === extractor
    );
    if (matchingExtractor) {
      extractionsByMethod[matchingExtractor] = extraction.extracted_data;
    }
  });
  
  // Calculate accuracy for each method
  Object.entries(extractionsByMethod).forEach(([method, extractedData]) => {
    let correct = 0;
    let total = 0;
    let hasAnyGroundTruth = false;
    
    // Compare each field with ground truth
    Object.entries(groundTruthData).forEach(([fieldName, groundTruthField]: [string, any]) => {
      // Extract ground truth using same logic as DocumentDetail.vue
      // If user_annotation property exists (even if null), use it; otherwise use extracted_value
      const groundTruthValue = groundTruthField?.hasOwnProperty('user_annotation') 
        ? groundTruthField.user_annotation 
        : (groundTruthField?.extracted_value ?? undefined);
      
      // Only count fields that have some form of ground truth (including null for "not extractable")
      if (groundTruthValue !== undefined) {
        hasAnyGroundTruth = true;
        total++;
        
        const extractedValue = extractedData?.[fieldName];
        if (extractedValue !== undefined) {
          // Get field type for comparison
          const fieldConfig = formattedConfigFields.value?.[fieldName];
          const fieldType = fieldConfig?.field_type || 'text';
          
          // Use improved correctness calculation that handles null values properly
          const correctnessScore = calculateCorrectness(extractedValue, groundTruthValue, fieldType);
          // Consider it correct if score is >= 70%
          if (correctnessScore >= 70) {
            correct++;
          }
        }
      }
    });
    
    // Always include the method in accuracies, even if no ground truth
    accuracies[method] = {
      correct,
      total,
      percentage: total > 0 ? (correct / total) * 100 : 0,
      hasGroundTruth: hasAnyGroundTruth
    };
  });
  
  return accuracies;
};


let abortController: AbortController | null = null;

// Remove the debounced loader since we now use props
// const loadExtractionResultsDebounced = (() => {
//   let timeout: NodeJS.Timeout;
//   return () => {
//     clearTimeout(timeout);
//     timeout = setTimeout(loadExtractionResults, 300);
//   };
// })();

// watch(() => props.documents, loadExtractionResultsDebounced, { deep: true });
</script> 


================================================
FILE: src/components/DocumentExtractionsPanel.vue
================================================
<template>
  <div class="bg-card border border-border rounded-lg overflow-hidden lg:col-span-2">
    <div class="border-b border-border p-4">
      <div class="flex items-center justify-between">
        <div>
          <h2 class="text-lg font-semibold text-foreground">Extraction Comparison</h2>
          <p class="text-sm text-muted-foreground">
            Side-by-side results from different models
          </p>
        </div>
      </div>
    </div>
    
    <div class="p-4 overflow-y-auto h-full">
      <div v-if="isInitializing" class="space-y-4">
        <!-- Skeleton for extractor headers -->
        <div class="grid gap-4 pb-2 border-b border-border" :style="{ gridTemplateColumns: `repeat(${Math.max(availableExtractors.length + 1, 3)}, minmax(0, 1fr))` }">
          <div class="col-span-1"></div>
          <div v-for="i in Math.max(availableExtractors.length, 2)" :key="i" class="text-center col-span-1 space-y-2">
            <div class="flex items-center justify-center space-x-3 mb-3">
              <Skeleton class="w-6 h-6 rounded" />
              <Skeleton class="h-4 w-16" />
              <Skeleton class="w-5 h-5 rounded" />
            </div>
            <Skeleton class="w-8 h-8 rounded-lg mx-auto" />
            <Skeleton class="h-3 w-20 mx-auto" />
          </div>
        </div>
        
        <!-- Skeleton for field rows -->
        <div class="space-y-2">
          <div v-for="i in 5" :key="i" class="border border-border rounded-lg p-4">
            <div class="grid gap-4" :style="{ gridTemplateColumns: `repeat(${Math.max(availableExtractors.length + 1, 3)}, minmax(0, 1fr))` }">
              <div class="space-y-2">
                <Skeleton class="h-4 w-24" />
                <Skeleton class="h-3 w-16" />
              </div>
              <div v-for="j in Math.max(availableExtractors.length, 2)" :key="j" class="space-y-2">
                <Skeleton class="h-4 w-full" />
                <Skeleton class="h-3 w-3/4" />
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="space-y-2">
        <!-- Extractor Headers -->
       
        <!-- Field Comparisons -->
        <div v-if="!isInitializing && extractionConfig" class="space-y-2">
          <FieldComparison
            v-for="field in comparedFields"
            :key="field.name"
            :field="field"
            :extraction-providers="availableExtractors"
            :ground-truth-data="groundTruthData"
            @update-annotation="handleAnnotationUpdate" @remove-annotation="handleAnnotationRemoval"
          />
        </div>
        
        <!-- Failed Extractions Error Row - Sticky at bottom -->
        <div 
          v-if="!isInitializing && failedExtractions.length > 0" 
          class="sticky bottom-0 bg-red-50 border border-red-200 rounded-lg p-4 mt-4 shadow-lg z-10"
        >
          <div class="flex items-start space-x-3">
            <XCircle class="h-5 w-5 text-red-500 flex-shrink-0 mt-0.5" />
            <div class="flex-1">
              <h3 class="text-sm font-medium text-red-800 mb-2">
                Failed Extractions ({{ failedExtractions.length }})
              </h3>
              <div class="space-y-2">
                <div 
                  v-for="failed in failedExtractions" 
                  :key="failed.extractor.id"
                  class="flex items-start space-x-3 p-2 bg-red-100 rounded border border-red-200"
                >
                  <ExtractorLogo :extractor="failed.extractor.model || 'unknown'" :size="16" />
                  <div class="flex-1 min-w-0">
                    <div class="flex items-center space-x-2">
                      <span class="text-sm font-medium text-red-700">{{ failed.extractor.model }}</span>
                      <span class="text-xs text-red-600 bg-red-200 px-2 py-0.5 rounded">Failed</span>
                    </div>
                    <p class="text-xs text-red-600 mt-1 break-words">{{ failed.error }}</p>
                  </div>
                  <button
                    @click="runSingleExtraction(failed.extractor.id)"
                    :disabled="extractorStatus[failed.extractor.id] === 'loading'"
                    class="p-1 bg-red-600 hover:bg-red-700 text-white rounded text-xs disabled:opacity-50 disabled:cursor-not-allowed transition-colors shadow-sm flex items-center space-x-1"
                    :title="`Retry ${failed.extractor.model} extraction`"
                  >
                    <RotateCcw class="h-3 w-3" />
                    <span class="hidden sm:inline">Retry</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Skeleton loading state for field comparisons -->
        <div v-else-if="isInitializing" class="space-y-4">
          <!-- Skeleton for extractor headers -->
          <div class="grid gap-4 pb-2 border-b border-border" :style="{ gridTemplateColumns: 'repeat(3, minmax(0, 1fr))' }">
            <div class="col-span-1"></div>
            <div v-for="i in 2" :key="i" class="text-center col-span-1 space-y-2">
              <div class="flex items-center justify-center space-x-3 mb-3">
                <Skeleton class="w-6 h-6 rounded" />
                <Skeleton class="h-4 w-16" />
                <Skeleton class="w-5 h-5 rounded" />
              </div>
              <Skeleton class="w-8 h-8 rounded-lg mx-auto" />
              <Skeleton class="h-3 w-20 mx-auto" />
            </div>
          </div>
          
          <!-- Skeleton for loading field rows -->
          <div class="space-y-2">
            <div v-for="i in 4" :key="i" class="border border-border rounded-lg p-4">
              <div class="grid gap-4" :style="{ gridTemplateColumns: 'repeat(3, minmax(0, 1fr))' }">
                <div class="space-y-2">
                  <Skeleton class="h-4 w-24" />
                  <Skeleton class="h-3 w-16" />
                </div>
                <div v-for="j in 2" :key="j" class="space-y-2">
                  <Skeleton class="h-4 w-full" />
                  <Skeleton class="h-3 w-3/4" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { CheckCircle2, XCircle, Play, RotateCcw, Loader2, X } from 'lucide-vue-next'
import FieldComparison from '@/components/FieldComparison.vue'
import ExtractorLogo from '@/components/ExtractorLogo.vue'
import { Skeleton } from '@/components/ui/skeleton'
import { calculateCorrectness } from '@/utils/correctness'
import { useResourcePool } from '@/services/resourcePool.js'

const {
  extractionConfigs, extractions, groundTruths,
} = useResourcePool()


</script>



================================================
FILE: src/components/DocumentItem.vue
================================================
<template>
  <div
    class="bg-card border border-border rounded-lg p-4 hover:bg-accent/50 cursor-pointer transition-colors"
    @click="$emit('click')"
  >
    <div class="flex items-start justify-between">
      <div class="flex-1">
        <div class="flex items-center space-x-3">
          <div class="p-2 bg-primary/10 rounded-md">
            <FileText class="h-5 w-5 text-primary" />
          </div>
          <div>
            <h3 class="font-medium text-foreground">{{ document.name }}</h3>
            <p class="text-sm text-muted-foreground">{{ document.type }} • {{ formatFileSize(document.size) }}</p>
          </div>
        </div>
        
        <div class="mt-3 flex items-center space-x-4 text-sm text-muted-foreground">
          <span>{{ formatDate(document.createdAt) }}</span>
          <span>Ready for extraction</span>
        </div>
      </div>

      <div class="flex items-center space-x-2">
        <ChevronRight class="h-4 w-4 text-muted-foreground" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { FileText, ChevronRight } from 'lucide-vue-next'
import type { Document } from '@/types'

interface Props {
  document: Document
}

defineProps<Props>()

defineEmits<{
  click: []
}>()

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`
}

const formatDate = (date: string): string => {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  }).format(new Date(date))
}
</script>



================================================
FILE: src/components/DocumentPreview.vue
================================================
<template>
<div class="w-full h-full flex flex-col bg-gray-50">
  <!-- Content Area -->
  <div class="flex-1 flex items-center justify-center overflow-auto">
    <!-- Loading State -->
    <div v-if="loading" class="flex flex-col items-center gap-4">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
      <p class="text-gray-600">Loading document...</p>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="flex flex-col items-center gap-4 text-center max-w-md">
      <div class="p-4 bg-red-50 rounded-lg border border-red-200">
        <AlertCircle class="w-8 h-8 text-red-500 mx-auto mb-2" />
        <p class="text-red-700 font-medium mb-2">Failed to load document</p>
        <p class="text-red-600 text-sm mb-4">{{ error }}</p>
        <button 
          @click="retryLoad" 
          class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
        >
          Retry
        </button>
      </div>
    </div>
    
    <!-- PDF Content -->
    <div v-else-if="isPDF && pdf" class="w-full h-full">
      <div class="h-full flex flex-col">
        <!-- PDF Controls -->
        <div class="flex items-center justify-between p-4 bg-white border-b border-gray-200">
          <div class="flex items-center space-x-4">
            <!-- Show navigation buttons only for multi-page documents -->
            <button 
              v-if="(pages || 0) > 1"
              @click="previousPage" 
              :disabled="currentPage <= 1"
              class="p-2 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
              title="Previous Page"
            >
              <ChevronLeft class="w-4 h-4" />
            </button>
            
            <!-- Always show page indicator -->
            <span class="text-sm text-gray-600 font-medium">
              Page {{ currentPage }} of {{ pages || '...' }}
            </span>
            
            <button 
              v-if="(pages || 0) > 1"
              @click="nextPage" 
              :disabled="currentPage >= (pages || 1)"
              class="p-2 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
              title="Next Page"
            >
              <ChevronRight class="w-4 h-4" />
            </button>
          </div>
          
          <div class="flex items-center space-x-2">
            <button 
              @click="zoomOut" 
              class="p-2 rounded hover:bg-gray-100"
              title="Zoom Out"
            >
              <ZoomOut class="w-4 h-4" />
            </button>
            <span class="text-sm text-gray-600 min-w-[60px] text-center">
              {{ Math.round(scale * 100) }}%
            </span>
            <button 
              @click="zoomIn" 
              class="p-2 rounded hover:bg-gray-100"
              title="Zoom In"
            >
              <ZoomIn class="w-4 h-4" />
            </button>
            <button 
              @click="fitToScreen" 
              class="p-2 rounded hover:bg-gray-100 ml-2"
              title="Fit to Screen"
            >
              <span class="text-xs font-medium">Fit</span>
            </button>
          </div>
        </div>
        
        <!-- PDF Viewer -->
        <div ref="pdfContainerRef" class="flex-1 bg-gray-100 overflow-auto">
          <div class="flex items-center justify-center h-full p-4">
            <Card class="max-w-full max-h-full overflow-hidden">
              <VuePDF 
                :pdf="pdf" 
                :scale="scale" 
                :page="currentPage" 
                text-layer 
                annotation-layer 
                @loaded="onPdfLoaded" 
              />
            </Card>
          </div>
        </div>
      </div>
    </div>

    <!-- Image Content -->
    <div v-else-if="isImage && documentUrl" class="h-full">
      <ImageViewer 
        :image-url="documentUrl"
        :alt="document?.fileName || document?.name || document?.gcsPath || 'Document'"
        :file-size="document?.size || 0"
        @error="onImageError"
      />
    </div>

    <!-- Unsupported File Type -->
    <div v-else-if="!loading && document" class="flex flex-col items-center gap-4 text-center">
      <FileText class="w-16 h-16 text-gray-400" />
      <div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">Preview not available</h3>
        <p class="text-gray-600 mb-4">
          Supported formats: PDF, PNG, JPG, JPEG, GIF, WebP
        </p>
      </div>
    </div>

    <!-- No Document -->
    <div v-else-if="!document" class="flex flex-col items-center gap-4 text-center">
      <FileText class="w-16 h-16 text-gray-400" />
      <p class="text-gray-600">No document selected</p>
    </div>
  </div>
</div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'
import { 
  FileText, 
  ChevronLeft, 
  ChevronRight, 
  ZoomIn, 
  ZoomOut, 
  AlertCircle 
} from 'lucide-vue-next'
import { VuePDF, usePDF } from '@tato30/vue-pdf'
import '@tato30/vue-pdf/style.css'
import ImageViewer from './ImageViewer.vue'
import type { Document } from '@/types'
import { getDocumentStreamingUrl } from '@/services'
import { Card } from '@/components/ui/card'

// Props
interface Props {
  document: Document | null
}
const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'pdf-loaded': [pages: number]
}>()

// Reactive state
const currentPage = ref(1)
const scale = ref(1)
const loading = ref(false)
const error = ref<string | null>(null)
const isManualZoom = ref(false) // Track if user is manually zooming

// PDF container reference
const pdfContainerRef = ref<HTMLElement>()

// Computed properties
const documentUrl = computed(() => {
  if (!props.document) return null
  
  // The document ID (either UUID from DB or filename from GCS)
  const documentIdentifier = props.document.id || (props.document.gcsPath ? props.document.gcsPath.split('/').pop() : null)
  
  if (!documentIdentifier) return null
  
  // The folder path is needed for documents within a dataset context
  return getDocumentStreamingUrl(documentIdentifier)
})

const isPDF = computed(() => {
  if (!props.document) return false
  const filename = props.document.fileName || props.document.name || props.document.gcsPath || ''
  return filename.toLowerCase().endsWith('.pdf') || props.document.type === 'application/pdf'
})

const isImage = computed(() => {
  if (!props.document) return false
  // Check multiple possible sources for the filename
  const filename = props.document.fileName || props.document.name || props.document.gcsPath || ''
  const ext = filename.split('.').pop()?.toLowerCase() || ''
  return ['png', 'jpg', 'jpeg', 'gif', 'webp', 'bmp', 'svg'].includes(ext)
})

// PDF Loading
const pdfSource = computed(() => {
  if (isPDF.value && documentUrl.value) {
    return documentUrl.value;
  }
  return null;
});

// Use usePDF hook with conditional source
const { pdf, pages, info } = usePDF(pdfSource);

// PDF Loading state management - only watch when we have valid refs
watch(() => pdf?.value, (p) => {
  if (p && isPDF.value) {
    loading.value = false;
    error.value = null; // Clear error on success
  }
}, { flush: 'sync' });

// Handle PDF loading errors
watch(() => pdfSource.value, (newSource, oldSource) => {
  if (newSource !== oldSource) {
    // Reset error state when source changes
    error.value = null;
    if (newSource && isPDF.value) {
      loading.value = true;
    }
  }
}, { immediate: true });

// Watch for PDF loading errors by monitoring when source exists but pdf doesn't load
let pdfLoadTimeout: ReturnType<typeof setTimeout> | null = null;

watch([() => pdfSource.value, () => pdf?.value], ([source, pdfInstance]) => {
  if (pdfLoadTimeout) {
    clearTimeout(pdfLoadTimeout);
    pdfLoadTimeout = null;
  }
  
  if (source && isPDF.value && !pdfInstance) {
    // Set a timeout to detect failed PDF loading
    pdfLoadTimeout = setTimeout(() => {
      if (!pdf?.value && pdfSource.value && isPDF.value) {
        console.warn('PDF loading timeout or cancelled:', pdfSource.value);
        loading.value = false;
        error.value = 'Failed to load PDF. The file may be corrupted or unavailable.';
      }
    }, 10000); // 10 second timeout
  } else if (pdfInstance) {
    // PDF loaded successfully
    if (pdfLoadTimeout) {
      clearTimeout(pdfLoadTimeout);
      pdfLoadTimeout = null;
    }
    loading.value = false;
    error.value = null;
  }
}, { immediate: true });

// Cleanup timeout on unmount
onUnmounted(() => {
  if (pdfLoadTimeout) {
    clearTimeout(pdfLoadTimeout);
  }
});

// Global error handler to catch PDF AbortExceptions and handle them gracefully
if (typeof window !== 'undefined') {
  const originalError = console.error;
  const handlePdfError = (...args: any[]) => {
    const error = args[0];
    // Check if this is a PDF TextLayer abort exception
    if (error && error.name === 'AbortException' && 
        (error.message?.includes('TextLayer task cancelled') || 
         error.message?.includes('cancelled'))) {
      // Silently handle PDF cancellation - this is expected behavior
      // when documents change quickly
      console.debug('PDF loading cancelled (expected when switching documents)');
      return;
    }
    // Call original error handler for other errors
    originalError(...args);
  };
  
  // Override console.error temporarily (will be restored on unmount)
  console.error = handlePdfError;
  
  onUnmounted(() => {
    // Restore original console.error
    console.error = originalError;
  });
}


// Methods
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`
}

const onImageLoad = () => {
  loading.value = false
  error.value = null
}

const onImageError = (event: Event) => {
  const target = event.target as HTMLImageElement
  const src = target?.src || documentUrl.value
  
  console.error('Image failed to load:', src)
  
  // Try to get more specific error information
  if (src?.includes('/stream')) {
    error.value = 'Document not found in storage. The file may have been moved or deleted.'
  } else {
    error.value = 'Failed to load image. The URL may be invalid or the file corrupted.'
  }
  
  loading.value = false
}

const previousPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--
  }
}

const nextPage = () => {
  const maxPages = pages.value || 1
  if (currentPage.value < maxPages) {
    currentPage.value++
  }
}

const retryLoad = () => {
  error.value = null
  // The usePDF hook should retry automatically when the URL is valid again.
  // For images, we can add a cache-buster.
  if (isImage.value) {
    const img = document.querySelector('.document-preview-image') as HTMLImageElement
    if (img) {
      img.src = `${documentUrl.value}?retry=${Date.now()}`
    }
  }
}

// Zoom functions
const zoomIn = () => {
  isManualZoom.value = true
  scale.value = Math.min(scale.value + 0.2, 3.0)
}

const zoomOut = () => {
  isManualZoom.value = true
  scale.value = Math.max(scale.value - 0.2, 0.4)
}

const fitToScreen = () => {
  isManualZoom.value = false // Reset manual zoom flag
  autoFitPdf() // Trigger auto-fit
}

// Auto-fit PDF to container
const autoFitPdf = async () => {
  // Don't auto-fit if user has manually zoomed
  if (!isPDF.value || !pdf.value || !pdfContainerRef.value || isManualZoom.value) {
    return
  }
  
  // Wait for the next tick to ensure DOM is updated
  await nextTick()
  
  try {
    // Get container dimensions (subtract padding and controls height)
    const container = pdfContainerRef.value
    const containerRect = container.getBoundingClientRect()
    const availableWidth = containerRect.width
    const availableHeight = containerRect.height
    
    // Use a heuristic approach since we can't directly get PDF dimensions
    // Standard PDF page is typically 595x842 points (A4 at 72 DPI)
    // We'll use these default dimensions for scaling calculation
    const defaultPdfWidth = 595
    const defaultPdfHeight = 842
    
    // Calculate scale to fit both width and height
    const scaleX = availableWidth / defaultPdfWidth
    const scaleY = availableHeight / defaultPdfHeight
    const optimalScale = Math.min(scaleX, scaleY, 2.0) // Don't scale beyond 200%
    
    // Set minimum scale to ensure readability
    const finalScale = Math.max(optimalScale, 0.3)
    
    scale.value = finalScale
  } catch (error) {
    console.warn('Failed to auto-fit PDF:', error)
    // Fallback to default scale
    scale.value = 1
  }
}

// Handle PDF loaded event
const onPdfLoaded = () => {
  emit('pdf-loaded', pages.value || 0)
  autoFitPdf()
}

// Reset state when document changes
watch(() => props.document, (newDoc, oldDoc) => {
  // Clear any pending PDF load timeout when document changes
  if (pdfLoadTimeout) {
    clearTimeout(pdfLoadTimeout);
    pdfLoadTimeout = null;
  }
  
  currentPage.value = 1
  scale.value = 1
  isManualZoom.value = false // Reset manual zoom flag for new documents
  error.value = null
  
  if (newDoc) {
    // Only set loading for PDFs, images handle their own loading state
    if (isPDF.value) {
      loading.value = true
    } else {
      loading.value = false
    }
  } else {
    loading.value = false
  }
}, { immediate: true })

// ResizeObserver to re-fit PDF when container size changes
let resizeObserver: ResizeObserver | null = null
let resizeTimeout: ReturnType<typeof setTimeout> | null = null

onMounted(() => {
  // Watch for container changes and set up resize observer
  watch(() => pdfContainerRef.value, (container) => {
    if (container && 'ResizeObserver' in window) {
      resizeObserver = new ResizeObserver(() => {
        if (isPDF.value && pdf.value) {
          // Debounce the auto-fit to avoid excessive calls during resize
          if (resizeTimeout) clearTimeout(resizeTimeout)
          resizeTimeout = setTimeout(() => {
            autoFitPdf()
          }, 150)
        }
      })
      resizeObserver.observe(container)
    }
  }, { immediate: true })
})

onUnmounted(() => {
  if (resizeObserver) {
    resizeObserver.disconnect()
  }
  if (resizeTimeout) {
    clearTimeout(resizeTimeout)
  }
})

</script>

 


================================================
FILE: src/components/DocumentsDataTable.vue
================================================
<template>
  <div class="space-y-4">
    <!-- Header with actions -->
    <div class="flex items-center justify-between">
      <div>
        <h2 class="text-xl font-semibold text-foreground">Documents</h2>
        <p class="text-sm text-muted-foreground mt-1">
          {{ documents.length }} document{{ documents.length !== 1 ? 's' : '' }} found
          <span v-if="extractedCount > 0" class="ml-2 text-green-600">
            ({{ extractedCount }} extracted)
          </span>
          <span v-if="approvalSummary.approved > 0" class="ml-2 text-green-600">
            ({{ approvalSummary.approved }} approved)
          </span>
          <span v-if="approvalSummary.pending > 0" class="ml-2 text-yellow-600">
            ({{ approvalSummary.pending }} pending)
          </span>
        </p>
      </div>
      <div class="flex items-center gap-3">
        <div v-if="isLoadingDocs" class="flex items-center gap-2 text-sm text-muted-foreground">
          <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
          Loading documents...
        </div>
        <div v-if="documents.length > 0 && config" class="flex items-center gap-2">
          <button
            @click="runBatchExtraction(false)"
            :disabled="isBatchExtracting || extractionTasksToStart === 0"
            class="px-3 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors flex items-center gap-2"
          >
            <Play class="h-4 w-4" />
            Run Missing ({{ extractionTasksToStart }})
          </button>
          <button
            @click="runBatchExtraction(true)"
            :disabled="isBatchExtracting"
            class="px-3 py-2 text-sm bg-gray-600 text-white rounded-md hover:bg-gray-700 disabled:opacity-50 transition-colors flex items-center gap-2"
          >
            <Play class="h-4 w-4" />
            Run All
          </button>
        </div>
      </div>
    </div>

    <!-- Empty state -->
    <div v-if="documents.length === 0 && !isLoadingDocs" class="text-center py-12 bg-muted/50 rounded-lg border-2 border-dashed border-border">
      <FileText class="h-12 w-12 text-muted-foreground mx-auto mb-4 opacity-50" />
      <p class="text-muted-foreground">No documents found</p>
      <p class="text-sm text-muted-foreground mt-1">Check your SQL query to ensure it returns document paths</p>
    </div>

    <!-- Data Table -->
    <div v-else class="border border-border rounded-lg">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Filename</TableHead>
            <TableHead>Type</TableHead>
            <TableHead>Size</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Accuracy</TableHead>
            <TableHead class="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow 
            v-for="doc in documents" 
            :key="doc.id"
            class="cursor-pointer"
            @click="viewDocument(doc.id, datasetId)"
          >
            <TableCell>
              <div class="flex items-center gap-3">
                <FileText class="h-4 w-4 text-muted-foreground flex-shrink-0" />
                <div class="min-w-0">
                  <div class="font-mono text-sm font-medium text-foreground truncate">
                    {{ getFilename(doc.gcsPath) }}
                  </div>
                  <div class="text-xs text-muted-foreground truncate">
                    {{ doc.gcsPath }}
                  </div>
                </div>
              </div>
            </TableCell>
            <TableCell>
              <div class="flex items-center gap-2">
                <span class="px-2 py-1 bg-muted text-muted-foreground rounded text-xs font-medium">
                  {{ getFileExtension(doc.gcsPath) }}
                </span>
              </div>
            </TableCell>
            <TableCell>
              <span class="text-sm text-muted-foreground">
                {{ formatFileSize(doc.fileSize || 0) }}
              </span>
            </TableCell>
            <TableCell>
              <div class="flex items-center gap-2">
                <!-- Approval Status -->
                <span 
                  v-if="doc.isApproved !== undefined"
                  :class="{
                    'bg-green-100 text-green-700 border-green-200': doc.isApproved === true,
                    'bg-yellow-100 text-yellow-700 border-yellow-200': doc.isApproved === false,
                    'bg-gray-100 text-gray-600 border-gray-200': doc.isApproved === null
                  }"
                  class="px-2 py-1 rounded-md text-xs font-medium border"
                >
                  {{ getApprovalStatusText(doc.id) }}
                </span>
                
                <!-- Needs Approval Badge -->
                <div 
                  v-if="(doc.isApproved === false || doc.isApproved === null)"
                  class="flex items-center gap-1 px-2 py-1 bg-yellow-100 text-yellow-800 border border-yellow-300 rounded-md text-xs font-medium"
                >
                  <Clock class="h-3 w-3" />
                  Needs Review
                </div>
              </div>
            </TableCell>
            <TableCell>
              <div class="flex items-center gap-2">
                <div v-for="accuracy in getDocumentAccuracies(doc.id)" :key="accuracy.extractor" class="flex items-center gap-1">
                  <span class="text-xs text-muted-foreground">{{ accuracy.extractor }}:</span>
                  <PercentBadge :percentage="accuracy.accuracy" />
                </div>
                <span v-if="getDocumentAccuracies(doc.id).length === 0" class="text-xs text-muted-foreground">
                  No extractions
                </span>
              </div>
            </TableCell>
            <TableCell class="text-right">
              <div class="flex items-center justify-end gap-2">
                <!-- Extraction Status Indicators -->
                <div class="flex items-center gap-1">
                  <div
                    v-for="extractor in effectiveExtractors"
                    :key="extractor"
                    class="flex items-center gap-1"
                  >
                    <div
                      :class="{
                        'bg-green-500': hasExtractorRun(doc.id, extractor),
                        'bg-gray-300': !hasExtractorRun(doc.id, extractor),
                        'animate-pulse bg-blue-500': extractingDocs.has(`${doc.id}-${extractor}`)
                      }"
                      class="w-2 h-2 rounded-full"
                      :title="`${extractor}: ${hasExtractorRun(doc.id, extractor) ? 'Completed' : 'Not run'}`"
                    ></div>
                  </div>
                </div>
              </div>
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useRouter } from 'vue-router';
import { FileText, Play, Clock, MapPin, Tag, Hash } from 'lucide-vue-next';
import { Table, TableHeader, TableBody, TableRow, TableHead, TableCell } from '@/components/ui/table';
import PercentBadge from './PercentBadge.vue';

interface Document {
  id: string;
  gcsPath: string;
  siteName?: string;
  objectType?: string;
  emissionsLogId?: string;
  isApproved?: boolean | null;
  fileSize?: number;
}

interface ExtractionConfig {
  id: string;
  data: {
    extractors?: Array<{ model: string }>;
  };
}

interface Props {
  documents: Document[];
  config: ExtractionConfig | null;
  availableExtractors: string[];
  extractingDocs: Set<string>;
  datasetId: string;
  isLoadingDocs: boolean;
  extractedCount: number;
  extractionTasksToStart: number;
  approvalSummary: {
    approved: number;
    pending: number;
    notReviewed: number;
  };
  runBatchExtraction: (includeCompleted: boolean) => void;
  getApprovalStatusText: (docId: string) => string;
  getDocumentAccuracies: (docId: string) => Array<{ extractor: string; accuracy: number }>;
  hasExtractorRun: (docId: string, extractor: string) => boolean;
}

const props = defineProps<Props>();
const router = useRouter();

// Computed property for effective extractors
const effectiveExtractors = computed(() => {
  const backendExtractors = props.availableExtractors || [];
  const configExtractors = props.config?.data?.extractors?.map(e => e.model) || [];
  
  // Use config extractors if available, otherwise fall back to backend extractors
  return configExtractors.length > 0 ? configExtractors : backendExtractors;
});

const isBatchExtracting = computed(() => {
  return Array.from(props.extractingDocs).some(key => key.includes('-batch-'));
});

// Helper functions
const getFilename = (path: string): string => {
  return path.split('/').pop() || path;
};

const getFileExtension = (path: string): string => {
  const filename = getFilename(path);
  const ext = filename.split('.').pop()?.toUpperCase();
  return ext || 'UNKNOWN';
};

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
};

const viewDocument = (docId: string, datasetId: string) => {
  router.push(`/datasets/${datasetId}/documents/${docId}`);
};
</script>



================================================
FILE: src/components/ErrorDisplay.vue
================================================
<template>
  <div class="flex items-center justify-center min-h-96">
    <div class="text-center max-w-md mx-auto">
      <!-- Network Error -->
      <div v-if="errorInfo && errorInfo.type === 'network'" class="p-6 bg-red-50 border border-red-200 rounded-lg">
        <div class="h-12 w-12 mx-auto text-red-400 mb-4">🔌</div>
        <h2 class="text-xl font-semibold text-red-600 mb-2">{{ networkTitle || 'Backend Server Unreachable' }}</h2>
        <p class="text-red-600 mb-4">{{ errorInfo.message }}</p>
        <div class="space-y-2">
          <button 
            v-if="showRetry"
            @click="$emit('retry')"
            class="flex items-center gap-2 mx-auto px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700 transition-colors"
          >
            {{ retryText || 'Try Again' }}
          </button>
          <p class="text-xs text-red-500">Make sure the backend server is running on localhost:8000</p>
        </div>
      </div>

      <!-- Not Found Error -->
      <div v-else-if="errorInfo && errorInfo.type === 'not_found'" class="p-6 bg-gray-50 border border-gray-200 rounded-lg">
        <AlertCircle class="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h2 class="text-xl font-semibold text-gray-600 mb-2">{{ notFoundTitle || 'Resource Not Found' }}</h2>
        <p class="text-gray-600 mb-4">{{ errorInfo.message }}</p>
        <button 
          v-if="showBack"
          @click="$emit('goBack')"
          class="flex items-center gap-2 mx-auto px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors"
        >
          {{ backText || 'Go Back' }}
        </button>
      </div>

      <!-- Server Error -->
      <div v-else-if="errorInfo && errorInfo.type === 'server'" class="p-6 bg-yellow-50 border border-yellow-200 rounded-lg">
        <div class="h-12 w-12 mx-auto text-yellow-400 mb-4">⚠️</div>
        <h2 class="text-xl font-semibold text-yellow-600 mb-2">{{ serverTitle || 'Server Error' }}</h2>
        <p class="text-yellow-600 mb-4">{{ errorInfo.message }}</p>
        <button 
          v-if="showRetry"
          @click="$emit('retry')"
          class="flex items-center gap-2 mx-auto px-4 py-2 text-sm font-medium text-white bg-yellow-600 rounded-md hover:bg-yellow-700 transition-colors"
        >
          {{ retryText || 'Try Again' }}
        </button>
      </div>

      <!-- Generic Error -->
      <div v-else class="p-6 bg-gray-50 border border-gray-200 rounded-lg">
        <AlertCircle class="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h2 class="text-xl font-semibold text-gray-600 mb-2">{{ genericTitle || 'Error' }}</h2>
        <p class="text-gray-600 mb-4">{{ errorInfo?.message || 'An unexpected error occurred.' }}</p>
        <div class="flex gap-2 justify-center">
          <button 
            v-if="showRetry"
            @click="$emit('retry')"
            class="flex items-center gap-2 px-4 py-2 text-sm font-medium text-white bg-gray-600 rounded-md hover:bg-gray-700 transition-colors"
          >
            {{ retryText || 'Try Again' }}
          </button>
          <button 
            v-if="showBack"
            @click="$emit('goBack')"
            class="flex items-center gap-2 px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors"
          >
            {{ backText || 'Go Back' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { AlertCircle } from 'lucide-vue-next';
import type { ErrorInfo } from '../utils/errorUtils';

interface Props {
  errorInfo: ErrorInfo | null;
  networkTitle?: string;
  notFoundTitle?: string;
  serverTitle?: string;
  genericTitle?: string;
  retryText?: string;
  backText?: string;
  showRetry?: boolean;
  showBack?: boolean;
}

withDefaults(defineProps<Props>(), {
  showRetry: true,
  showBack: true,
});

defineEmits<{
  retry: [];
  goBack: [];
}>();
</script> 


================================================
FILE: src/components/ExtractionAccuracyIndicator.vue
================================================
<template>
  <div class="flex flex-col items-center gap-1">
    <!-- Check if any method has ground truth -->
    <div v-if="hasAnyGroundTruth">
      <!-- Accuracy chips for each method -->
      <div v-for="(accuracy, method) in methodAccuracies" :key="method" class="flex items-center gap-2">
        <!-- Extractor Logo -->
        <ExtractorLogo :extractor="method" :size="16" />
        
        <!-- Accuracy Chip -->
        <div 
          v-if="accuracy.hasGroundTruth"
          :class="{
            'bg-green-100 text-green-700 border-green-200': accuracy.percentage >= 80,
            'bg-yellow-100 text-yellow-700 border-yellow-200': accuracy.percentage >= 60 && accuracy.percentage < 80,
            'bg-red-100 text-red-700 border-red-200': accuracy.percentage < 60,
          }"
          class="px-2 py-0.5 text-xs font-medium rounded border flex items-center gap-1"
        >
          <span>{{ accuracy.correct }}/{{ accuracy.total }}</span>
          <span class="text-xs opacity-75">({{ Math.round(accuracy.percentage) }}%)</span>
        </div>
      </div>
    </div>
    
    <!-- Single "No Ground Truth" indicator when no ground truth available -->
    <div 
      v-else-if="Object.keys(methodAccuracies).length > 0"
      class="px-2 py-0.5 text-xs font-medium rounded border bg-gray-100 text-gray-500 border-gray-200"
    >
      <span>No Ground Truth</span>
    </div>
    
    <!-- Show "No extractions" if no methods found -->
    <div 
      v-else
      class="px-2 py-0.5 text-xs font-medium rounded border bg-gray-100 text-gray-400 border-gray-200"
    >
      No extractions
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import ExtractorLogo from './ExtractorLogo.vue';

interface Props {
  extractions: {
    method: string;
    extracted_data: Record<string, any>;
  }[];
  groundTruth: Record<string, any>;
  configFields?: Record<string, { field_type: string }>;
}

const props = defineProps<Props>();

const methodAccuracies = computed(() => {
  const accuracies: Record<string, {
    correct: number;
    total: number;
    percentage: number;
    hasGroundTruth: boolean;
  }> = {};

  // Group extractions by method
  const extractionsByMethod: Record<string, any> = {};
  props.extractions.forEach(extraction => {
    extractionsByMethod[extraction.method] = extraction.extracted_data;
  });

  // Calculate accuracy for each method
  Object.entries(extractionsByMethod).forEach(([method, extractedData]) => {
    let correct = 0;
    let total = 0;
    let hasAnyGroundTruth = false;

    // Compare each field with ground truth
    Object.entries(props.groundTruth).forEach(([fieldName, groundTruthValue]) => {
      if (groundTruthValue !== null && groundTruthValue !== undefined && groundTruthValue !== '') {
        hasAnyGroundTruth = true;
        total++;

        const extractedValue = extractedData[fieldName];
        if (extractedValue !== null && extractedValue !== undefined) {
          // Get field type for comparison
          const fieldType = props.configFields?.[fieldName]?.field_type || 'text';
          
          // Calculate correctness based on field type
          const isCorrect = calculateFieldCorrectness(extractedValue, groundTruthValue, fieldType);
          if (isCorrect) {
            correct++;
          }
        }
      }
    });

    accuracies[method] = {
      correct,
      total,
      percentage: total > 0 ? (correct / total) * 100 : 0,
      hasGroundTruth: hasAnyGroundTruth
    };
  });

  return accuracies;
});

const hasAnyGroundTruth = computed(() => {
  return Object.values(methodAccuracies.value).some(accuracy => accuracy.hasGroundTruth);
});

const calculateFieldCorrectness = (extracted: any, groundTruth: any, fieldType: string): boolean => {
  if (extracted === null || extracted === undefined || extracted === 'N/A') {
    return false;
  }

  switch (fieldType) {
    case 'number':
      const extractedNum = parseFloat(String(extracted).replace(/[^\d.-]/g, ''));
      const groundTruthNum = parseFloat(String(groundTruth).replace(/[^\d.-]/g, ''));
      if (isNaN(extractedNum) || isNaN(groundTruthNum)) return false;
      // Allow 5% tolerance for numbers
      const tolerance = Math.abs(groundTruthNum) * 0.05;
      return Math.abs(extractedNum - groundTruthNum) <= tolerance;
      
    case 'date':
      try {
        const extractedDate = new Date(extracted).toISOString().split('T')[0];
        const groundTruthDate = new Date(groundTruth).toISOString().split('T')[0];
        return extractedDate === groundTruthDate;
      } catch {
        return false;
      }
      
    case 'boolean':
      return Boolean(extracted) === Boolean(groundTruth);
      
    default: // text and others
      const extractedStr = String(extracted).toLowerCase().trim();
      const groundTruthStr = String(groundTruth).toLowerCase().trim();
      
      // Exact match
      if (extractedStr === groundTruthStr) return true;
      
      // Allow for minor differences (fuzzy matching)
      if (extractedStr.length > 3 && groundTruthStr.length > 3) {
        const similarity = calculateStringSimilarity(extractedStr, groundTruthStr);
        return similarity >= 0.8; // 80% similarity threshold
      }
      
      return false;
  }
};

const calculateStringSimilarity = (str1: string, str2: string): number => {
  const longer = str1.length > str2.length ? str1 : str2;
  const shorter = str1.length > str2.length ? str2 : str1;
  
  if (longer.length === 0) return 1.0;
  
  const editDistance = levenshteinDistance(longer, shorter);
  return (longer.length - editDistance) / longer.length;
};

const levenshteinDistance = (str1: string, str2: string): number => {
  const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));
  
  for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
  for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;
  
  for (let j = 1; j <= str2.length; j++) {
    for (let i = 1; i <= str1.length; i++) {
      const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
      matrix[j][i] = Math.min(
        matrix[j][i - 1] + 1,     // deletion
        matrix[j - 1][i] + 1,     // insertion
        matrix[j - 1][i - 1] + indicator  // substitution
      );
    }
  }
  
  return matrix[str2.length][str1.length];
};
</script> 


================================================
FILE: src/components/ExtractionCardSkeleton.vue
================================================
<template>
  <div class="p-3 rounded-lg border border-border bg-card">
    <div class="flex items-start justify-between">
      <div class="flex-1">
        <div v-if="!hideHeader" class="flex items-center space-x-2 mb-2">
          <Skeleton class="h-4 w-20" />
          <Skeleton class="w-4 h-4 rounded" />
        </div>
        
        <div class="space-y-2">
          <Skeleton class="h-5 w-3/4" />
          <Skeleton class="h-3 w-1/2" />
        </div>
      </div>
      
      <div class="flex flex-col items-end space-y-1">
        <Skeleton class="h-5 w-12 rounded" />
        <Skeleton class="h-3 w-16" />
      </div>
    </div>
    
    <div v-if="showMetadata" class="mt-3 pt-3 border-t border-border">
      <div class="grid grid-cols-2 gap-2">
        <Skeleton class="h-3 w-16" />
        <Skeleton class="h-3 w-20" />
        <Skeleton class="h-3 w-24 col-span-2" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Skeleton } from '@/components/ui/skeleton'

interface Props {
  showMetadata?: boolean
  hideHeader?: boolean
}

withDefaults(defineProps<Props>(), {
  showMetadata: false,
  hideHeader: false,
})
</script>
</template>


================================================
FILE: src/components/ExtractionField.vue
================================================
<template>
  <Card 
    ref="fieldElement"
    :class="[
      'transition-colors duration-200 relative cursor-pointer',
      {
        'border-red-200 bg-red-50': value.error,
        'hover:bg-blue-50 hover:border-blue-200': !value.noExtractionYet,
        'border-dashed border-gray-300 bg-gray-50/50': value.noExtractionYet
      }
    ]"
    @mouseenter="onHover"
    @mouseleave="onUnhover"
    @click="onClick"
  >
    <!-- Hover Check Mark with Tooltip (only show if extraction has been run) -->
    <div 
      v-show="isHovered && !value.noExtractionYet"
      class="absolute top-2 right-2 bg-blue-600 text-white rounded-full p-1 shadow-lg z-10 group"
    >
      <Check class="h-3 w-3" />
      <div class="absolute bottom-full right-0 mb-2 px-2 py-1 bg-gray-900 text-white text-xs rounded whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none">
        Click to use as ground truth
        <div class="absolute top-full right-2 w-0 h-0 border-l-2 border-r-2 border-t-2 border-transparent border-t-gray-900"></div>
      </div>
    </div>

    <div class="flex items-start justify-between">
      <div class="flex-1">
        <div v-if="!hideHeader" class="flex items-center space-x-2">
          <h3 class="font-medium text-foreground">
            {{ value.name }}
          </h3>
          <FieldTypeIcon :type="value.type" />
        </div>
        
        <div :class="{ 'mt-2': !hideHeader }">
          <!-- Show different content based on extraction state -->
          <div v-if="value.isExtracting" class="space-y-2">
            <Skeleton class="h-4 w-3/4" />
            <Skeleton class="h-3 w-1/2" />
          </div>
          
          <div v-else-if="value.noExtractionYet" class="flex flex-col items-center justify-center py-4 text-center">
            <div class="text-gray-400 mb-1">
              <svg class="h-8 w-8 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 5l7 7-7 7" />
              </svg>
            </div>
            <div class="text-sm text-gray-500 font-medium">Click "Run" above</div>
            <div class="text-xs text-gray-400">to extract this field</div>
          </div>
          
          <div v-else class="flex items-baseline space-x-2">
            <span 
              class="text-lg font-semibold"
              :class="value.error ? 'text-red-600' : 'text-foreground'"
            >
              {{ formatValue(value.value) }}
            </span>
            <span v-if="value.unit" class="text-sm text-muted-foreground">
              {{ value.unit }}
            </span>
          </div>
        </div>
      </div>
      
      <div v-if="value.isExtracting" class="flex flex-col items-end space-y-1">
        <Skeleton class="h-5 w-12 rounded" />
        <Skeleton class="h-3 w-16" />
      </div>
      
      <div v-else-if="!value.noExtractionYet" class="flex flex-col items-end space-y-1">
        <!-- Show correctness badge -->
        <PercentBadge 
          v-if="value.correctnessScore !== null && value.correctnessScore !== undefined"
          :percentage="value.correctnessScore"
        />
      </div>
    </div>
    
    <!-- Additional metadata -->
    <div v-if="showMetadata" class="mt-3 pt-3 border-t border-border">
      <div class="grid grid-cols-2 gap-2 text-xs text-muted-foreground">
        <div>Type: {{ value.type }}</div>
        <div v-if="value.correctnessScore !== undefined && value.correctnessScore !== null">
          Correctness: {{ value.correctnessScore }}%
        </div>
      </div>
    </div>
  </Card>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Check } from 'lucide-vue-next'
import PercentBadge from './PercentBadge.vue'
import FieldTypeIcon from './FieldTypeIcon.vue'
import { Skeleton } from '@/components/ui/skeleton'
import { Card } from '@/components/ui/card'
import { ExtractionField } from '@/types'


interface Props {
  extractedField: ExtractionField
}

interface Emits {
  (e: 'hover', fieldName: string, element: HTMLElement): void
  (e: 'unhover'): void
  (e: 'click', fieldName: string): void
}

const props = defineProps<Props>()

const emit = defineEmits<Emits>()

const isHovered = ref(false)

const formatValue = (value: any): string => {
  // Handle null/undefined values
  if (value === null || value === undefined) return 'null'
  
  // Handle string values that represent null
  if (typeof value === 'string') {
    const trimmed = value.trim().toLowerCase()
    if (trimmed === 'n/a' || trimmed === 'null' || trimmed === 'none' || trimmed === '' || trimmed === 'not available') {
      return 'null'
    }
    // Handle date strings
    if (value.match(/^\d{4}-\d{2}-\d{2}/)) {
      return new Date(value).toLocaleDateString()
    }
  }
  
  // Handle numeric values
  if (typeof value === 'number') {
    return value.toLocaleString()
  }
  
  return String(value)
}

const onHover = () => {
  isHovered.value = true
  if (fieldElement.value) {
    emit('hover', props.value, fieldElement.value)
  }
}

const onUnhover = () => {
  isHovered.value = false
  emit('unhover')
}

const onClick = () => {
  emit('click', props.value)
}
</script>



================================================
FILE: src/components/ExtractionSummaryTab.vue
================================================
<template>
  <div class="bg-card border border-border rounded-lg shadow-sm">
    <div class="p-6 border-b border-border">
      <h2 class="text-xl font-semibold text-foreground">Extraction Summary</h2>
      <p class="text-sm text-muted-foreground mt-1">
        Overall extraction performance across {{ documents.length }} document{{ documents.length !== 1 ? 's' : '' }}
      </p>
    </div>

    <div v-if="isLoading" class="p-6 space-y-8 min-h-64">
      <!-- Skeleton for Overall Coverage Stats -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div v-for="i in 2" :key="i" class="bg-muted/30 border border-border rounded-lg p-4">
          <div class="flex items-center gap-2 mb-3">
            <Skeleton class="w-5 h-5 rounded" />
            <Skeleton class="h-4 w-16" />
            <Skeleton class="flex-1 h-2 rounded-full" />
          </div>
          
          <div class="space-y-2">
            <div class="flex items-center justify-between">
              <Skeleton class="h-3 w-24" />
              <Skeleton class="h-3 w-16" />
            </div>
            <div class="flex items-center justify-between">
              <Skeleton class="h-3 w-28" />
              <Skeleton class="h-3 w-8" />
            </div>
            <div class="flex items-center justify-between">
              <Skeleton class="h-3 w-20" />
              <Skeleton class="h-3 w-8" />
            </div>
          </div>
        </div>
      </div>
      
      <!-- Skeleton for Field-by-Field Analysis -->
      <div class="space-y-4">
        <Skeleton class="h-5 w-48" />
        <div class="space-y-3">
          <div v-for="i in 4" :key="i" class="bg-muted/30 border border-border rounded-lg p-4">
            <div class="flex items-center justify-between mb-2">
              <div class="flex items-center space-x-2">
                <Skeleton class="w-4 h-4 rounded" />
                <Skeleton class="h-4 w-20" />
              </div>
              <Skeleton class="h-5 w-12 rounded" />
            </div>
            <div class="grid grid-cols-2 gap-4">
              <div v-for="j in 2" :key="j" class="space-y-1">
                <Skeleton class="h-3 w-16" />
                <Skeleton class="h-3 w-12" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div v-else class="p-6 space-y-8">
      <!-- Overall Coverage Stats -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div v-for="extractor in effectiveExtractors" :key="extractor" class="bg-muted/30 border border-border rounded-lg p-4">
          <div class="flex items-center gap-2 mb-3">
            <ExtractorLogo :extractor="extractor" :size="20" />
            <h3 class="font-semibold text-foreground capitalize">{{ extractor }}</h3>

            <div class="w-full bg-gray-200 rounded-full h-2">
              <div 
                class="bg-primary h-2 rounded-full transition-all duration-300"
                :style="{ width: `${getExtractorStats(extractor).coveragePercentage}%` }"
              ></div>
            </div>
          </div>
          
          <div class="space-y-2">
            <div class="flex items-center justify-between">
              <span class="text-sm text-muted-foreground">Documents Processed</span>
              <span class="font-medium">
                {{ getExtractorStats(extractor).processed }}/{{ documents.length }}
                <span class="text-xs text-muted-foreground ml-1">
                  ({{ getExtractorStats(extractor).coveragePercentage }}%)
                </span>
              </span>
            </div>
            
            <div class="flex items-center justify-between">
              <span class="text-sm text-muted-foreground">Successful Extractions</span>
              <span class="font-medium text-green-600">
                {{ getExtractorStats(extractor).successful }}
              </span>
            </div>
            
            <div class="flex items-center justify-between">
              <span class="text-sm text-muted-foreground">Failed Extractions</span>
              <span class="font-medium text-red-600">
                {{ getExtractorStats(extractor).failed }}
              </span>
            </div>
            
            <!-- Overall field extraction summary -->
            <div class="mt-3 pt-3 border-t border-border">
              <div class="text-xs text-muted-foreground mb-2">Field Extraction Summary:</div>
              <div class="space-y-1 text-xs">
                <div class="flex items-center justify-between">
                  <span class="flex items-center gap-1">
                    <div class="w-2 h-2 rounded-full bg-green-500"></div>
                    <span>Correct</span>
                  </span>
                  <span class="font-medium">{{ getExtractorFieldSummary(extractor).correct }}</span>
                </div>
                <div class="flex items-center justify-between">
                  <span class="flex items-center gap-1">
                    <div class="w-2 h-2 rounded-full bg-red-500"></div>
                    <span>Wrong</span>
                  </span>
                  <span class="font-medium">{{ getExtractorFieldSummary(extractor).wrong }}</span>
                </div>
                <div class="flex items-center justify-between">
                  <span class="flex items-center gap-1">
                    <div class="w-2 h-2 rounded-full bg-blue-500"></div>
                    <span>Overall Accuracy</span>
                  </span>
                  <span class="font-medium">{{ getExtractorFieldSummary(extractor).accuracy }}%</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Field-by-Field Analysis -->
      <div v-if="fieldAnalytics.length > 0" class="space-y-4">
        <div class="overflow-x-auto">
          <table class="w-full border border-border rounded-lg">
            <thead class="bg-muted/50">
              <tr>
                <th class="text-left p-3 border-b border-border font-medium text-foreground">Field Name</th>
                <th class="text-left p-3 border-b border-border font-medium text-foreground">Type</th>
                <th v-for="extractor in effectiveExtractors" :key="extractor" class="text-center p-3 border-b border-border font-medium text-foreground min-w-[150px]">
                  <div class="flex flex-col items-center gap-1">
                    <div class="flex items-center gap-2">
                      <ExtractorLogo :extractor="extractor" :size="16" />
                      <span class="capitalize">{{ extractor }}</span>
                    </div>
                    <span class="text-xs text-muted-foreground font-normal">Accuracy</span>
                  </div>
                </th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="field in fieldAnalytics" :key="field.fieldName" class="hover:bg-muted/20">
                <td class="p-3 border-b border-border">
                  <div class="flex items-center gap-2">
                    <FieldTypeIcon :type="field.fieldType" />
                    <span class="font-medium">{{ field.fieldName }}</span>
                  </div>
                </td>
                <td class="p-3 border-b border-border text-sm text-muted-foreground capitalize">
                  {{ field.fieldType }}
                </td>
                <td v-for="extractor in effectiveExtractors" :key="extractor" class="p-4 border-b border-border">
                  <div class="flex items-center justify-center">
                    <PercentBadge :percentage="field.extractors[extractor]?.accuracyPercentage || 0" />
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- Document Type Breakdown -->
      <div class="bg-muted/30 border border-border rounded-lg p-4">
        <div class="flex items-center gap-2 mb-3">
          <FileText class="h-5 w-5 text-muted-foreground" />
          <h3 class="font-semibold text-foreground">Document Type Breakdown</h3>
        </div>
        
        <div v-if="documentTypeStats.length === 0" class="text-center py-8 text-muted-foreground">
          <p>No document type information available</p>
        </div>
        
        <div v-else class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- ECharts Pie Chart -->
          <div class="flex justify-center">
            <v-chart
              v-if="documentTypeStats.length > 0 && !isLoading"
              :option="pieChartOption"
              :style="{ width: '350px', height: '350px' }"
              autoresize
            />
          </div>
          
          <!-- Legend -->
          <div class="space-y-3">
            <div v-for="typeStats in documentTypeStats" :key="typeStats.type" class="flex items-center justify-between">
              <div class="flex items-center gap-2">
                <div class="w-3 h-3 rounded-full" :style="{ backgroundColor: typeStats.color }"></div>
                <span class="font-medium text-foreground">{{ typeStats.type || 'Unknown' }}</span>
              </div>
              <div class="flex items-center gap-2">
                <span class="text-muted-foreground">{{ typeStats.count }}</span>
                <span class="text-sm text-muted-foreground">({{ typeStats.percentage }}%)</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Site Distribution -->
      <div v-if="siteDistribution.length > 0" class="bg-muted/30 border border-border rounded-lg p-4">
        <div class="flex items-center gap-2 mb-3">
          <MapPin class="h-5 w-5 text-muted-foreground" />
          <h3 class="font-semibold text-foreground">Site Distribution</h3>
        </div>
        
        <div class="space-y-3">
          <div v-for="siteStats in siteDistribution" :key="siteStats.site" class="flex items-center justify-between">
            <div class="flex items-center gap-2">
              <div class="w-3 h-3 rounded-full" :style="{ backgroundColor: siteStats.color }"></div>
              <span class="font-medium text-foreground">{{ siteStats.site || 'Unknown Site' }}</span>
            </div>
            <div class="flex items-center gap-2">
              <span class="text-muted-foreground">{{ siteStats.count }}</span>
              <span class="text-sm text-muted-foreground">({{ siteStats.percentage }}%)</span>
            </div>
          </div>
        </div>
      </div>

      <!-- File Type Extraction Success Heat Map -->
      <div class="bg-muted/30 border border-border rounded-lg p-4">
        <div class="flex items-center gap-2 mb-3">
          <BarChart3 class="h-5 w-5 text-muted-foreground" />
          <h3 class="font-semibold text-foreground">Extraction Success by File Type</h3>
        </div>
        
        <div v-if="fileTypeHeatMapData.fileTypes.length === 0 || fileTypeHeatMapData.fields.length === 0 || effectiveExtractors.length === 0" class="text-center py-8 text-muted-foreground">
          <p>No extraction data available for heat map analysis</p>
        </div>
        
        <div v-else class="space-y-6">
          <!-- Legend -->
          <div class="bg-muted/50 border border-border rounded-lg p-4">
            <h4 class="font-medium text-foreground mb-3">Success Rate Legend:</h4>
            <div class="flex flex-wrap gap-4 text-sm">
              <div class="flex items-center gap-2">
                <div class="w-4 h-4 rounded" style="background-color: #d73027"></div>
                <span class="text-muted-foreground">0-20% (Poor)</span>
              </div>
              <div class="flex items-center gap-2">
                <div class="w-4 h-4 rounded" style="background-color: #fc8d59"></div>
                <span class="text-muted-foreground">21-40% (Below Average)</span>
              </div>
              <div class="flex items-center gap-2">
                <div class="w-4 h-4 rounded" style="background-color: #fee08b"></div>
                <span class="text-muted-foreground">41-60% (Average)</span>
              </div>
              <div class="flex items-center gap-2">
                <div class="w-4 h-4 rounded" style="background-color: #91d5ff"></div>
                <span class="text-muted-foreground">61-80% (Good)</span>
              </div>
              <div class="flex items-center gap-2">
                <div class="w-4 h-4 rounded" style="background-color: #4575b4"></div>
                <span class="text-muted-foreground">81-100% (Excellent)</span>
              </div>
            </div>
          </div>

          <!-- Heat Maps per Extractor - Side by Side -->
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div v-for="extractor in effectiveExtractors" :key="extractor" class="space-y-3">
              <div class="flex items-center gap-2">
                <ExtractorLogo :extractor="extractor" :size="20" />
                <h4 class="text-lg font-semibold text-foreground capitalize">{{ extractor }} Extraction Success</h4>
              </div>
              
              <div class="border border-border rounded-lg p-4 bg-background">
                <v-chart
                  :option="getHeatMapOptionForExtractor(extractor)"
                  :style="{ width: '100%', height: '500px', minWidth: '600px' }"
                  autoresize
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Ground Truth Coverage -->
      <div class="bg-muted/30 border border-border rounded-lg p-4">
        <div class="flex items-center gap-2 mb-3">
          <Database class="h-5 w-5 text-muted-foreground" />
          <h3 class="font-semibold text-foreground">Ground Truth Coverage</h3>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
          <div class="text-center">
            <div class="text-2xl font-bold text-blue-600">{{ groundTruthStats.total }}</div>
            <div class="text-muted-foreground">Total Documents</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-green-600">{{ groundTruthStats.withGroundTruth }}</div>
            <div class="text-muted-foreground">With Ground Truth</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-orange-600">{{ groundTruthStats.annotated }}</div>
            <div class="text-muted-foreground">User Annotated</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-purple-600">{{ groundTruthStats.approved }}</div>
            <div class="text-muted-foreground">Approved</div>
          </div>
        </div>
        
        <div class="mt-4">
          <div class="flex items-center justify-between text-sm mb-2">
            <span class="text-muted-foreground">Ground Truth Coverage</span>
            <span class="font-medium">
              {{ groundTruthStats.coveragePercentage }}%
            </span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2">
            <div 
              class="bg-green-500 h-2 rounded-full transition-all duration-300"
              :style="{ width: `${groundTruthStats.coveragePercentage}%` }"
            ></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted, watch } from 'vue';
import { documentService, extractionService } from '../services';
import type { Document, ExtractionConfig } from '../types';
import ExtractorLogo from './ExtractorLogo.vue';
import FieldTypeIcon from './FieldTypeIcon.vue';
import PercentBadge from './PercentBadge.vue';
import { Skeleton } from '@/components/ui/skeleton';
import { BarChart3, Database, FileText, MapPin } from 'lucide-vue-next';
import { calculateCorrectness } from '../utils/correctness';
import VChart from 'vue-echarts';
import { use } from 'echarts/core';
import { PieChart, SankeyChart, HeatmapChart } from 'echarts/charts';
import { TitleComponent, TooltipComponent, LegendComponent, GridComponent, VisualMapComponent } from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';

use([PieChart, SankeyChart, HeatmapChart, TitleComponent, TooltipComponent, LegendComponent, GridComponent, VisualMapComponent, CanvasRenderer]);

interface Props {
  documents: Document[];
  config: ExtractionConfig | null;
  extractionData: Map<string, any>;
  groundTruthData: Map<string, any>;
  approvalData: Map<string, any>;
  availableExtractors: string[];
}

interface ExtractorStats {
  processed: number;
  successful: number;
  failed: number;
  coveragePercentage: number;
}

interface FieldAnalytics {
  fieldName: string;
  fieldType: string;
  groundTruth: {
    total: number;
    database: number;
    annotated: number;
  };
  extractors: Record<string, {
    // Pipeline stages
    totalProcessed: number;
    withGroundTruth: number;
    correct: number;
    wrong: number;
    // Accuracy (correct / withGroundTruth)
    accuracyPercentage: number;
    // Breakdown for debugging
    correctlyExtracted: number;  // Found value when GT has value
    correctlyNull: number;       // Returned null when GT is null/empty
    missedField: number;         // Returned null when GT has value
    incorrectValue: number;      // Returned wrong value when GT has value
    falsePositive: number;       // Returned value when GT is null/empty
  }>;
}

const props = defineProps<Props>();

// Computed property that combines backend extractors with config extractors
const effectiveExtractors = computed(() => {
  const backendExtractors = props.availableExtractors || [];
  const configExtractors = props.config?.data?.extractors?.map(e => e.model) || [];
  
  // Removed verbose debug logging to improve performance
  // Debug logging was causing console spam and performance overhead
  
  // Use config extractors if available, otherwise fall back to backend extractors
  if (configExtractors.length > 0) {
    return configExtractors;
  }
  
  return backendExtractors;
});

// Remove the local reactive state and use props instead
// const documentExtractions = ref<Map<string, any>>(new Map());
// const documentGroundTruth = ref<Map<string, any>>(new Map());
// const documentApprovals = ref<Map<string, any>>(new Map());

// Use props directly
const documentExtractions = computed(() => props.extractionData);
const documentGroundTruth = computed(() => props.groundTruthData);
const documentApprovals = computed(() => props.approvalData);

const isLoading = ref(false);

const getExtractorStats = (extractor: string): ExtractorStats => {
  let processed = 0;
  let successful = 0;
  let failed = 0;

    // Use the actual document IDs that have extractions instead of props.documents
  documentExtractions.value.forEach((docExtractions, docId) => {
    if (docExtractions?.extraction_results) {
      const extractorResult = docExtractions.extraction_results.find((r: any) => r.model === extractor);
      if (extractorResult) {
        processed++;
        // Check for both failed flag and extraction data errors (e.g., Mistral API errors)
        // The error might be stored as a field called "error" in the extracted_data
        const hasError = extractorResult?.failed || 
                        extractorResult?.extracted_data?.error ||
                        extractorResult?.raw_response?.error;
        if (hasError) {
          failed++;
        } else {
          successful++;
        }
      }
    }
  });

    const coveragePercentage = props.documents.length > 0 ? Math.round((processed / props.documents.length) * 100) : 0;

  return {
    processed,
    successful,
    failed,
    coveragePercentage
  };
};

const getExtractorFieldSummary = (extractor: string) => {
  let correct = 0;
  let wrong = 0;
  let totalWithGT = 0;

  // Sum up across all fields for this extractor
  fieldAnalytics.value.forEach(field => {
    const extractorData = field.extractors[extractor];
    if (extractorData) {
      correct += extractorData.correct || 0;
      wrong += extractorData.wrong || 0;
      totalWithGT += extractorData.withGroundTruth || 0;
    }
  });

  return {
    correct,
    wrong,
    totalWithGT,
    accuracy: totalWithGT > 0 ? Math.round((correct / totalWithGT) * 100) : 0
  };
};

const fieldAnalytics = computed((): FieldAnalytics[] => {
  if (!props.config?.data?.fields || props.documents.length === 0) {
    return [];
  }

  const analytics: FieldAnalytics[] = [];

  props.config.data.fields.forEach(field => {
    const fieldAnalytic: FieldAnalytics = {
      fieldName: field.name,
      fieldType: field.field_type,
      groundTruth: {
        total: 0,
        database: 0,
        annotated: 0
      },
      extractors: {}
    };

    // Calculate ground truth coverage for this field
    props.documents.forEach(doc => {
      const docGroundTruth = documentGroundTruth.value.get(doc.id);
      if (docGroundTruth && docGroundTruth[field.name]) {
        const groundTruthObj = docGroundTruth[field.name];
        const hasUserAnnotation = groundTruthObj.user_annotation !== null && groundTruthObj.user_annotation !== undefined && groundTruthObj.user_annotation !== '';
        const hasExtractedValue = groundTruthObj.extracted_value !== null && groundTruthObj.extracted_value !== undefined && groundTruthObj.extracted_value !== '';
        
        if (hasUserAnnotation || hasExtractedValue) {
          fieldAnalytic.groundTruth.total++;
          if (hasUserAnnotation) {
            fieldAnalytic.groundTruth.annotated++;
          } else if (hasExtractedValue) {
            fieldAnalytic.groundTruth.database++;
          }
        }
      }
    });

    effectiveExtractors.value.forEach(extractor => {
      let totalProcessed = 0;
      let withGroundTruth = 0;
      let correct = 0;
      let wrong = 0;
      let correctlyExtracted = 0;
      let correctlyNull = 0;
      let missedField = 0;
      let incorrectValue = 0;
      let falsePositive = 0;

      // Use the actual document IDs that have extractions instead of props.documents
      documentExtractions.value.forEach((docExtractions, docId) => {
        const docGroundTruth = documentGroundTruth.value.get(docId);
        
        if (docExtractions?.extraction_results) {
          const extractorResult = docExtractions.extraction_results.find((r: any) => r.model === extractor);
          
          // Check for both failed flag and extraction data errors
          // The error might be stored as a field called "error" in the extracted_data
          const hasError = extractorResult?.failed || 
                          extractorResult?.extracted_data?.error ||
                          extractorResult?.raw_response?.error;
          
          if (extractorResult && !hasError) {
            totalProcessed++;
            
            const extractedValue = extractorResult.extracted_data?.[field.name];
            const groundTruthObj = docGroundTruth?.[field.name];
            const groundTruthValue = groundTruthObj?.user_annotation ?? groundTruthObj?.extracted_value;

            // Check if we have ground truth (including null as valid GT)
            if (groundTruthObj !== undefined) {
              withGroundTruth++;
              
              const hasExtractedValue = extractedValue !== undefined && extractedValue !== null && extractedValue !== '';
              const hasGroundTruthValue = groundTruthValue !== undefined && groundTruthValue !== null && groundTruthValue !== '';

              if (hasGroundTruthValue && hasExtractedValue) {
                // Both have values - check if they match
                const correctnessScore = calculateCorrectness(extractedValue, groundTruthValue, field.field_type);
                if (correctnessScore >= 95) {
                  correct++;
                  correctlyExtracted++;
                } else {
                  wrong++;
                  incorrectValue++;
                }
              } else if (!hasGroundTruthValue && !hasExtractedValue) {
                // Both are null/empty - this is correct
                correct++;
                correctlyNull++;
              } else if (hasGroundTruthValue && !hasExtractedValue) {
                // GT has value but extraction is null - missed field
                wrong++;
                missedField++;
              } else if (!hasGroundTruthValue && hasExtractedValue) {
                // GT is null but extraction has value - false positive
                wrong++;
                falsePositive++;
              }
            }
          }
        }
      });

      fieldAnalytic.extractors[extractor] = {
        totalProcessed,
        withGroundTruth,
        correct,
        wrong,
        accuracyPercentage: withGroundTruth > 0 ? Math.round((correct / withGroundTruth) * 100) : 0,
        correctlyExtracted,
        correctlyNull,
        missedField,
        incorrectValue,
        falsePositive
      };
    });

    analytics.push(fieldAnalytic);
  });

  return analytics.sort((a, b) => a.fieldName.localeCompare(b.fieldName));
});

const documentTypeStats = computed(() => {
  if (props.documents.length === 0) return [];
  
  const typeCounts = new Map<string, number>();
  
  props.documents.forEach(doc => {
    // Extract file extension from gcs_path
    const path = doc.gcs_path || '';
    const lastDotIndex = path.lastIndexOf('.');
    const extension = lastDotIndex !== -1 ? path.substring(lastDotIndex + 1).toLowerCase() : 'unknown';
    const type = extension === 'unknown' ? 'No Extension' : `.${extension}`;
    
    typeCounts.set(type, (typeCounts.get(type) || 0) + 1);
  });
  
  const colors = [
    '#3B82F6', '#EF4444', '#10B981', '#F59E0B', '#8B5CF6',
    '#EC4899', '#6B7280', '#14B8A6', '#F97316', '#84CC16'
  ];
  
  const stats = Array.from(typeCounts.entries())
    .map(([type, count], index) => ({
      type,
      count,
      percentage: Math.round((count / props.documents.length) * 100),
      color: colors[index % colors.length]
    }))
    .sort((a, b) => b.count - a.count);
  
  return stats;
});

const pieChartOption = computed(() => ({
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c} ({d}%)'
  },
  grid: {
    top: 20,
    right: 20,
    bottom: 20,
    left: 20,
    containLabel: true
  },
  series: [
    {
      name: 'Document Types',
      type: 'pie',
      radius: ['35%', '60%'],
      center: ['50%', '50%'],
      avoidLabelOverlap: false,
      label: {
        show: true,
        position: 'outside',
        formatter: '{b}\n{d}%',
        fontSize: 11,
        fontWeight: 'bold',
        distanceToLabelLine: 5
      },
      emphasis: {
        label: {
          show: true,
          fontSize: 13,
          fontWeight: 'bold'
        },
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },
      labelLine: {
        show: true,
        length: 10,
        length2: 8,
        smooth: true
      },
      data: documentTypeStats.value.map(stat => ({
        value: stat.count,
        name: stat.type,
        itemStyle: {
          color: stat.color
        }
      }))
    }
  ]
}));

const siteDistribution = computed(() => {
  if (props.documents.length === 0) return [];
  
  const siteCounts = new Map<string, number>();
  
  props.documents.forEach(doc => {
    const site = doc.site_name || 'Unknown Site';
    siteCounts.set(site, (siteCounts.get(site) || 0) + 1);
  });
  
  // Only show site distribution if there are multiple sites
  if (siteCounts.size <= 1) return [];
  
  const colors = [
    '#3B82F6', '#EF4444', '#10B981', '#F59E0B', '#8B5CF6',
    '#EC4899', '#6B7280', '#14B8A6', '#F97316', '#84CC16'
  ];
  
  const stats = Array.from(siteCounts.entries())
    .map(([site, count], index) => ({
      site,
      count,
      percentage: Math.round((count / props.documents.length) * 100),
      color: colors[index % colors.length]
    }))
    .sort((a, b) => b.count - a.count);
  
  return stats;
});

const groundTruthStats = computed(() => {
  const total = props.documents.length;
  let withGroundTruth = 0;
  let annotated = 0;
  let approved = 0;

  props.documents.forEach(doc => {
    const docGroundTruth = documentGroundTruth.value.get(doc.id);
    const docApproval = documentApprovals.value.get(doc.id);
    
    if (docGroundTruth && Object.keys(docGroundTruth).length > 0) {
      const fields = Object.values(docGroundTruth);
      const hasGroundTruth = fields.some((field: any) => 
        field.user_annotation !== null && field.user_annotation !== undefined ||
        field.extracted_value !== null && field.extracted_value !== undefined
      );
      
      if (hasGroundTruth) {
        withGroundTruth++;
        
        const hasAnnotation = fields.some((field: any) => 
          field.user_annotation !== null && field.user_annotation !== undefined
        );
        if (hasAnnotation) {
          annotated++;
        }
      }
    }
    
    // Check document-level approval status
    if (docApproval?.is_approved === true) {
      approved++;
    }
  });

  const coveragePercentage = total > 0 ? Math.round((withGroundTruth / total) * 100) : 0;

  return {
    total,
    withGroundTruth,
    annotated,
    approved,
    coveragePercentage
  };
});

const fileTypeHeatMapData = computed(() => {
  if (!props.config?.data?.fields || props.documents.length === 0 || effectiveExtractors.value.length === 0) {
    return {
      data: [],
      fileTypes: [],
      fields: []
    };
  }

  // Get all unique file types
  const fileTypes = new Set<string>();
  props.documents.forEach(doc => {
    const path = doc.gcs_path || '';
    const lastDotIndex = path.lastIndexOf('.');
    const extension = lastDotIndex !== -1 ? path.substring(lastDotIndex + 1).toLowerCase() : 'unknown';
    const type = extension === 'unknown' ? 'No Extension' : `.${extension}`;
    fileTypes.add(type);
  });

  const fileTypeArray = Array.from(fileTypes).sort();
  const fields = props.config.data.fields.map(f => f.name).sort();

  return {
    data: [], // Will be computed per extractor
    fileTypes: fileTypeArray,
    fields: fields
  };
});

const getHeatMapOptionForExtractor = (extractor: string) => {
  const { fileTypes, fields } = fileTypeHeatMapData.value;
  
  if (fileTypes.length === 0 || fields.length === 0) {
    return {};
  }

  // Calculate heat map data for this specific extractor
  const heatMapData: Array<[number, number, number]> = [];
  
  // Add summary column data
  const fieldsWithSummary = [...fields, 'Overall Summary'];
  
  fileTypes.forEach((fileType, fileTypeIndex) => {
    fieldsWithSummary.forEach((field, fieldIndex) => {
      let successRate = 0;
      
      if (field === 'Overall Summary') {
        // Calculate overall success rate for this file type across all fields
        let totalCorrect = 0;
        let totalProcessed = 0;
        
        fields.forEach(fieldName => {
          const fieldConfig = props.config?.data?.fields.find(f => f.name === fieldName);
          if (!fieldConfig) return;
          
          const documentsOfType = props.documents.filter(doc => {
            const path = doc.gcs_path || '';
            const lastDotIndex = path.lastIndexOf('.');
            const extension = lastDotIndex !== -1 ? path.substring(lastDotIndex + 1).toLowerCase() : 'unknown';
            const type = extension === 'unknown' ? 'No Extension' : `.${extension}`;
            return type === fileType;
          });

          documentsOfType.forEach(doc => {
            const docExtractions = documentExtractions.value.get(doc.id);
            const docGroundTruth = documentGroundTruth.value.get(doc.id);
            
            if (docExtractions?.extraction_results && docGroundTruth) {
              const extractorResult = docExtractions.extraction_results.find((r: any) => {
                // Handle both old 'method' field and new 'provider' field
                if (r.provider) {
                  return r.provider.includes(extractor) || r.provider === extractor;
                } else if (r.method) {
                  return r.method.includes(extractor);
                }
                return false;
              });
              
              // Check for both failed flag and extraction data errors
              // The error might be stored as a field called "error" in the extracted_data
              const hasError = extractorResult?.failed || 
                              extractorResult?.extracted_data?.error ||
                              extractorResult?.raw_response?.error;
              
              if (extractorResult && !hasError) {
                const extractedValue = extractorResult.extracted_data?.[fieldName];
                const groundTruthObj = docGroundTruth[fieldName];
                const groundTruthValue = groundTruthObj?.user_annotation ?? groundTruthObj?.extracted_value;

                if (groundTruthObj !== undefined) {
                  totalProcessed++;
                  
                  const hasExtractedValue = extractedValue !== undefined && extractedValue !== null && extractedValue !== '';
                  const hasGroundTruthValue = groundTruthValue !== undefined && groundTruthValue !== null && groundTruthValue !== '';

                  if (hasGroundTruthValue && hasExtractedValue) {
                    const correctnessScore = calculateCorrectness(extractedValue, groundTruthValue, fieldConfig.field_type);
                    if (correctnessScore >= 95) {
                      totalCorrect++;
                    }
                  } else if (!hasGroundTruthValue && !hasExtractedValue) {
                    totalCorrect++;
                  }
                }
              }
            }
          });
        });
        
        successRate = totalProcessed > 0 ? Math.round((totalCorrect / totalProcessed) * 100) : 0;
      } else {
        // Calculate success rate for specific field and file type
        const fieldConfig = props.config?.data?.fields.find(f => f.name === field);
        if (!fieldConfig) return;
        
        let totalCorrect = 0;
        let totalProcessed = 0;

        const documentsOfType = props.documents.filter(doc => {
          const path = doc.gcs_path || '';
          const lastDotIndex = path.lastIndexOf('.');
          const extension = lastDotIndex !== -1 ? path.substring(lastDotIndex + 1).toLowerCase() : 'unknown';
          const type = extension === 'unknown' ? 'No Extension' : `.${extension}`;
          return type === fileType;
        });

        documentsOfType.forEach(doc => {
          const docExtractions = documentExtractions.value.get(doc.id);
          const docGroundTruth = documentGroundTruth.value.get(doc.id);
          
          if (docExtractions?.extraction_results && docGroundTruth) {
            const extractorResult = docExtractions.extraction_results.find((r: any) => {
              // Handle both old 'method' field and new 'provider' field
              if (r.provider) {
                return r.provider.includes(extractor) || r.provider === extractor;
              } else if (r.method) {
                return r.method.includes(extractor);
              }
              return false;
            });
            
            // Check for both failed flag and extraction data errors
            // The error might be stored as a field called "error" in the extracted_data
            const hasError = extractorResult?.failed || 
                            extractorResult?.extracted_data?.error ||
                            extractorResult?.raw_response?.error;
            
            if (extractorResult && !hasError) {
              const extractedValue = extractorResult.extracted_data?.[field];
              const groundTruthObj = docGroundTruth[field];
              const groundTruthValue = groundTruthObj?.user_annotation ?? groundTruthObj?.extracted_value;

              if (groundTruthObj !== undefined) {
                totalProcessed++;
                
                const hasExtractedValue = extractedValue !== undefined && extractedValue !== null && extractedValue !== '';
                const hasGroundTruthValue = groundTruthValue !== undefined && groundTruthValue !== null && groundTruthValue !== '';

                if (hasGroundTruthValue && hasExtractedValue) {
                  const correctnessScore = calculateCorrectness(extractedValue, groundTruthValue, fieldConfig.field_type);
                  if (correctnessScore >= 95) {
                    totalCorrect++;
                  }
                } else if (!hasGroundTruthValue && !hasExtractedValue) {
                  totalCorrect++;
                }
              }
            }
          }
        });

        successRate = totalProcessed > 0 ? Math.round((totalCorrect / totalProcessed) * 100) : 0;
      }
      
      heatMapData.push([fieldIndex, fileTypeIndex, successRate]);
    });
  });

  return {
    tooltip: {
      position: 'top',
      formatter: function (params: any) {
        const fieldName = fieldsWithSummary[params.data[0]];
        const fileType = fileTypes[params.data[1]];
        const successRate = params.data[2];
        return `${fieldName}<br/>${fileType}<br/>Success Rate: ${successRate}%`;
      }
    },
    grid: {
      height: '65%',
      top: '8%',
      left: '20%',
      right: '5%',
      bottom: '25%'
    },
    xAxis: {
      type: 'category',
      data: fieldsWithSummary,
      splitArea: {
        show: true
      },
      axisLabel: {
        rotate: 45,
        fontSize: 10,
        interval: 0,
        overflow: 'truncate',
        width: 80
      }
    },
    yAxis: {
      type: 'category',
      data: fileTypes,
      splitArea: {
        show: true
      },
      axisLabel: {
        fontSize: 10,
        overflow: 'truncate',
        width: 100
      }
    },
    visualMap: {
      min: 0,
      max: 100,
      calculable: true,
      orient: 'horizontal',
      left: 'center',
      bottom: '2%',
      inRange: {
        color: ['#d73027', '#fc8d59', '#fee08b', '#91d5ff', '#4575b4']
      },
      text: ['High Success', 'Low Success'],
      textStyle: {
        fontSize: 11
      }
    },
    series: [{
      name: 'Extraction Success Rate',
      type: 'heatmap',
      data: heatMapData,
      label: {
        show: true,
        formatter: function(params: any) {
          return params.data[2] + '%';
        },
        fontSize: 10,
        fontWeight: 'bold'
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  };
};

// Remove the loadAnalyticsData function since we now use props
// const loadAnalyticsData = async () => { ... }
</script> 


================================================
FILE: src/components/ExtractorHeader.vue
================================================
 <template>
 <div 
    v-if="extractors.length > 0"
    class="grid gap-4 pb-2 border-b border-border sticky top-0 bg-card z-10"
    :style="{ gridTemplateColumns: `repeat(${extractors.length + 1}, minmax(0, 1fr))` }"
        >
          <div class="col-span-1"></div>
          <div v-for="extractor in extractors" :key="extractor.id" class="text-center col-span-1">
            <div class="flex items-center justify-center space-x-3 mb-3">
              <!-- Provider Logo -->
              <ExtractorLogo :extractor="extractor.model || 'unknown'" :size="24" />
              
              <h3 class="font-semibold text-sm text-foreground">{{ extractor.model }}</h3>
              
              <!-- Status Indicator -->
              <div class="flex items-center">
                <Skeleton v-if="extractorStatus[extractor.id] === 'loading'" class="w-5 h-5 rounded animate-pulse" />
                <CheckCircle2 v-else-if="extractorStatus[extractor.id] === 'success'" class="h-5 w-5 text-green-500" />
                <XCircle v-else-if="extractorStatus[extractor.id] === 'error'" class="h-5 w-5 text-red-500" />
                <div v-else class="h-5 w-5"></div>
              </div>
            </div>
            
            <!-- Individual run/rerun button for each extractor -->
            <div class="flex items-center justify-center mb-2">
              <button
                @click="runSingleExtraction(extractor.id)"
                :disabled="isInitializing || extractorStatus[extractor.id] === 'loading' || !extractionConfig"
                class="p-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed transition-colors shadow-sm group"
                :title="`${extractions[extractor.id] ? 'Rerun' : 'Run'} ${extractor.name || extractor.model} extractor`"
              >
                <Play v-if="!extractions[extractor.id]" class="h-4 w-4" />
                <RotateCcw v-else class="h-4 w-4 group-hover:rotate-180 transition-transform duration-300" />
              </button>
            </div>
            
            <p class="text-xs text-muted-foreground mt-1 h-4">
              <span v-if="extractions[extractor.id]?.processing_time">
                {{ Number(extractions[extractor.id]?.processing_time || 0).toFixed(1) }}s processing time
              </span>
            </p>
          </div>
        </div>

</template>

<script setup lang="ts">
import { Extractor } from '@/types'
import { useResourcePool } from '@/services/resourcePool.js'
import { computed } from 'vue'

const {
  extractions
} = useResourcePool()
</script>


================================================
FILE: src/components/ExtractorLogo.vue
================================================
<template>
  <div class="flex items-center justify-center" :class="sizeClasses">
    <!-- Gemini Logo -->
    <div v-if="normalizedExtractor === 'gemini'" class="flex items-center justify-center">
      <img 
        src="../assets/logos/gemini.svg" 
        :width="size" 
        :height="size" 
        alt="Gemini"
        class="object-contain"
      />
    </div>
    
    <!-- Mistral Logo -->
    <div v-else-if="normalizedExtractor === 'mistral'" class="flex items-center justify-center">
      <img 
        src="../assets/logos/mistral.svg" 
        :width="size" 
        :height="size" 
        alt="Mistral"
        class="object-contain"
      />
    </div>
    
    <!-- Default/Unknown Extractor -->
    <div v-else class="flex items-center justify-center bg-gray-200 rounded-full" :style="{ width: size + 'px', height: size + 'px' }">
      <span class="text-xs font-medium text-gray-600 uppercase">{{ extractor.slice(0, 2) }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  extractor: string;
  size?: number;
}

const props = withDefaults(defineProps<Props>(), {
  size: 16
});

const normalizedExtractor = computed(() => {
  const extractorLower = props.extractor.toLowerCase();
  
  if (extractorLower.includes('gemini')) {
    return 'gemini';
  } else if (extractorLower.includes('mistral')) {
    return 'mistral';
  }
  
  return extractorLower;
});

const sizeClasses = computed(() => {
  if (props.size <= 16) return 'w-4 h-4';
  if (props.size <= 20) return 'w-5 h-5';
  if (props.size <= 24) return 'w-6 h-6';
  return 'w-8 h-8';
});
</script> 


================================================
FILE: src/components/ExtractorPanel.vue
================================================
<template>
  <div class="flex flex-col items-center gap-1">
    <!-- Horizontal Layout: Logo, PercentBadge, Button -->
    <div class="flex items-center gap-2">
      <!-- Extractor Logo -->
      <div class="w-4 flex justify-center">
        <Skeleton v-if="isExtracting" class="w-4 h-4 rounded" />
        <ExtractorLogo v-else :extractor="extractor" :size="16" />
      </div>
      
      <!-- Accuracy Badge -->
      <div class="w-20 flex justify-center">
        <Skeleton v-if="isExtracting" class="w-12 h-5 rounded" />
        <PercentBadge v-else-if="accuracy?.hasGroundTruth" :percentage="accuracy.percentage" />
        <div v-else-if="hasExtraction && accuracy && !accuracy.hasGroundTruth" class="px-2 py-0.5 text-xs font-medium rounded border bg-gray-100 text-gray-500 border-gray-200">
          No GT
        </div>
        <div v-else-if="hasExtraction" class="px-2 py-0.5 text-xs font-medium rounded border bg-gray-100 text-gray-400 border-gray-200">
          No GT
        </div>
        <div v-else class="px-2 py-0.5 text-xs font-medium rounded border bg-gray-100 text-gray-400 border-gray-200">
          None
        </div>
      </div>
      
      <!-- Extraction Button -->
      <div class="w-5 flex justify-center">
        <button
          @click="$emit('extract')"
          :disabled="disabled"
          :class="{
            'bg-primary text-primary-foreground hover:bg-primary/90': !hasExtraction,
            'bg-secondary text-secondary-foreground hover:bg-secondary/80 border border-primary/20': hasExtraction
          }"
          class="w-5 h-5 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
          :title="`${hasExtraction ? 'Re-run' : 'Run'} ${extractor}`"
        >
          <div v-if="isExtracting" class="animate-spin rounded-full h-2 w-2 border-b-2 border-current"></div>
          <RotateCcw v-else-if="hasExtraction" class="h-2.5 w-2.5" />
          <Play v-else class="h-2.5 w-2.5" />
        </button>
      </div>
    </div>
    
    <!-- Progress Bar -->
    <div v-if="extractionStatus" class="w-full mt-1">
      <div class="w-full h-1 bg-gray-200 rounded-full overflow-hidden">
        <div 
          class="h-full transition-all duration-300"
          :class="{
            'bg-blue-500': extractionStatus.status !== 'failed' && extractionStatus.status !== 'completed',
            'bg-green-500': extractionStatus.status === 'completed',
            'bg-red-500': extractionStatus.status === 'failed'
          }"
          :style="{ width: `${extractionStatus.progress || 0}%` }"
        ></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import ExtractorLogo from './ExtractorLogo.vue';
import PercentBadge from './PercentBadge.vue';
import { Skeleton } from '@/components/ui/skeleton';
import { Play, RotateCcw } from 'lucide-vue-next';

interface Props {
  extractor: string;
  accuracy?: {
    correct: number;
    total: number;
    percentage: number;
    hasGroundTruth: boolean;
  } | null;
  hasExtraction: boolean;
  isExtracting: boolean;
  disabled: boolean;
  extractionStatus?: {
    status: string;
    progress: number;
    message: string;
  } | null;
}

defineProps<Props>();
defineEmits<{
  extract: [];
}>();
</script>


================================================
FILE: src/components/ExtractorPanelSkeleton.vue
================================================
<template>
  <div class="flex flex-col items-center gap-1">
    <!-- Horizontal Layout: Logo, PercentBadge, Button -->
    <div class="flex items-center gap-2">
      <!-- Extractor Logo Skeleton -->
      <div class="w-4 flex justify-center">
        <Skeleton class="w-4 h-4 rounded" />
      </div>
      
      <!-- Accuracy Badge Skeleton -->
      <div class="w-20 flex justify-center">
        <Skeleton class="w-12 h-5 rounded" />
      </div>
      
      <!-- Extraction Button Skeleton -->
      <div class="w-5 flex justify-center">
        <Skeleton class="w-5 h-5 rounded-md" />
      </div>
    </div>
    
    <!-- Progress Bar Skeleton -->
    <div class="w-full mt-1">
      <Skeleton class="w-full h-1 rounded-full" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { Skeleton } from '@/components/ui/skeleton'
</script>
</template>


================================================
FILE: src/components/FieldComparison.vue
================================================
<template>
  <div 
    class="grid gap-4 items-start py-4 border-b border-border"
    :style="{ gridTemplateColumns: `repeat(${extractionProviders.length + 1}, minmax(0, 1fr))` }"
  >
    <!-- Column 1: Field Name & Icon + Ground Truth -->
    <div class="col-span-1 sticky top-16">
      <div class="space-y-3">
        <!-- Field Header -->
        <div class="flex items-center space-x-2">
          <FieldTypeIcon :type="field.results[extractionProviders[0]?.id]?.type || 'text'" />
          <h4 class="font-medium text-foreground">
            {{ field.name }}
          </h4>
        </div>
        
        <!-- Unified Ground Truth Input -->
        <div class="space-y-2">
          <div 
            class="p-2 rounded-md border border-dashed"
            :class="{
              'bg-blue-50/50 border-blue-200': hasAnnotation && !isMarkedNotExtractable,
              'bg-red-50/50 border-red-200': isMarkedNotExtractable,
              'bg-muted/50 border-border': !hasAnnotation && !isMarkedNotExtractable
            }"
          >
            <label class="block text-xs font-medium mb-1" 
              :class="{
                'text-blue-700': hasAnnotation && !isMarkedNotExtractable,
                'text-red-700': isMarkedNotExtractable,
                'text-muted-foreground': !hasAnnotation && !isMarkedNotExtractable
              }"
            >
              {{ isMarkedNotExtractable ? 'Marked as Not Extractable' : hasAnnotation ? 'Overridden by Annotation' : 'Ground Truth (Database)' }}
            </label>
            <div v-if="isMarkedNotExtractable" class="flex items-center justify-between">
              <div class="flex-1 px-3 py-2 text-xs bg-red-50 border border-red-200 rounded text-red-700">
                Model should return null.
              </div>
              <button 
                @click="clearNotExtractableMarking(field.name)" 
                class="ml-2 p-1.5 hover:bg-green-50 hover:text-green-600 rounded-md border border-green-200 text-green-500"
                title="Clear not extractable marking"
              >
                <RotateCcw class="h-3 w-3" />
              </button>
            </div>
            <div v-else class="flex items-center gap-2">
              <input
                :value="effectiveGroundTruth"
                @input="updateGroundTruthInput(field.name, $event)"
                @blur="saveAnnotation(field.name)"
                :type="getInputType(field.results[extractionProviders[0]?.id]?.type || 'text')"
                :placeholder="getPlaceholder(field.results[extractionProviders[0]?.id]?.type || 'text')"
                class="w-full px-2 py-1 text-sm bg-background border rounded focus:outline-none focus:ring-1 focus:ring-primary"
                :class="{ 
                  'border-blue-300': hasAnnotation, 
                  'border-border': !hasAnnotation 
                }"
              />
              <button 
                v-if="hasAnnotation" 
                @click="revertAnnotation(field.name)" 
                class="p-1.5 hover:bg-muted rounded-md" 
                title="Revert to database value"
              >
                <RotateCcw class="h-3 w-3 text-muted-foreground" />
              </button>
              <button 
                @click="markAsNotExtractable(field.name)" 
                class="p-1.5 hover:bg-red-50 hover:text-red-600 rounded-md border border-red-200 text-red-500"
                title="Mark field as not extractable from this document"
              >
                <X class="h-3 w-3" />
              </button>
            </div>
            <div v-if="groundTruthData[field.name]" class="mt-2 text-xs text-muted-foreground">
              DB Value: 
              <span class="font-mono bg-gray-200 text-gray-600 px-1 py-0.5 rounded">
                {{ isMarkedNotExtractable ? 'null' : (groundTruthData[field.name].extracted_value ?? 'null') }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Columns 2+: Provider Results -->
    <div v-for="provider in extractionProviders" :key="provider.id" class="col-span-1">
      <div class="space-y-3">
        <!-- Empty space to align with field header -->
        <div class="h-6"></div>
        
        <!-- Extraction field aligned with ground truth input -->
        <div class="space-y-2">
          <ExtractionField
            :value="field.results[provider.id]"
            :hide-header="true"
            :hide-ground-truth-toggle="true"
            @hover="$emit('hover', $event)"
            @unhover="$emit('unhover')"
            @click="handleExtractedValueClick(field.name, field.results[provider.id])"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import ExtractionField from './ExtractionField.vue'
import FieldTypeIcon from './FieldTypeIcon.vue'
import { RotateCcw, X } from 'lucide-vue-next'
import type { FieldData } from '@/types'

interface FieldComparison {
  name: string
  results: Record<string, FieldData>
}

interface Provider {
  id: string
  name: string
}

interface GroundTruthDetail {
  extracted_value: any
  user_annotation: any
}

interface Props {
  field: FieldComparison
  extractionProviders: Provider[]
  groundTruthData: Record<string, GroundTruthDetail>
}

interface Emits {
  (e: 'updateAnnotation', fieldName: string, value: any): void
  (e: 'removeAnnotation', fieldName: string): void
  (e: 'hover', fieldName: string): void
  (e: 'unhover'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const hasAnnotation = computed(() => {
  const gt = props.groundTruthData[props.field.name]
  // Check if user_annotation property exists (only present when annotation exists in DB)
  return gt && gt.hasOwnProperty('user_annotation')
})

const effectiveGroundTruth = computed(() => {
  const gt = props.groundTruthData[props.field.name]
  const fieldType = props.field.results[props.extractionProviders[0]?.id]?.type || 'text'
  
  let value = ''
  // If user has made any annotation (including null for "not extractable")
  if (gt && gt.user_annotation !== undefined) {
    value = gt.user_annotation ?? '' // null becomes empty string for display
  } else {
    // Fall back to database value only if no user annotation exists
    value = gt?.extracted_value ?? ''
  }
  
  // Convert date values for display in HTML5 date input
  if (fieldType === 'date' && value && typeof value === 'string') {
    return convertDateForInput(value)
  }
  
  return value
})

const isMarkedNotExtractable = computed(() => {
  const gt = props.groundTruthData[props.field.name]
  // Marked as not extractable if user annotation exists and is explicitly null
  return hasAnnotation.value && gt.user_annotation === null
})

const getInputType = (fieldType: string): string => {
  switch (fieldType) {
    case 'number':
      return 'number'
    case 'date':
      return 'date'
    case 'boolean':
      return 'checkbox'
    default:
      return 'text'
  }
}

const getPlaceholder = (fieldType: string): string => {
  switch (fieldType) {
    case 'number':
      return 'Override with number...'
    case 'date':
      return 'YYYY-MM-DD'
    case 'boolean':
      return ''
    default:
      return 'Override with text...'
  }
}

// Date conversion utilities
const convertDateForInput = (dateString: string): string => {
  if (!dateString || typeof dateString !== 'string') return ''
  
  // Try to parse various date formats and convert to YYYY-MM-DD
  try {
    // Check if already in YYYY-MM-DD format
    if (/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
      return dateString
    }
    
    // Handle DD.MM.YYYY format (like "24.03.2024")
    if (/^\d{1,2}\.\d{1,2}\.\d{4}$/.test(dateString)) {
      const [day, month, year] = dateString.split('.')
      return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`
    }
    
    // Handle DD/MM/YYYY format
    if (/^\d{1,2}\/\d{1,2}\/\d{4}$/.test(dateString)) {
      const [day, month, year] = dateString.split('/')
      return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`
    }
    
    // Handle MM/DD/YYYY format (US style)
    if (/^\d{1,2}\/\d{1,2}\/\d{4}$/.test(dateString)) {
      const [month, day, year] = dateString.split('/')
      return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`
    }
    
    // Try using Date constructor as fallback
    const date = new Date(dateString)
    if (!isNaN(date.getTime())) {
      return date.toISOString().split('T')[0]
    }
    
    return dateString // Return original if can't parse
  } catch (error) {
    console.warn('Failed to convert date for input:', dateString, error)
    return dateString
  }
}

const convertDateFromInput = (inputValue: string, originalFormat?: string): string => {
  if (!inputValue || typeof inputValue !== 'string') return inputValue
  
  // If input is in YYYY-MM-DD format, convert back to original format if possible
  if (/^\d{4}-\d{2}-\d{2}$/.test(inputValue)) {
    const [year, month, day] = inputValue.split('-')
    
    // If we know the original was DD.MM.YYYY format, convert back
    if (originalFormat && /^\d{1,2}\.\d{1,2}\.\d{4}$/.test(originalFormat)) {
      return `${parseInt(day)}.${parseInt(month)}.${year}`
    }
    
    // Default to DD.MM.YYYY format for new annotations
    return `${parseInt(day)}.${parseInt(month)}.${year}`
  }
  
  return inputValue
}

const updateGroundTruthInput = (fieldName: string, event: Event) => {
  const target = event.target as HTMLInputElement
  const value = target.value.trim()
  const fieldType = props.field.results[props.extractionProviders[0]?.id]?.type || 'text'
  
  // Convert date values back from HTML5 input format
  let processedValue = value
  if (fieldType === 'date' && value) {
    // Get original format from existing data
    const gt = props.groundTruthData[fieldName]
    const originalValue = gt?.user_annotation ?? gt?.extracted_value
    processedValue = convertDateFromInput(value, originalValue)
  }
  
  // Emit the processed annotation value
  emit('updateAnnotation', fieldName, processedValue === '' ? null : processedValue)
}

const revertAnnotation = (fieldName: string) => {
  // Completely remove the user annotation to fall back to database value
  emit('removeAnnotation', fieldName);
}

const markAsNotExtractable = (fieldName: string) => {
  // Set annotation to null to indicate field is not extractable from this document
  emit('updateAnnotation', fieldName, null);
}

const clearNotExtractableMarking = (fieldName: string) => {
  // Completely remove the user annotation to fall back to database value
  emit('removeAnnotation', fieldName);
}

const saveAnnotation = (fieldName: string) => {
  // Get the current annotation value from the reactive data
  const gt = props.groundTruthData[fieldName]
  const currentValue = gt?.user_annotation
  
  // Get field type from first provider's result
  const fieldType = props.field.results[props.extractionProviders[0].id]?.type || 'text'
  
  // Check if the value is empty or null
  if (currentValue === '' || currentValue === null || currentValue === undefined) {
    emit('updateAnnotation', fieldName, null)
    return
  }
  
  let finalValue = currentValue
  
  // Process the value based on field type
  if (fieldType === 'number' && typeof currentValue === 'string' && currentValue !== '') {
    const numericValue = parseFloat(currentValue)
    finalValue = isNaN(numericValue) ? null : numericValue
  } else if (fieldType === 'boolean') {
    finalValue = Boolean(currentValue)
  } else if (fieldType === 'date' && typeof currentValue === 'string' && currentValue !== '') {
    // For dates, convert from input format back to storage format
    const originalValue = gt?.extracted_value
    finalValue = convertDateFromInput(currentValue, originalValue)
  }
  
  emit('updateAnnotation', fieldName, finalValue)
}

const handleExtractedValueClick = (fieldName: string, fieldData: FieldData) => {
  // Set the extracted value as an annotation
  if (fieldData) {
    // Check if the extracted value represents "null" or "not extractable"
    const isNullEquivalent = (value: any): boolean => {
      if (value === null || value === undefined) return true;
      if (typeof value === 'string') {
        const trimmed = value.trim().toLowerCase();
        return trimmed === 'null' || trimmed === 'n/a' || trimmed === 'none' || trimmed === '' || trimmed === 'not available';
      }
      return false;
    };
    
    if (isNullEquivalent(fieldData.value)) {
      // If extracted value is null-equivalent, mark field as "Not Extractable"
      emit('updateAnnotation', fieldName, null);
    } else {
      // For non-null values, set as annotation exactly as extracted
      emit('updateAnnotation', fieldName, fieldData.value);
    }
  }
}
</script> 


================================================
FILE: src/components/FieldTypeIcon.vue
================================================
<template>
  <component 
    :is="iconComponent" 
    :class="className"
  />
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { 
  Type, 
  Hash, 
  Calendar, 
  ToggleLeft, 
  Percent, 
  DollarSign, 
  Mail, 
  Phone, 
  MapPin 
} from 'lucide-vue-next';

interface Props {
  type: string;
  class?: string;
}

const props = withDefaults(defineProps<Props>(), {
  class: 'h-4 w-4'
});

const className = computed(() => props.class);

const iconComponent = computed(() => {
  const iconMap: Record<string, any> = {
    'text': Type,
    'number': Hash,
    'date': Calendar,
    'boolean': ToggleLeft,
    'percentage': Percent,
    'currency': DollarSign,
    'email': Mail,
    'phone': Phone,
    'address': MapPin
  };
  
  return iconMap[props.type] || Type;
});
</script> 


================================================
FILE: src/components/ImageViewer.vue
================================================
<template>
  <div class="image-viewer h-full w-full relative bg-gray-50 overflow-hidden">
    <!-- Image Controls Bar -->
    <div class="absolute top-4 left-1/2 transform -translate-x-1/2 z-20 bg-white/90 backdrop-blur-sm rounded-lg shadow-lg px-4 py-2">
      <div class="flex items-center space-x-2">
        <!-- Zoom Controls -->
        <button 
          @click="zoomOut" 
          :disabled="scale <= minScale"
          class="p-2 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
          title="Zoom Out"
        >
          <ZoomOut class="w-4 h-4" />
        </button>
        
        <span class="text-sm text-gray-600 min-w-[60px] text-center font-medium">
          {{ Math.round(scale * 100) }}%
        </span>
        
        <button 
          @click="zoomIn" 
          :disabled="scale >= maxScale"
          class="p-2 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
          title="Zoom In"
        >
          <ZoomIn class="w-4 h-4" />
        </button>
        
        <div class="w-px h-6 bg-gray-300 mx-2"></div>
        
        <!-- Fit Controls -->
        <button 
          @click="fitToScreen" 
          class="p-2 rounded hover:bg-gray-100"
          title="Fit to Screen"
        >
          <Maximize class="w-4 h-4" />
        </button>
        
        <button 
          @click="actualSize" 
          class="p-2 rounded hover:bg-gray-100"
          title="Actual Size (100%)"
        >
          <Minimize class="w-4 h-4" />
        </button>
        
        <div class="w-px h-6 bg-gray-300 mx-2"></div>
        
        <!-- Rotation Controls -->
        <button 
          @click="rotateLeft" 
          class="p-2 rounded hover:bg-gray-100"
          title="Rotate Left"
        >
          <RotateCcw class="w-4 h-4" />
        </button>
        
        <button 
          @click="rotateRight" 
          class="p-2 rounded hover:bg-gray-100"
          title="Rotate Right"
        >
          <RotateCw class="w-4 h-4" />
        </button>
        
        <div class="w-px h-6 bg-gray-300 mx-2"></div>
        
        <!-- Fullscreen Toggle -->
        <button 
          @click="toggleFullscreen" 
          class="p-2 rounded hover:bg-gray-100"
          title="Toggle Fullscreen"
        >
          <Expand v-if="!isFullscreen" class="w-4 h-4" />
          <Shrink v-else class="w-4 h-4" />
        </button>
      </div>
    </div>
    
    <!-- Image Container -->
    <div 
      ref="containerRef"
      class="image-container h-full w-full overflow-hidden cursor-grab active:cursor-grabbing"
      :class="{ 'cursor-zoom-in': scale < maxScale && !isDragging, 'cursor-zoom-out': scale > minScale && !isDragging }"
      @mousedown="startDrag"
      @mousemove="drag"
      @mouseup="endDrag"
      @mouseleave="endDrag"
      @wheel="handleWheel"
      @dblclick="handleDoubleClick"
    >
      <div 
        class="image-wrapper flex items-center justify-center h-full w-full"
        :style="{ 
          transform: `translate(${panX}px, ${panY}px)`,
          transition: isAnimating ? 'transform 0.3s ease-out' : 'none'
        }"
      >
        <img 
          ref="imageRef"
          :src="imageUrl" 
          :alt="alt"
          class="max-w-none select-none transition-transform duration-200"
          :style="{ 
            transform: `scale(${scale}) rotate(${rotation}deg)`,
            transformOrigin: 'center center'
          }"
          @load="onImageLoad"
          @error="onImageError"
          @dragstart.prevent
        />
      </div>
    </div>
    
    <!-- Loading State -->
    <div v-if="loading" class="absolute inset-0 flex items-center justify-center bg-gray-50">
      <div class="flex flex-col items-center space-y-4">
        <Loader2 class="w-8 h-8 animate-spin text-blue-500" />
        <p class="text-gray-600">Loading image...</p>
      </div>
    </div>
    
    <!-- Error State -->
    <div v-if="error" class="absolute inset-0 flex items-center justify-center bg-gray-50">
      <div class="text-center max-w-md px-4">
        <AlertCircle class="w-12 h-12 text-red-500 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-900 mb-2">Failed to load image</h3>
        <p class="text-gray-600 mb-4">{{ error }}</p>
        <button 
          @click="retryLoad"
          class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          Try Again
        </button>
      </div>
    </div>
    
    <!-- Image Info Overlay -->
    <div v-if="showInfo && !loading && !error" class="absolute bottom-4 left-4 bg-black/70 text-white px-3 py-2 rounded-lg text-sm">
      <div class="space-y-1">
        <div>{{ imageWidth }} × {{ imageHeight }}px</div>
        <div>{{ formatFileSize(fileSize) }}</div>
        <div>{{ scale.toFixed(1) }}× zoom</div>
      </div>
    </div>
    
    <!-- Toggle Info Button -->
    <button 
      v-if="!loading && !error"
      @click="showInfo = !showInfo"
      class="absolute bottom-4 right-4 p-2 bg-black/70 text-white rounded-lg hover:bg-black/80 transition-colors"
      title="Toggle Image Info"
    >
      <Info class="w-4 h-4" />
    </button>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { 
  ZoomIn, 
  ZoomOut, 
  RotateCw, 
  RotateCcw, 
  Maximize, 
  Minimize, 
  Expand, 
  Shrink, 
  Loader2, 
  AlertCircle,
  Info
} from 'lucide-vue-next'

interface Props {
  imageUrl: string
  alt?: string
  fileSize?: number
}

const props = withDefaults(defineProps<Props>(), {
  alt: 'Image',
  fileSize: 0
})

// Reactive state
const loading = ref(true)
const error = ref<string | null>(null)
const scale = ref(1)
const rotation = ref(0)
const panX = ref(0)
const panY = ref(0)
const isDragging = ref(false)
const isAnimating = ref(false)
const isFullscreen = ref(false)
const showInfo = ref(false)

// Image dimensions
const imageWidth = ref(0)
const imageHeight = ref(0)

// Container and image refs
const containerRef = ref<HTMLElement>()
const imageRef = ref<HTMLImageElement>()

// Drag state
const dragStart = ref({ x: 0, y: 0 })
const panStart = ref({ x: 0, y: 0 })

// Scale limits
const minScale = 0.1
const maxScale = 5.0

// Methods
const zoomIn = () => {
  const newScale = Math.min(scale.value * 1.2, maxScale)
  setScale(newScale)
}

const zoomOut = () => {
  const newScale = Math.max(scale.value / 1.2, minScale)
  setScale(newScale)
}

const setScale = (newScale: number, animate = true) => {
  if (animate) {
    isAnimating.value = true
    setTimeout(() => {
      isAnimating.value = false
    }, 300)
  }
  scale.value = newScale
}

const fitToScreen = () => {
  if (!containerRef.value || !imageRef.value) return
  
  const container = containerRef.value.getBoundingClientRect()
  const containerWidth = container.width - 20 // Minimal padding for UI elements
  const containerHeight = container.height - 80 // Account for controls bar at top
  
  const scaleX = containerWidth / imageWidth.value
  const scaleY = containerHeight / imageHeight.value
  const newScale = Math.min(scaleX, scaleY, 1) // Don't scale up beyond 100%
  
  setScale(newScale)
  centerImage()
}

const actualSize = () => {
  setScale(1)
  centerImage()
}

const centerImage = () => {
  isAnimating.value = true
  panX.value = 0
  panY.value = 0
  setTimeout(() => {
    isAnimating.value = false
  }, 300)
}

const rotateLeft = () => {
  rotation.value -= 90
  if (rotation.value < 0) rotation.value += 360
}

const rotateRight = () => {
  rotation.value += 90
  if (rotation.value >= 360) rotation.value -= 360
}

const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    containerRef.value?.requestFullscreen()
  } else {
    document.exitFullscreen()
  }
}

const startDrag = (event: MouseEvent) => {
  if (event.button !== 0) return // Only left mouse button
  
  isDragging.value = true
  dragStart.value = { x: event.clientX, y: event.clientY }
  panStart.value = { x: panX.value, y: panY.value }
  
  event.preventDefault()
}

const drag = (event: MouseEvent) => {
  if (!isDragging.value) return
  
  const deltaX = event.clientX - dragStart.value.x
  const deltaY = event.clientY - dragStart.value.y
  
  panX.value = panStart.value.x + deltaX
  panY.value = panStart.value.y + deltaY
}

const endDrag = () => {
  isDragging.value = false
}

const handleWheel = (event: WheelEvent) => {
  event.preventDefault()
  
  const delta = -event.deltaY
  const scaleFactor = delta > 0 ? 1.1 : 0.9
  const newScale = Math.max(minScale, Math.min(maxScale, scale.value * scaleFactor))
  
  setScale(newScale, false)
}

const handleDoubleClick = () => {
  if (scale.value === 1) {
    fitToScreen()
  } else {
    actualSize()
  }
}

const onImageLoad = () => {
  loading.value = false
  error.value = null
  
  if (imageRef.value) {
    imageWidth.value = imageRef.value.naturalWidth
    imageHeight.value = imageRef.value.naturalHeight
    
    // Auto-fit on first load
    nextTick(() => {
      fitToScreen()
    })
  }
}

const onImageError = () => {
  loading.value = false
  error.value = 'Failed to load image. The file may be corrupted or unavailable.'
}

const retryLoad = () => {
  loading.value = true
  error.value = null
  
  if (imageRef.value) {
    // Force reload by adding timestamp
    const url = new URL(props.imageUrl)
    url.searchParams.set('retry', Date.now().toString())
    imageRef.value.src = url.toString()
  }
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`
}

// Fullscreen event listeners
const handleFullscreenChange = () => {
  isFullscreen.value = !!document.fullscreenElement
}

// Keyboard shortcuts
const handleKeyDown = (event: KeyboardEvent) => {
  if (!containerRef.value?.contains(event.target as Node)) return
  
  switch (event.key) {
    case '+':
    case '=':
      event.preventDefault()
      zoomIn()
      break
    case '-':
      event.preventDefault()
      zoomOut()
      break
    case '0':
      event.preventDefault()
      actualSize()
      break
    case 'f':
    case 'F11':
      event.preventDefault()
      toggleFullscreen()
      break
    case 'r':
      event.preventDefault()
      if (event.shiftKey) {
        rotateLeft()
      } else {
        rotateRight()
      }
      break
    case 'Escape':
      if (document.fullscreenElement) {
        document.exitFullscreen()
      }
      break
  }
}

// Lifecycle
onMounted(() => {
  document.addEventListener('fullscreenchange', handleFullscreenChange)
  document.addEventListener('keydown', handleKeyDown)
})

onUnmounted(() => {
  document.removeEventListener('fullscreenchange', handleFullscreenChange)
  document.removeEventListener('keydown', handleKeyDown)
})

// Watch for image URL changes
watch(() => props.imageUrl, () => {
  loading.value = true
  error.value = null
  scale.value = 1
  rotation.value = 0
  panX.value = 0
  panY.value = 0
})
</script>



================================================
FILE: src/components/PercentBadge.vue
================================================
<template>
  <div 
    class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
    :class="badgeClasses"
  >
    <div class="w-2 h-2 rounded-full mr-1" :class="dotClasses"></div>
    <span class="font-mono">{{ displayText }}</span>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  percentage: number
}

const props = defineProps<Props>()

const displayText = computed(() => {
  return `${Math.round(props.percentage)}%`
})

const badgeClasses = computed(() => {
  if (props.percentage >= 90) {
    return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
  } else if (props.percentage >= 70) {
    return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
  } else {
    return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
  }
})

const dotClasses = computed(() => {
  if (props.percentage >= 90) {
    return 'bg-green-500'
  } else if (props.percentage >= 70) {
    return 'bg-yellow-500'
  } else {
    return 'bg-red-500'
  }
})
</script> 


================================================
FILE: src/components/ui/card/Card.vue
================================================
<script setup lang="ts">
import type { HTMLAttributes } from 'vue'
import { cn } from '@/lib/utils'

const props = defineProps<{
  class?: HTMLAttributes['class']
}>()
</script>

<template>
  <div
    data-slot="card"
    :class="
      cn(
        'bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm',
        props.class,
      )
    "
  >
    <slot />
  </div>
</template>



================================================
FILE: src/components/ui/card/CardAction.vue
================================================
<script setup lang="ts">
import type { HTMLAttributes } from 'vue'
import { cn } from '@/lib/utils'

const props = defineProps<{
  class?: HTMLAttributes['class']
}>()
</script>

<template>
  <div
    data-slot="card-action"
    :class="cn('col-start-2 row-span-2 row-start-1 self-start justify-self-end', props.class)"
  >
    <slot />
  </div>
</template>



================================================
FILE: src/components/ui/card/CardContent.vue
================================================
<script setup lang="ts">
import type { HTMLAttributes } from 'vue'
import { cn } from '@/lib/utils'

const props = defineProps<{
  class?: HTMLAttributes['class']
}>()
</script>

<template>
  <div
    data-slot="card-content"
    :class="cn('px-6', props.class)"
  >
    <slot />
  </div>
</template>



================================================
FILE: src/components/ui/card/CardDescription.vue
================================================
<script setup lang="ts">
import type { HTMLAttributes } from 'vue'
import { cn } from '@/lib/utils'

const props = defineProps<{
  class?: HTMLAttributes['class']
}>()
</script>

<template>
  <p
    data-slot="card-description"
    :class="cn('text-muted-foreground text-sm', props.class)"
  >
    <slot />
  </p>
</template>



================================================
FILE: src/components/ui/card/CardFooter.vue
================================================
<script setup lang="ts">
import type { HTMLAttributes } from 'vue'
import { cn } from '@/lib/utils'

const props = defineProps<{
  class?: HTMLAttributes['class']
}>()
</script>

<template>
  <div
    data-slot="card-footer"
    :class="cn('flex items-center px-6 [.border-t]:pt-6', props.class)"
  >
    <slot />
  </div>
</template>



================================================
FILE: src/components/ui/card/CardHeader.vue
================================================
<script setup lang="ts">
import type { HTMLAttributes } from 'vue'
import { cn } from '@/lib/utils'

const props = defineProps<{
  class?: HTMLAttributes['class']
}>()
</script>

<template>
  <div
    data-slot="card-header"
    :class="cn('@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6', props.class)"
  >
    <slot />
  </div>
</template>



================================================
FILE: src/components/ui/card/CardTitle.vue
================================================
<script setup lang="ts">
import type { HTMLAttributes } from 'vue'
import { cn } from '@/lib/utils'

const props = defineProps<{
  class?: HTMLAttributes['class']
}>()
</script>

<template>
  <h3
    data-slot="card-title"
    :class="cn('leading-none font-semibold', props.class)"
  >
    <slot />
  </h3>
</template>



================================================
FILE: src/components/ui/card/index.ts
================================================
export { default as Card } from './Card.vue'
export { default as CardAction } from './CardAction.vue'
export { default as CardContent } from './CardContent.vue'
export { default as CardDescription } from './CardDescription.vue'
export { default as CardFooter } from './CardFooter.vue'
export { default as CardHeader } from './CardHeader.vue'
export { default as CardTitle } from './CardTitle.vue'



================================================
FILE: src/components/ui/skeleton/index.ts
================================================
export { default as Skeleton } from './Skeleton.vue'



================================================
FILE: src/components/ui/skeleton/Skeleton.vue
================================================
<script setup lang="ts">
import type { HTMLAttributes } from 'vue'
import { cn } from '@/lib/utils'

interface SkeletonProps {
  class?: HTMLAttributes['class']
}

const props = defineProps<SkeletonProps>()
</script>

<template>
  <div
    data-slot="skeleton"
    :class="cn('animate-pulse rounded-md bg-primary/10', props.class)"
  />
</template>



================================================
FILE: src/components/ui/table/index.ts
================================================
export { default as Table } from './Table.vue'
export { default as TableHeader } from './TableHeader.vue'
export { default as TableBody } from './TableBody.vue'
export { default as TableRow } from './TableRow.vue'
export { default as TableHead } from './TableHead.vue'
export { default as TableCell } from './TableCell.vue'



================================================
FILE: src/components/ui/table/Table.vue
================================================
<template>
  <div class="relative w-full overflow-auto">
    <table class="w-full caption-bottom text-sm text-gray-900 dark:text-gray-100">
      <slot />
    </table>
  </div>
</template>

<script setup lang="ts">
// Table root component
</script>



================================================
FILE: src/components/ui/table/TableBody.vue
================================================
<template>
  <tbody class="[&_tr:last-child]:border-0 [&_tr]:border-gray-200 dark:[&_tr]:border-gray-700">
    <slot />
  </tbody>
</template>

<script setup lang="ts">
// Table body component
</script>



================================================
FILE: src/components/ui/table/TableCell.vue
================================================
<template>
  <td class="p-4 align-middle text-gray-900 dark:text-gray-100 [&:has([role=checkbox])]:pr-0">
    <slot />
  </td>
</template>

<script setup lang="ts">
// Table cell component
</script>



================================================
FILE: src/components/ui/table/TableHead.vue
================================================
<template>
  <th class="h-12 px-4 text-left align-middle font-medium text-gray-600 dark:text-gray-400 [&:has([role=checkbox])]:pr-0">
    <slot />
  </th>
</template>

<script setup lang="ts">
// Table head cell component
</script>



================================================
FILE: src/components/ui/table/TableHeader.vue
================================================
<template>
  <thead class="[&_tr]:border-b [&_tr]:border-gray-200 dark:[&_tr]:border-gray-700">
    <slot />
  </thead>
</template>

<script setup lang="ts">
// Table header component
</script>



================================================
FILE: src/components/ui/table/TableRow.vue
================================================
<template>
  <tr class="border-b border-gray-200 dark:border-gray-700 transition-colors hover:bg-gray-50 dark:hover:bg-gray-800 data-[state=selected]:bg-gray-100 dark:data-[state=selected]:bg-gray-800">
    <slot />
  </tr>
</template>

<script setup lang="ts">
// Table row component
</script>



================================================
FILE: src/composables/useWebSocketSync.ts
================================================
import { ref, onMounted, onUnmounted } from 'vue'
import { applyPatch } from '@/services/resourcePool.js'

export const useWebSocketSync = (documentId?: string, datasetId?: string) => {
  const isConnected = ref(false)
  const error = ref<string | null>(null)
  let ws: WebSocket | null = null
  let reconnectTimer: number | null = null
  let reconnectAttempts = 0
  const maxReconnectAttempts = 5

  const connect = () => {
    try {
      // Connect to the new backend WebSocket endpoint
      const wsUrl = `ws://localhost:8000/ws`
      
      console.log('🔌 Connecting to WebSocket:', wsUrl)
      ws = new WebSocket(wsUrl)

      ws.onopen = () => {
        console.log('🔌 WebSocket connected for real-time updates')
        isConnected.value = true
        error.value = null
        reconnectAttempts = 0

        // Subscribe to extraction updates
        const subscriptions = ['extractions']
        if (documentId) {
          subscriptions.push(`document:${documentId}`)
        }

        ws?.send(JSON.stringify({
          type: 'subscribe',
          channels: subscriptions
        }))
      }

      ws.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data)
          handleMessage(message)
        } catch (err) {
          console.error('Failed to parse WebSocket message:', err)
        }
      }

      ws.onclose = (event) => {
        console.log('🔌 WebSocket disconnected:', event.code)
        isConnected.value = false
        
        // Attempt to reconnect if not a clean close
        if (event.code !== 1000 && reconnectAttempts < maxReconnectAttempts) {
          const delay = Math.min(1000 * Math.pow(2, reconnectAttempts), 30000)
          console.log(`🔄 Reconnecting WebSocket in ${delay}ms (attempt ${reconnectAttempts + 1}/${maxReconnectAttempts})`)
          
          reconnectTimer = window.setTimeout(() => {
            reconnectAttempts++
            connect()
          }, delay)
        }
      }

      ws.onerror = (err) => {
        console.error('🔌 WebSocket error:', err)
        error.value = 'WebSocket connection error'
      }

    } catch (err) {
      console.error('🔌 Failed to create WebSocket:', err)
      error.value = 'Failed to create WebSocket connection'
    }
  }

  const handleMessage = (message: any) => {
    switch (message.type) {
      case 'connected':
        console.log('✅ Connected to extraction updates:', message.message)
        break

      case 'subscribed':
        console.log('📡 Subscribed to channels:', message.channels)
        break

      case 'extraction_updated':
        console.log('🔄 Extraction updated:', message.extraction?.id, message.extraction?.status)
        handleExtractionUpdate(message.extraction)
        break

      case 'error':
        console.error('❌ WebSocket error:', message.message)
        error.value = message.message
        break

      case 'pong':
        // Handle pong response
        console.log('🏓 Pong received')
        break

      default:
        console.log('📨 WebSocket message:', message)
    }
  }

  const handleExtractionUpdate = (extraction: any) => {
    if (extraction) {
      // Apply patch to ResourcePool with the updated extraction
      applyPatch('extraction_updated', { extraction })
    }
  }

  const disconnect = () => {
    if (reconnectTimer) {
      clearTimeout(reconnectTimer)
      reconnectTimer = null
    }
    
    if (ws) {
      ws.close(1000, 'Manual disconnect')
      ws = null
    }
    
    isConnected.value = false
  }

  const send = (message: any) => {
    if (ws && ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify(message))
    } else {
      console.warn('⚠️ WebSocket not connected, cannot send message')
    }
  }

  // Auto-connect on mount
  onMounted(() => {
    connect()
  })

  // Clean up on unmount
  onUnmounted(() => {
    disconnect()
  })

  return {
    isConnected,
    error,
    connect,
    disconnect,
    send
  }
} 


================================================
FILE: src/lib/trpc.ts
================================================
import { createTRPCProxyClient, httpBatchLink } from '@trpc/client';
import type { AppRouter } from '../types/index.js';

// Create the tRPC client
export const trpc = createTRPCProxyClient<AppRouter>({
  links: [
    httpBatchLink({
      url: 'http://localhost:8000/trpc',
      headers() {
        return {
          // Add any auth headers here if needed
        };
      },
    }),
  ],
});

// Create a reactive wrapper for Vue
import { ref, reactive } from 'vue';

export function useTRPC() {
  const loading = ref(false);
  const error = ref<string | null>(null);

  const withLoading = async <T>(fn: () => Promise<T>): Promise<T> => {
    loading.value = true;
    error.value = null;
    try {
      const result = await fn();
      return result;
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'An error occurred';
      throw err;
    } finally {
      loading.value = false;
    }
  };

  return {
    trpc,
    loading,
    error,
    withLoading,
  };
} 


================================================
FILE: src/lib/utils.ts
================================================
import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}


================================================
FILE: src/services/index.ts
================================================
// New tRPC-based ResourcePool system
export * from './resourcePool.js';

// tRPC client utilities
export * from '../lib/trpc.js';

// Export resourcePool instance with common aliases for compatibility
export { resourcePool } from './resourcePool.js';

// Document streaming helper (for binary data that tRPC doesn't handle)
export const getDocumentStreamingUrl = (documentIdentifier: string): string => {
  // Construct the streaming URL based on backend API pattern
  const baseUrl = '/api/documents';
  return `${baseUrl}/${encodeURIComponent(documentIdentifier)}/stream`;
};




================================================
FILE: src/services/resourcePool.ts
================================================
import { ref, reactive, computed } from 'vue';
import { trpc } from '../lib/trpc.js';
import type { 
  Dataset, 
  Document, 
  ExtractionConfig, 
  Extraction, 
  GroundTruth 
} from '../types/index.js';

// Reactive state for all resources
const state = reactive({
  datasets: [] as Dataset[],
  documents: [] as Document[],
  extractionConfigs: [] as ExtractionConfig[],
  extractions: [] as Extraction[],
  groundTruths: [] as GroundTruth[],
  isBootstrapped: false,
  isLoading: false,
  error: null as string | null,
});

class ResourcePool {
  private static instance: ResourcePool;

  static getInstance(): ResourcePool {
    if (!ResourcePool.instance) {
      ResourcePool.instance = new ResourcePool();
    }
    return ResourcePool.instance;
  }

  // ============= BOOTSTRAP =============
  async bootstrap(): Promise<void> {
    if (state.isBootstrapped && !this.isEmpty()) {
      console.log('🎯 ResourcePool: Already bootstrapped, skipping');
      return;
    }

    state.isLoading = true;
    state.error = null;

    try {
      console.log('🚀 ResourcePool: Starting bootstrap...');
      
      const data = await trpc.resources.bootstrap.query();
      
      // Update reactive state
      state.datasets = data.datasets;
      state.documents = data.documents;
      state.extractionConfigs = data.extractionConfigs;
      state.extractions = data.extractions;
      state.groundTruths = data.groundTruths;
      state.isBootstrapped = true;

      console.log(`✅ ResourcePool: Bootstrap complete! Loaded ${data.datasets.length} datasets, ${data.documents.length} documents, ${data.extractionConfigs.length} configs, ${data.extractions.length} extractions, ${data.groundTruths.length} ground truths`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Bootstrap failed';
      state.error = errorMessage;
      console.error('❌ ResourcePool: Bootstrap failed:', error);
      throw error;
    } finally {
      state.isLoading = false;
    }
  }

  // ============= GETTERS =============
  
  // All datasets
  get datasets(): Dataset[] {
    return state.datasets;
  }

  // All documents
  get documents(): Document[] {
    return state.documents;
  }

  // Documents by dataset
  getDocumentsByDataset(datasetId: string): Document[] {
    return state.documents.filter(doc => doc.datasetId === datasetId);
  }

  // All extraction configs
  get extractionConfigs(): ExtractionConfig[] {
    return state.extractionConfigs;
  }

  // All extractions
  get extractions(): Extraction[] {
    return state.extractions;
  }

  // Extractions by document
  getExtractionsByDocument(documentId: string): Extraction[] {
    return state.extractions.filter(ext => ext.documentId === documentId);
  }

  // All ground truths
  get groundTruths(): GroundTruth[] {
    return state.groundTruths;
  }

  // Get individual resources by ID
  getDataset(id: string): Dataset | undefined {
    return state.datasets.find(d => d.id === id);
  }

  getDocument(id: string): Document | undefined {
    return state.documents.find(d => d.id === id);
  }

  getExtractionConfig(id: string): ExtractionConfig | undefined {
    return state.extractionConfigs.find(c => c.id === id);
  }

  getExtraction(id: string): Extraction | undefined {
    return state.extractions.find(e => e.id === id);
  }

  getGroundTruth(id: string): GroundTruth | undefined {
    return state.groundTruths.find(g => g.id === id);
  }

  // ============= MUTATIONS =============

  async createDataset(data: Omit<Dataset, 'id' | 'createdAt' | 'updatedAt'>): Promise<Dataset> {
    const newDataset = await trpc.resources.datasets.create.mutate(data);
    state.datasets.push(newDataset);
    console.log('✅ ResourcePool: Dataset created:', newDataset.id);
    return newDataset;
  }

  async updateDataset(id: string, data: Partial<Omit<Dataset, 'id' | 'createdAt' | 'updatedAt'>>): Promise<Dataset> {
    const updatedDataset = await trpc.resources.datasets.update.mutate({ id, data });
    const index = state.datasets.findIndex(d => d.id === id);
    if (index !== -1) {
      state.datasets[index] = updatedDataset;
    }
    console.log('✅ ResourcePool: Dataset updated:', id);
    return updatedDataset;
  }

  async deleteDataset(id: string): Promise<void> {
    await trpc.resources.datasets.delete.mutate({ id });
    const index = state.datasets.findIndex(d => d.id === id);
    if (index !== -1) {
      state.datasets.splice(index, 1);
    }
    // Also remove related documents
    state.documents = state.documents.filter(doc => doc.datasetId !== id);
    console.log('✅ ResourcePool: Dataset deleted:', id);
  }

  async createDocument(data: Omit<Document, 'id' | 'createdAt' | 'updatedAt'>): Promise<Document> {
    const newDocument = await trpc.resources.documents.create.mutate(data);
    state.documents.push(newDocument);
    console.log('✅ ResourcePool: Document created:', newDocument.id);
    return newDocument;
  }

  async deleteDocument(id: string): Promise<void> {
    await trpc.resources.documents.delete.mutate({ id });
    const index = state.documents.findIndex(d => d.id === id);
    if (index !== -1) {
      state.documents.splice(index, 1);
    }
    // Also remove related extractions and ground truths
    state.extractions = state.extractions.filter(ext => ext.documentId !== id);
    state.groundTruths = state.groundTruths.filter(gt => gt.documentId !== id);
    console.log('✅ ResourcePool: Document deleted:', id);
  }

  async createExtractionConfig(data: Omit<ExtractionConfig, 'id' | 'createdAt' | 'updatedAt'>): Promise<ExtractionConfig> {
    const newConfig = await trpc.resources.extractionConfigs.create.mutate(data);
    state.extractionConfigs.push(newConfig);
    console.log('✅ ResourcePool: ExtractionConfig created:', newConfig.id);
    return newConfig;
  }

  async deleteExtractionConfig(id: string): Promise<void> {
    await trpc.resources.extractionConfigs.delete.mutate({ id });
    const index = state.extractionConfigs.findIndex(c => c.id === id);
    if (index !== -1) {
      state.extractionConfigs.splice(index, 1);
    }
    // Also remove related extractions and ground truths
    state.extractions = state.extractions.filter(ext => ext.extractionConfigId !== id);
    state.groundTruths = state.groundTruths.filter(gt => gt.extractionConfigId !== id);
    console.log('✅ ResourcePool: ExtractionConfig deleted:', id);
  }

  async createExtraction(data: Omit<Extraction, 'id' | 'createdAt' | 'updatedAt'>): Promise<Extraction> {
    const newExtraction = await trpc.resources.extractions.create.mutate(data);
    state.extractions.push(newExtraction);
    console.log('✅ ResourcePool: Extraction created:', newExtraction.id);
    return newExtraction;
  }

  async createGroundTruth(data: Omit<GroundTruth, 'id' | 'createdAt' | 'updatedAt'>): Promise<GroundTruth> {
    const newGroundTruth = await trpc.resources.groundTruths.create.mutate(data);
    state.groundTruths.push(newGroundTruth);
    console.log('✅ ResourcePool: GroundTruth created:', newGroundTruth.id);
    return newGroundTruth;
  }

  // ============= UTILITIES =============

  isEmpty(): boolean {
    return state.datasets.length === 0 && 
           state.documents.length === 0 && 
           state.extractionConfigs.length === 0 &&
           state.extractions.length === 0 &&
           state.groundTruths.length === 0;
  }

  get status() {
    return {
      isBootstrapped: state.isBootstrapped,
      isLoading: state.isLoading,
      error: state.error,
      counts: {
        datasets: state.datasets.length,
        documents: state.documents.length,
        extractionConfigs: state.extractionConfigs.length,
        extractions: state.extractions.length,
        groundTruths: state.groundTruths.length,
      }
    };
  }

  async clearCache(): Promise<void> {
    await trpc.resources.clearCache.mutate();
    console.log('🗑️ ResourcePool: Backend cache cleared');
  }

  async getCacheStats(): Promise<{ size: number; keys: string[] }> {
    return await trpc.resources.cacheStats.query();
  }

  // Force refresh by clearing local state and re-bootstrapping
  async refresh(): Promise<void> {
    state.datasets = [];
    state.documents = [];
    state.extractionConfigs = [];
    state.extractions = [];
    state.groundTruths = [];
    state.isBootstrapped = false;
    await this.bootstrap();
  }
}

// Simple wrapper functions for async extraction functionality
export const triggerExtractionAsync = async (
  documentId: string, 
  extractionConfigId: string, 
  extractorModel: string
): Promise<any> => {
  try {
    console.log(`🚀 Triggering async extraction: ${documentId} -> ${extractorModel}`);
    
    const result = await trpc.resources.extractions.runAsync.mutate({
      documentId,
      extractionConfigId, 
      extractorModel
    });
    
    console.log('✅ Extraction queued via tRPC:', result);
    return result;
  } catch (error) {
    console.error('❌ Failed to trigger async extraction:', error);
    throw error;
  }
};

export const triggerBatchExtractionAsync = async (
  documentIds: string[], 
  extractionConfigId: string, 
  extractorModels: string[]
): Promise<any> => {
  try {
    console.log(`🚀 Triggering batch async extraction: ${documentIds.length} docs, ${extractorModels.length} extractors`);
    
    const result = await trpc.resources.extractions.runBatchAsync.mutate({
      documentIds,
      extractionConfigId,
      extractorModels
    });
    
    console.log('✅ Batch extraction queued via tRPC:', result);
    return result;
  } catch (error) {
    console.error('❌ Failed to trigger batch async extraction:', error);
    throw error;
  }
};

// Simple patch application function
export const applyPatch = (patchType: string, data: any): void => {
  console.log(`📨 ResourcePool: Applying patch ${patchType}:`, data);

  switch (patchType) {
    case 'extraction_completed':
    case 'extraction_created':
    case 'extraction_updated':
      if (data.extraction) {
        const extraction = data.extraction;
        const existingIndex = state.extractions.findIndex(e => e.id === extraction.id);
        
        if (existingIndex >= 0) {
          // Update existing extraction
          state.extractions[existingIndex] = extraction;
          console.log(`✅ Applied extraction update patch for ${extraction.id} (status: ${extraction.status})`);
        } else {
          // Add new extraction
          state.extractions.push(extraction);
          console.log(`✅ Applied extraction creation patch for ${extraction.id} (status: ${extraction.status})`);
        }
      }
      break;
      
    case 'dataset_updated':
      if (data.dataset) {
        const dataset = data.dataset;
        const index = state.datasets.findIndex(d => d.id === dataset.id);
        
        if (index >= 0) {
          state.datasets[index] = dataset;
          console.log(`✅ Applied dataset update patch for ${dataset.id}`);
        }
      }
      break;
      
    case 'document_updated':
      if (data.document) {
        const document = data.document;
        const index = state.documents.findIndex(d => d.id === document.id);
        
        if (index >= 0) {
          state.documents[index] = document;
          console.log(`✅ Applied document update patch for ${document.id}`);
        }
      }
      break;
      
    case 'ground_truth_updated':
      if (data.groundTruth) {
        const groundTruth = data.groundTruth;
        const index = state.groundTruths.findIndex(gt => gt.id === groundTruth.id);
        
        if (index >= 0) {
          state.groundTruths[index] = groundTruth;
        } else {
          state.groundTruths.push(groundTruth);
        }
        
        console.log(`✅ Applied ground truth update patch for ${groundTruth.id}`);
      }
      break;
      
    default:
      console.warn(`⚠️ Unknown patch type: ${patchType}`);
  }
};

// Export reactive state for components to use
export const useResourcePool = () => {
  return {
    // Reactive state
    datasets: computed(() => state.datasets),
    documents: computed(() => state.documents),
    extractionConfigs: computed(() => state.extractionConfigs),
    extractions: computed(() => state.extractions),
    groundTruths: computed(() => state.groundTruths),
    isBootstrapped: computed(() => state.isBootstrapped),
    isLoading: computed(() => state.isLoading),
    error: computed(() => state.error),
    
    // Methods
    bootstrap: () => resourcePool.bootstrap(),
    refresh: () => resourcePool.refresh(),
    
    // Getters
    getDataset: (id: string): Dataset | undefined => resourcePool.getDataset(id),
    getDocument: (id: string) => resourcePool.getDocument(id),
    getExtractionConfig: (id: string) => resourcePool.getExtractionConfig(id),
    getExtraction: (id: string) => resourcePool.getExtraction(id),
    getGroundTruth: (id: string) => resourcePool.getGroundTruth(id),
    
    getDocumentsByDataset: (datasetId: string): Document[] => resourcePool.getDocumentsByDataset(datasetId),
    getExtractionsByDocument: (documentId: string): Extraction[] => resourcePool.getExtractionsByDocument(documentId),
    
    // Mutations
    createDataset: (data: Omit<Dataset, 'id' | 'createdAt' | 'updatedAt'>) => resourcePool.createDataset(data),
    updateDataset: (id: string, data: Partial<Omit<Dataset, 'id' | 'createdAt' | 'updatedAt'>>) => resourcePool.updateDataset(id, data),
    deleteDataset: (id: string) => resourcePool.deleteDataset(id),
    
    createDocument: (data: Omit<Document, 'id' | 'createdAt' | 'updatedAt'>) => resourcePool.createDocument(data),
    deleteDocument: (id: string) => resourcePool.deleteDocument(id),
    
    createExtractionConfig: (data: Omit<ExtractionConfig, 'id' | 'createdAt' | 'updatedAt'>) => resourcePool.createExtractionConfig(data),
    deleteExtractionConfig: (id: string) => resourcePool.deleteExtractionConfig(id),
    
    applyPatch: (patchType: string, data: any) => resourcePool.applyPatch(patchType, data),

  };
};

// Add patch application methods to ResourcePool
class ResourcePoolExtended extends ResourcePool {
  // NEW: Trigger async extraction (calls tRPC)
  async triggerExtractionAsync(documentId: string, extractionConfigId: string, extractorModel: string): Promise<any> {
    try {
      console.log(`🚀 Triggering async extraction: ${documentId} -> ${extractorModel}`);
      
      const result = await trpc.resources.extractions.runAsync.mutate({
        documentId,
        extractionConfigId, 
        extractorModel
      });
      
      console.log('✅ Extraction queued via tRPC:', result);
      return result;
    } catch (error) {
      console.error('❌ Failed to trigger async extraction:', error);
      throw error;
    }
  }

  // NEW: Trigger batch async extractions (calls tRPC)
  async triggerBatchExtractionAsync(documentIds: string[], extractionConfigId: string, extractorModels: string[]): Promise<any> {
    try {
      console.log(`🚀 Triggering batch async extraction: ${documentIds.length} docs, ${extractorModels.length} extractors`);
      
      const result = await trpc.resources.extractions.runBatchAsync.mutate({
        documentIds,
        extractionConfigId,
        extractorModels
      });
      
      console.log('✅ Batch extraction queued via tRPC:', result);
      return result;
    } catch (error) {
      console.error('❌ Failed to trigger batch async extraction:', error);
      throw error;
    }
  }

  // NEW: Apply data patches from WebSocket events
  applyPatch(patchType: string, data: any): void {
    console.log(`📨 ResourcePool: Applying patch ${patchType}:`, data);

    switch (patchType) {
      case 'extraction_completed':
      case 'extraction_created':
      case 'extraction_updated':
        if (data.extraction) {
          const extraction = data.extraction;
          const existingIndex = state.extractions.findIndex(e => e.id === extraction.id);
          
          if (existingIndex >= 0) {
            // Update existing extraction
            state.extractions[existingIndex] = extraction;
          } else {
            // Add new extraction
            state.extractions.push(extraction);
          }
          
          console.log(`✅ Applied extraction patch for ${extraction.id} (${patchType})`);
        }
        break;
        
      case 'dataset_updated':
        if (data.dataset) {
          const dataset = data.dataset;
          const index = state.datasets.findIndex(d => d.id === dataset.id);
          
          if (index >= 0) {
            state.datasets[index] = dataset;
            console.log(`✅ Applied dataset update patch for ${dataset.id}`);
          }
        }
        break;
        
      case 'document_updated':
        if (data.document) {
          const document = data.document;
          const index = state.documents.findIndex(d => d.id === document.id);
          
          if (index >= 0) {
            state.documents[index] = document;
            console.log(`✅ Applied document update patch for ${document.id}`);
          }
        }
        break;
        
      case 'ground_truth_updated':
        if (data.groundTruth) {
          const groundTruth = data.groundTruth;
          const index = state.groundTruths.findIndex(gt => gt.id === groundTruth.id);
          
          if (index >= 0) {
            state.groundTruths[index] = groundTruth;
          } else {
            state.groundTruths.push(groundTruth);
          }
          
          console.log(`✅ Applied ground truth update patch for ${groundTruth.id}`);
        }
        break;
        
      default:
        console.warn(`⚠️ Unknown patch type: ${patchType}`);
    }
  }
}

// Use the extended ResourcePool
export const resourcePool = ResourcePoolExtended.getInstance(); 



================================================
FILE: src/types/index.ts
================================================

// Import types from backend tRPC router (these will be auto-generated)
export type { AppRouter } from '../../../backend/src/routers/index.js';

// Re-export the Resource types from backend
export type {
  Resource,
  Dataset,
  Document,
  ExtractionConfig,
  ExtractionField,
  Extraction,
  GroundTruth,
  Extractor,
  ExtractionConfigData,
  ExtractedValue
} from '../../../backend/src/types/resource.js';



================================================
FILE: src/utils/correctness.ts
================================================
/**
 * Calculate correctness score by comparing extracted value with ground truth
 */
export function calculateCorrectness(extractedValue: any, groundTruth: any, fieldType: string): number {
  // Normalize null-like values
  const normalizeNullValue = (value: any): any => {
    if (value === null || value === undefined) return null;
    if (typeof value === 'string') {
      const trimmed = value.trim().toLowerCase();
      // Treat common "null" representations as null
      if (trimmed === 'n/a' || trimmed === 'null' || trimmed === 'none' || trimmed === '' || trimmed === 'not available') {
        return null;
      }
    }
    return value;
  };

  const normalizedExtracted = normalizeNullValue(extractedValue);
  const normalizedGroundTruth = normalizeNullValue(groundTruth);

  // Both null - perfect match
  if (normalizedExtracted === null && normalizedGroundTruth === null) {
    return 100;
  }

  // One is null, the other isn't - no match
  if (normalizedExtracted === null || normalizedGroundTruth === null) {
    return 0;
  }

  // Convert both values to strings for comparison (handles edge cases)
  const extracted = String(normalizedExtracted).trim().toLowerCase()
  const truth = String(normalizedGroundTruth).trim().toLowerCase()

  // Exact match gets 100%
  if (extracted === truth) {
    return 100
  }

  // Field type specific logic
  switch (fieldType) {
    case 'number':
      return calculateNumericCorrectness(normalizedExtracted, normalizedGroundTruth);
    
    case 'date':
      return calculateDateCorrectness(extracted, truth);
    
    case 'text':
    default:
      return calculateTextCorrectness(extracted, truth);
  }
}

/**
 * Calculate correctness for numeric values with tolerance
 */
function calculateNumericCorrectness(extracted: any, truth: any): number {
  const extractedNum = parseFloat(String(extracted))
  const truthNum = parseFloat(String(truth))

  if (isNaN(extractedNum) || isNaN(truthNum)) {
    return 0
  }

  if (extractedNum === truthNum) {
    return 100
  }

  // Calculate percentage difference
  const difference = Math.abs(extractedNum - truthNum)
  const average = (Math.abs(extractedNum) + Math.abs(truthNum)) / 2
  
  if (average === 0) {
    return extractedNum === truthNum ? 100 : 0
  }

  const percentageDiff = (difference / average) * 100

  // Score based on how close the values are
  if (percentageDiff <= 1) return 95  // Within 1%
  if (percentageDiff <= 5) return 85  // Within 5%
  if (percentageDiff <= 10) return 70 // Within 10%
  if (percentageDiff <= 20) return 50 // Within 20%
  if (percentageDiff <= 50) return 25 // Within 50%
  
  return 0 // More than 50% difference
}

/**
 * Calculate correctness for date values
 */
function calculateDateCorrectness(extracted: string, truth: string): number {
  // Try to parse as dates
  const extractedDate = new Date(extracted)
  const truthDate = new Date(truth)

  if (isNaN(extractedDate.getTime()) || isNaN(truthDate.getTime())) {
    // Fall back to string comparison if not valid dates
    return calculateTextCorrectness(extracted, truth)
  }

  // Check if same date
  if (extractedDate.getTime() === truthDate.getTime()) {
    return 100
  }

  // Check if same day (ignore time)
  if (extractedDate.toDateString() === truthDate.toDateString()) {
    return 90
  }

  // Calculate day difference
  const dayDiff = Math.abs(extractedDate.getTime() - truthDate.getTime()) / (1000 * 60 * 60 * 24)
  
  if (dayDiff <= 1) return 80  // Within 1 day
  if (dayDiff <= 7) return 60  // Within 1 week
  if (dayDiff <= 30) return 40 // Within 1 month
  
  return 0 // More than 1 month difference
}

/**
 * Calculate correctness for text values using similarity
 */
function calculateTextCorrectness(extracted: string, truth: string): number {
  if (extracted === truth) {
    return 100
  }

  // Check if one contains the other
  if (extracted.includes(truth) || truth.includes(extracted)) {
    return 80
  }

  // Calculate Levenshtein distance for similarity
  const similarity = calculateStringSimilarity(extracted, truth)
  
  if (similarity >= 0.9) return 85
  if (similarity >= 0.8) return 70
  if (similarity >= 0.6) return 50
  if (similarity >= 0.4) return 30
  
  return 0
}

/**
 * Calculate string similarity using Levenshtein distance
 */
function calculateStringSimilarity(str1: string, str2: string): number {
  const len1 = str1.length
  const len2 = str2.length

  if (len1 === 0) return len2 === 0 ? 1 : 0
  if (len2 === 0) return 0

  const matrix = Array(len2 + 1).fill(null).map(() => Array(len1 + 1).fill(null))

  for (let i = 0; i <= len1; i++) matrix[0][i] = i
  for (let j = 0; j <= len2; j++) matrix[j][0] = j

  for (let j = 1; j <= len2; j++) {
    for (let i = 1; i <= len1; i++) {
      const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1
      matrix[j][i] = Math.min(
        matrix[j][i - 1] + 1,     // deletion
        matrix[j - 1][i] + 1,     // insertion
        matrix[j - 1][i - 1] + indicator // substitution
      )
    }
  }

  const distance = matrix[len2][len1]
  const maxLen = Math.max(len1, len2)
  
  return maxLen === 0 ? 1 : (maxLen - distance) / maxLen
} 


================================================
FILE: src/utils/errorUtils.ts
================================================
export interface ErrorInfo {
  type: 'network' | 'not_found' | 'server' | 'unknown';
  message: string;
  canRetry: boolean;
}

export function analyzeError(error: any): ErrorInfo {
  // Check if it's a network connectivity error
  if (error?.code === 'ECONNREFUSED' || 
      error?.code === 'NETWORK_ERROR' ||
      error?.name === 'NetworkError' ||
      error?.message?.includes('fetch') ||
      error?.message?.includes('Network request failed') ||
      error?.message?.includes('Failed to fetch') ||
      (error?.response === undefined && error?.request !== undefined)) {
    return {
      type: 'network',
      message: 'Unable to connect to the backend server. Please check if the server is running.',
      canRetry: true
    };
  }

  // Check if it's a 404 Not Found error
  if (error?.response?.status === 404 || 
      error?.status === 404 ||
      error?.message?.includes('404') ||
      error?.message?.includes('not found')) {
    return {
      type: 'not_found',
      message: 'The requested resource could not be found.',
      canRetry: false
    };
  }

  // Check if it's a server error (5xx)
  if (error?.response?.status >= 500 || 
      error?.status >= 500) {
    return {
      type: 'server',
      message: 'Server error occurred. Please try again later.',
      canRetry: true
    };
  }

  // Check for common network-related error messages
  if (error?.message) {
    const message = error.message.toLowerCase();
    if (message.includes('connection') || 
        message.includes('timeout') ||
        message.includes('network') ||
        message.includes('refused') ||
        message.includes('unreachable')) {
      return {
        type: 'network',
        message: 'Unable to connect to the backend server. Please check if the server is running.',
        canRetry: true
      };
    }
  }

  // Default to unknown error
  return {
    type: 'unknown',
    message: error?.message || 'An unexpected error occurred.',
    canRetry: true
  };
}

export function isNetworkError(error: any): boolean {
  return analyzeError(error).type === 'network';
}

export function isNotFoundError(error: any): boolean {
  return analyzeError(error).type === 'not_found';
} 


================================================
FILE: src/views/DatasetDetail.vue
================================================
<template>
  <div v-if="isLoading" class="flex items-center justify-center min-h-96">
    <div class="text-center">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
      <p class="text-muted-foreground">Loading dataset details...</p>
    </div>
  </div>
  
  <div v-else-if="error" class="text-center py-12">
    <div class="text-destructive mb-4">
      <svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
      </svg>
    </div>
    <h2 class="text-xl font-semibold text-foreground mb-2">Error Loading Dataset</h2>
    <p class="text-muted-foreground mb-4">{{ error }}</p>
    <button 
      @click="router.push('/')"
      class="px-4 py-2 bg-primary text-primary-foreground rounded hover:bg-primary/90 transition-colors"
    >
      Back to Datasets
    </button>
  </div>

  <div v-else-if="dataset && extractionConfig" class="space-y-8">
    <!-- Header Section -->
    <div class="border-b border-border pb-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-3xl font-bold text-foreground">{{ dataset.name }}</h1>
          <p class="text-muted-foreground mt-2 text-lg">{{ dataset.description || 'Dataset configuration and document management' }}</p>
        </div>
        <div class="flex items-center gap-3">
          <div v-if="isSaving" class="flex items-center gap-2 text-sm text-muted-foreground">
            <div class="animate-spin rounded-full h-3 w-3 border-b-2 border-primary"></div>
            Saving...
          </div>
          <div class="text-sm text-muted-foreground">
            {{ datasetDocuments.length }} documents
          </div>
        </div>
      </div>
    </div>

    <!-- Tab Navigation -->
    <div class="border-b border-border">
      <nav class="-mb-px flex space-x-8">
        <button
          @click="activeTab = 'documents'"
          :class="[
            'py-2 px-1 border-b-2 font-medium text-sm transition-colors',
            activeTab === 'documents'
              ? 'border-primary text-primary'
              : 'border-transparent text-muted-foreground hover:text-foreground hover:border-border'
          ]"
        >
          <div class="flex items-center gap-2">
            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            Documents
            <span v-if="datasetDocuments.length > 0" class="bg-muted text-muted-foreground px-2 py-0.5 rounded-full text-xs">
              {{ datasetDocuments.length }}
            </span>
          </div>
        </button>
        <button
          @click="activeTab = 'configuration'"
          :class="[
            'py-2 px-1 border-b-2 font-medium text-sm transition-colors',
            activeTab === 'configuration'
              ? 'border-primary text-primary'
              : 'border-transparent text-muted-foreground hover:text-foreground hover:border-border'
          ]"
        >
          <div class="flex items-center gap-2">
            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            </svg>
            Configuration
            <div v-if="saveStatus" :class="[
              'w-2 h-2 rounded-full',
              saveStatus === 'saving' ? 'bg-yellow-500 animate-pulse' : 
              saveStatus === 'saved' ? 'bg-green-500' : 
              saveStatus === 'error' ? 'bg-red-500' : 'bg-gray-300'
            ]" :title="getSaveStatusText()"></div>
          </div>
        </button>
      </nav>
    </div>

    <!-- Tab Content -->
    <div class="tab-content">
      <!-- Documents Tab -->
      <div v-show="activeTab === 'documents'" class="tab-panel">
        <DocumentsDataTable
          :documents="datasetDocuments"
          :config="extractionConfig as any"
          :available-extractors="availableExtractors"
          :extracting-docs="extractingDocs"
          :dataset-id="dataset.id"
          :is-loading-docs="isLoadingDocs"
          :extracted-count="extractedCount"
          :extraction-tasks-to-start="extractionTasksToStart"
          :approval-summary="approvalSummary"
          :run-batch-extraction="runBatchExtraction"
          :get-approval-status-text="getApprovalStatusText"
          :get-document-accuracies="getDocumentAccuracies"
          :has-extractor-run="hasExtractorRun"
        />
      </div>

      <!-- Configuration Tab -->
      <div v-show="activeTab === 'configuration'" class="tab-panel">
        <div class="grid grid-cols-1 xl:grid-cols-2 gap-8">
          <!-- Dataset Information -->
          <div class="space-y-6">
            <div class="bg-card border border-border rounded-lg shadow-sm">
              <div class="p-6 border-b border-border">
                <h2 class="text-xl font-semibold text-foreground">Dataset Information</h2>
              </div>
              <div class="p-6 space-y-4">
                <div>
                  <label class="block text-sm font-medium text-foreground mb-1">Name</label>
                  <input 
                    v-model="editableDataset.name" 
                    type="text" 
                    @input="debouncedSave"
                    class="w-full px-3 py-2 border border-input rounded-md bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-input"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-foreground mb-1">Description</label>
                  <textarea 
                    v-model="editableDataset.description" 
                    rows="3"
                    @input="debouncedSave"
                    class="w-full px-3 py-2 border border-input rounded-md bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-input resize-vertical"
                  ></textarea>
                </div>
                <div>
                  <label class="block text-sm font-medium text-foreground mb-1">SQL Query</label>
                  <textarea 
                    v-model="editableDataset.query" 
                    rows="6"
                    @input="debouncedSave"
                    class="w-full px-3 py-2 border border-input rounded-md bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-input font-mono text-sm resize-vertical"
                    placeholder="SELECT * FROM documents WHERE..."
                  ></textarea>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Extraction Configuration -->
          <div class="space-y-6">
            <!-- Fields Section -->
            <div class="bg-card border border-border rounded-lg shadow-sm">
              <div class="p-6 border-b border-border">
                <div class="flex items-center justify-between">
                  <h2 class="text-xl font-semibold text-foreground">Extraction Fields</h2>
                  <button 
                    @click="addField" 
                    class="px-3 py-2 text-sm bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors flex items-center gap-2"
                  >
                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Add Field
                  </button>
                </div>
              </div>
              <div class="p-6">
                <div v-if="editableConfig.data.fields.length === 0" class="text-center py-8 text-muted-foreground">
                  <svg class="h-12 w-12 mx-auto mb-3 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                  </svg>
                  <p>No fields configured yet</p>
                  <p class="text-sm mt-1">Add fields to define what data to extract</p>
                </div>
                <div v-else class="space-y-4">
                  <div 
                    v-for="(field, index) in editableConfig.data.fields" 
                    :key="index" 
                    class="bg-muted/30 border border-border rounded-lg p-4"
                  >
                    <div class="flex items-start justify-between mb-3">
                      <div class="flex-1 space-y-3">
                        <div class="grid grid-cols-2 gap-3">
                          <div>
                            <label class="block text-sm font-medium text-foreground mb-1">Field Name</label>
                            <input 
                              v-model="field.name" 
                              type="text" 
                              @input="debouncedSave"
                              class="w-full px-3 py-2 border border-input rounded-md bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-input"
                            />
                          </div>
                          <div>
                            <label class="block text-sm font-medium text-foreground mb-1">Type</label>
                            <select 
                              v-model="field.fieldType" 
                              @change="debouncedSave"
                              class="w-full px-3 py-2 border border-input rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-input"
                            >
                              <option value="text">Text</option>
                              <option value="number">Number</option>
                              <option value="date">Date</option>
                              <option value="boolean">Boolean</option>
                              <option value="email">Email</option>
                              <option value="phone">Phone</option>
                              <option value="address">Address</option>
                            </select>
                          </div>
                        </div>
                        <div>
                          <label class="block text-sm font-medium text-foreground mb-1">Description</label>
                          <textarea 
                            v-model="field.description" 
                            rows="3"
                            @input="debouncedSave"
                            class="w-full px-3 py-2 border border-input rounded-md bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-input resize-vertical"
                          ></textarea>
                        </div>
                      </div>
                      <button 
                        @click="removeField(index)" 
                        class="ml-4 p-2 text-destructive hover:bg-destructive/10 rounded-md transition-colors"
                      >
                        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Extractors Section -->
            <div class="bg-card border border-border rounded-lg shadow-sm">
              <div class="p-6 border-b border-border">
                <div class="flex items-center justify-between">
                  <h2 class="text-xl font-semibold text-foreground">Extractors</h2>
                  <button 
                    @click="addExtractor" 
                    class="px-3 py-2 text-sm bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors flex items-center gap-2"
                  >
                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Add Extractor
                  </button>
                </div>
              </div>
              <div class="p-6">
                <div v-if="editableConfig.data.extractors.length === 0" class="text-center py-8 text-muted-foreground">
                  <svg class="h-12 w-12 mx-auto mb-3 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                  </svg>
                  <p>No extractors configured yet</p>
                  <p class="text-sm mt-1">Add extractors to define how data should be extracted</p>
                </div>
                <div v-else class="space-y-4">
                  <div 
                    v-for="(extractor, index) in editableConfig.data.extractors" 
                    :key="index" 
                    class="bg-muted/30 border border-border rounded-lg p-4"
                  >
                    <div class="flex items-start justify-between mb-4">
                      <div class="flex-1 space-y-3">
                        <div class="grid grid-cols-2 gap-3">
                          <div>
                            <label class="block text-sm font-medium text-foreground mb-1">Provider</label>
                            <select 
                              v-model="extractor.provider" 
                              @change="debouncedSave"
                              class="w-full px-3 py-2 border border-input rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-input"
                            >
                              <option value="google">Google</option>
                              <option value="mistral">Mistral</option>
                              <option value="anthropic">Anthropic</option>
                              <option value="openai">OpenAI</option>
                            </select>
                          </div>
                          <div>
                            <label class="block text-sm font-medium text-foreground mb-1">Model</label>
                            <input 
                              v-model="extractor.model" 
                              type="text" 
                              @input="debouncedSave"
                              class="w-full px-3 py-2 border border-input rounded-md bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-input"
                            />
                          </div>
                        </div>
                        <div>
                          <label class="block text-sm font-medium text-foreground mb-1">Prompt</label>
                          <textarea 
                            v-model="extractor.prompt" 
                            rows="6"
                            @input="debouncedSave"
                            class="w-full px-3 py-2 border border-input rounded-md bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-input resize-vertical"
                          ></textarea>
                        </div>
                      </div>
                      <button 
                        @click="removeExtractor(index)" 
                        class="ml-4 p-2 text-destructive hover:bg-destructive/10 rounded-md transition-colors"
                      >
                        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div v-else class="text-center py-12">
    <div class="text-muted-foreground mb-4">
      <svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
      </svg>
    </div>
    <h3 class="text-lg font-medium text-foreground mb-2">Dataset not found</h3>
    <p class="text-muted-foreground mb-4">The requested dataset could not be found.</p>
    <button 
      @click="router.push('/')"
      class="px-4 py-2 bg-primary text-primary-foreground rounded hover:bg-primary/90 transition-colors"
    >
      Back to Datasets
    </button>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useResourcePool } from '../services/resourcePool.js';
import type { Dataset, ExtractionConfig } from '../types/index.js';
import DocumentsDataTable from '@/components/DocumentsDataTable.vue';

const route = useRoute();
const router = useRouter();

// Get ResourcePool composable
const {
  datasets,
  extractionConfigs,
  documents,
  extractions,
  isBootstrapped,
  isLoading: poolIsLoading,
  error: poolError,
  getDocumentsByDataset,
  getExtractionsByDocument,
  updateDataset,
  getDataset,
  getExtractionConfig
} = useResourcePool();

// Component state
const activeTab = ref<'documents' | 'configuration'>('documents');
const isSaving = ref(false);
const saveStatus = ref<'saving' | 'saved' | 'error' | null>(null);
const saveTimeout = ref<number | null>(null);

// Get dataset and config from the pool
const datasetId = computed(() => route.params.id as string);

const dataset = computed(() => {
  if (!isBootstrapped.value) return null;
  return getDataset(datasetId.value);
});

const extractionConfig = computed(() => {
  if (!isBootstrapped.value || !dataset.value?.extractionConfigId) return null;
  return getExtractionConfig(dataset.value.extractionConfigId);
});

const datasetDocuments = computed<Document[]>(() => {
  if (!isBootstrapped.value) return [];
  return getDocumentsByDataset(datasetId.value);
});

const getSaveStatusText = () => {
  switch (saveStatus.value) {
    case 'saving': return 'Saving changes...';
    case 'saved': return 'All changes saved';
    case 'error': return 'Error saving changes';
    default: return '';
  }
};

// Editable copies of data
const editableDataset = ref<Dataset>({
  id: '',
  name: '',
  description: '',
  query: '',
  version: 1,
  documentCount: 0,
  status: 'ready',
  createdAt: '',
  updatedAt: ''
});

const editableConfig = ref<ExtractionConfig>({
  id: '',
  data: {
    fields: [],
    extractors: []
  },
  version: 1,
  createdAt: '',
  updatedAt: ''
});

// Loading and error state
const isLoading = computed(() => {
  return !isBootstrapped.value || poolIsLoading.value;
});

const error = computed(() => {
  if (poolError.value) return poolError.value;
  if (isBootstrapped.value && !dataset.value) return 'Dataset not found';
  if (isBootstrapped.value && dataset.value && !extractionConfig.value) return 'Extraction configuration not found';
  return null;
});

// Initialize editable data when resource pool data changes
watch([dataset, extractionConfig], ([newDataset, newConfig]) => {
  if (newDataset) {
    editableDataset.value = { ...newDataset };
  }
  if (newConfig) {
    editableConfig.value = JSON.parse(JSON.stringify(newConfig));
  }
}, { immediate: true });

// Debounced save function
const debouncedSave = () => {
  // Clear existing timeout
  if (saveTimeout.value) {
    clearTimeout(saveTimeout.value);
  }

  // Set new timeout
  saveTimeout.value = window.setTimeout(async () => {
    await saveChanges();
  }, 1000); // 1 second debounce
};

const saveChanges = async () => {
  if (!dataset.value || !extractionConfig.value) return;

  isSaving.value = true;
  saveStatus.value = 'saving';

  try {
    // Save dataset changes
    await updateDataset(dataset.value.id, {
      name: editableDataset.value.name,
      description: editableDataset.value.description,
      query: editableDataset.value.query,
    });

    // Save config changes - we'd need to implement updateExtractionConfig in ResourcePool
    // For now, we'll create a new config (this would need to be properly implemented)
    // await updateExtractionConfig(extractionConfig.value.id, editableConfig.value.data);

    saveStatus.value = 'saved';
    console.log('✅ Changes saved successfully');

    // Clear saved status after 3 seconds
    setTimeout(() => {
      if (saveStatus.value === 'saved') {
        saveStatus.value = null;
      }
    }, 3000);
  } catch (error) {
    console.error('❌ Save failed:', error);
    saveStatus.value = 'error';

    // Clear error status after 5 seconds
    setTimeout(() => {
      if (saveStatus.value === 'error') {
        saveStatus.value = null;
      }
    }, 5000);
  } finally {
    isSaving.value = false;
  }
};

// Field operations
const addField = () => {
  editableConfig.value.data.fields.push({
    name: 'New Field',
    description: '',
    fieldType: 'text',
    required: false
  });
  debouncedSave();
};

const removeField = (index: number) => {
  editableConfig.value.data.fields.splice(index, 1);
  debouncedSave();
};

// Extractor operations
const addExtractor = () => {
  editableConfig.value.data.extractors.push({
    provider: 'google',
    model: 'gemini-2.5-pro',
    prompt: 'You are a document extraction expert. Extract the specified JSON fields from the document if possible. If a field does not match the exact description, return null for that field.'
  });
  debouncedSave();
};

const removeExtractor = (index: number) => {
  editableConfig.value.data.extractors.splice(index, 1);
  debouncedSave();
};

// Initialize tab from URL
onMounted(() => {
  const urlTab = route.query.tab as string;
  if (urlTab && ['documents', 'configuration'].includes(urlTab)) {
    activeTab.value = urlTab as 'documents' | 'configuration';
  }
});

// Computed properties for dirty state
const isConfigDirty = computed(() => {
  if (!extractionConfig.value || !editableConfig.value) return false;
  return JSON.stringify(extractionConfig.value) !== JSON.stringify(editableConfig.value);
});

const isDatasetDirty = computed(() => {
  if (!dataset.value || !editableDataset.value) return false;
  return JSON.stringify(dataset.value) !== JSON.stringify(editableDataset.value);
});

// Watch for changes and save
watch([editableDataset, editableConfig], () => {
  if (isConfigDirty.value || isDatasetDirty.value) {
    debouncedSave();
  }
}, { deep: true });

// Watch for tab changes and update URL
watch(activeTab, (newTab) => {
  router.replace({ query: { ...route.query, tab: newTab } });
});

// Watch for URL tab changes
watch(() => route.query.tab, (newTab) => {
  if (newTab && ['documents', 'configuration'].includes(newTab as string)) {
    activeTab.value = newTab as 'documents' | 'configuration';
  }
});

// Additional properties and functions needed for DocumentsDataTable
const availableExtractors = ref<string[]>(['gemini', 'mistral']);
const extractingDocs = ref<Set<string>>(new Set());
const isLoadingDocs = ref(false);
const groundTruth = ref({});

// Computed properties for DocumentsDataTable
const extractedCount = computed(() => {
  return datasetDocuments.value.filter(doc => 
    getExtractionsByDocument(doc.id).length > 0
  ).length;
});

const extractionTasksToStart = computed(() => {
  return datasetDocuments.value.filter(doc => 
    getExtractionsByDocument(doc.id).length === 0
  ).length;
});

const approvalSummary = computed(() => {
  let approved = 0;
  let pending = 0;
  let notReviewed = 0;
  
  datasetDocuments.value.forEach(doc => {
    if (doc.isApproved === true) {
      approved++;
    } else if (doc.isApproved === false) {
      pending++;
    } else {
      notReviewed++;
    }
  });
  
  return { approved, pending, notReviewed };
});

// Functions needed for DocumentsDataTable
const runBatchExtraction = (includeCompleted: boolean) => {
  console.log('Running batch extraction:', includeCompleted);
  // Implementation would go here
};

const getApprovalStatusText = (docId: string): string => {
  const doc = datasetDocuments.value.find(d => d.id === docId);
  if (!doc) return 'Unknown';
  
  if (doc.isApproved === true) return 'Approved';
  if (doc.isApproved === false) return 'Pending';
  return 'Not Reviewed';
};

const getDocumentAccuracies = (docId: string) => {
  // Mock implementation - would calculate actual accuracies
  const extractions = getExtractionsByDocument(docId);
  return extractions.map(extraction => ({
    extractor: extraction.model || 'unknown',
    accuracy: Math.floor(Math.random() * 100) // Mock accuracy
  }));
};

const hasExtractorRun = (docId: string, extractor: string): boolean => {
  const extractions = getExtractionsByDocument(docId);
  return extractions.some(extraction => extraction.model === extractor);
};
</script>



================================================
FILE: src/views/DatasetList.vue
================================================
<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Datasets</h1>
        <p class="text-gray-600">Manage your document datasets and extraction configurations</p>
      </div>
      <button
        @click="showCreateModal = true"
        class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
      >
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
        </svg>
        <span>Create Dataset</span>
      </button>
    </div>

    <!-- Loading state -->
    <div v-if="isLoading" class="text-center py-12">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
      <p class="text-gray-600">Loading datasets...</p>
    </div>

    <!-- Error state -->
    <div v-else-if="error" class="bg-red-50 border border-red-200 rounded-lg p-4">
      <div class="flex items-center space-x-2">
        <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <span class="text-red-800 font-medium">Failed to load datasets</span>
      </div>
      <p class="text-red-700 mt-1">{{ error }}</p>
      <button 
        @click="refresh"
        class="mt-2 px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700 transition-colors"
      >
        Retry
      </button>
    </div>

    <!-- Empty state -->
    <div v-else-if="datasets.length === 0" class="text-center py-12 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
      <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
      </svg>
      <h3 class="text-lg font-medium text-gray-900 mb-2">No datasets found</h3>
      <p class="text-gray-600 mb-4">Get started by creating your first dataset</p>
      <button
        @click="showCreateModal = true"
        class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
      >
        Create Dataset
      </button>
    </div>

    <!-- Dataset grid -->
    <div v-else class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      <div
        v-for="dataset in datasets"
        :key="dataset.id"
        class="bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow cursor-pointer"
        @click="navigateToDataset(dataset.id)"
      >
        <div class="p-6">
          <!-- Dataset header -->
          <div class="flex justify-between items-start mb-4">
            <div class="flex-1">
              <h3 class="text-lg font-semibold text-gray-900 mb-1">{{ dataset.name }}</h3>
              <p v-if="dataset.description" class="text-gray-600 text-sm line-clamp-2">{{ dataset.description }}</p>
            </div>
            <div class="flex items-center space-x-2 ml-4">
              <!-- Status badge -->
              <span 
                class="px-2 py-1 text-xs font-medium rounded-full"
                :class="{
                  'bg-green-100 text-green-800': dataset.status === 'ready',
                  'bg-yellow-100 text-yellow-800': dataset.status === 'syncing',
                  'bg-red-100 text-red-800': dataset.status === 'error'
                }"
              >
                {{ dataset.status }}
              </span>
              
              <!-- Actions dropdown -->
              <div class="relative">
                <button
                  @click.stop="toggleDropdown(dataset.id)"
                  class="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                  </svg>
                </button>
                
                <div
                  v-if="activeDropdown === dataset.id"
                  class="absolute right-0 mt-1 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10"
                >
                  <div class="py-1">
                    <button
                      @click.stop="editDataset(dataset)"
                      class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                    >
                      Edit Dataset
                    </button>
                    <button
                      @click.stop="confirmDelete(dataset)"
                      class="block w-full text-left px-4 py-2 text-sm text-red-700 hover:bg-red-50"
                    >
                      Delete Dataset
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Dataset stats -->
          <div class="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span class="text-gray-500">Documents:</span>
              <span class="font-medium ml-1">{{ getDocumentCount(dataset.id) }}</span>
            </div>
            <div>
              <span class="text-gray-500">Version:</span>
              <span class="font-medium ml-1">{{ dataset.version }}</span>
            </div>
          </div>

          <!-- Creation date -->
          <div class="mt-4 pt-4 border-t border-gray-100">
            <span class="text-xs text-gray-500">
              Created {{ formatDate(dataset.createdAt) }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Create/Edit Modal -->
    <div v-if="showCreateModal || editingDataset" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 w-full max-w-md mx-4">
        <h2 class="text-xl font-semibold mb-4">
          {{ editingDataset ? 'Edit Dataset' : 'Create Dataset' }}
        </h2>
        
        <form @submit.prevent="saveDataset">
          <div class="space-y-4">
            <div>
              <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Name</label>
              <input
                id="name"
                v-model="datasetForm.name"
                type="text"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter dataset name"
              >
            </div>
            
            <div>
              <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
              <textarea
                id="description"
                v-model="datasetForm.description"
                rows="3"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter dataset description"
              ></textarea>
            </div>
            
            <div>
              <label for="query" class="block text-sm font-medium text-gray-700 mb-1">Query</label>
              <textarea
                id="query"
                v-model="datasetForm.query"
                rows="4"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
                placeholder="SELECT * FROM documents WHERE..."
              ></textarea>
            </div>
          </div>
          
          <div class="flex justify-end space-x-3 mt-6">
            <button
              type="button"
              @click="cancelEdit"
              class="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              :disabled="isSubmitting"
              class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50"
            >
              {{ isSubmitting ? 'Saving...' : (editingDataset ? 'Update' : 'Create') }}
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- Delete confirmation modal -->
    <div v-if="datasetToDelete" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 w-full max-w-md mx-4">
        <h2 class="text-xl font-semibold mb-4 text-red-900">Confirm Deletion</h2>
        <p class="text-gray-700 mb-6">
          Are you sure you want to delete "<strong>{{ datasetToDelete.name }}</strong>"? 
          This action cannot be undone and will also delete all related documents and extractions.
        </p>
        
        <div class="flex justify-end space-x-3">
          <button
            @click="datasetToDelete = null"
            class="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
          >
            Cancel
          </button>
          <button
            @click="deleteDataset"
            :disabled="isDeleting"
            class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors disabled:opacity-50"
          >
            {{ isDeleting ? 'Deleting...' : 'Delete' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import { useResourcePool } from '../services/resourcePool.js';
import type { Dataset } from '../types/index.js';

const router = useRouter();

// Get ResourcePool composable
const {
  datasets,
  isLoading,
  error,
  getDocumentsByDataset,
  createDataset,
  updateDataset,
  deleteDataset: deleteDatasetFromPool,
  refresh
} = useResourcePool();

// Component state
const showCreateModal = ref(false);
const editingDataset = ref<Dataset | null>(null);
const activeDropdown = ref<string | null>(null);
const datasetToDelete = ref<Dataset | null>(null);
const isSubmitting = ref(false);
const isDeleting = ref(false);

// Form data
const datasetForm = ref({
  name: '',
  description: '',
  query: '',
});

// Helper functions
const getDocumentCount = (datasetId: string): number => {
  return getDocumentsByDataset(datasetId).length;
};

const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

// Navigation
const navigateToDataset = (datasetId: string) => {
  router.push(`/datasets/${datasetId}`);
};

// Dropdown management
const toggleDropdown = (datasetId: string) => {
  activeDropdown.value = activeDropdown.value === datasetId ? null : datasetId;
};

const closeDropdown = () => {
  activeDropdown.value = null;
};

// Dataset operations
const editDataset = (dataset: Dataset) => {
  editingDataset.value = dataset;
  datasetForm.value = {
    name: dataset.name,
    description: dataset.description || '',
    query: dataset.query,
  };
  closeDropdown();
};

const confirmDelete = (dataset: Dataset) => {
  datasetToDelete.value = dataset;
  closeDropdown();
};

const cancelEdit = () => {
  showCreateModal.value = false;
  editingDataset.value = null;
  datasetForm.value = {
    name: '',
    description: '',
    query: '',
  };
};

const saveDataset = async () => {
  isSubmitting.value = true;
  
  try {
    const datasetData = {
      name: datasetForm.value.name,
      description: datasetForm.value.description || undefined,
      query: datasetForm.value.query,
      version: 1,
      documentCount: 0,
      status: 'ready' as const,
    };

    if (editingDataset.value) {
      await updateDataset(editingDataset.value.id, datasetData);
      console.log('✅ Dataset updated successfully');
    } else {
      await createDataset(datasetData);
      console.log('✅ Dataset created successfully');
    }
    
    cancelEdit();
  } catch (error) {
    console.error('❌ Failed to save dataset:', error);
    // TODO: Show error toast
  } finally {
    isSubmitting.value = false;
  }
};

const deleteDataset = async () => {
  if (!datasetToDelete.value) return;
  
  isDeleting.value = true;
  
  try {
    await deleteDatasetFromPool(datasetToDelete.value.id);
    console.log('✅ Dataset deleted successfully');
    datasetToDelete.value = null;
  } catch (error) {
    console.error('❌ Failed to delete dataset:', error);
    // TODO: Show error toast
  } finally {
    isDeleting.value = false;
  }
};

// Event listeners
onMounted(() => {
  document.addEventListener('click', closeDropdown);
});

onUnmounted(() => {
  document.removeEventListener('click', closeDropdown);
});
</script>


================================================
FILE: src/views/DocumentDetail.vue
================================================
<template>
  <div class="h-screen flex flex-col bg-background">
    <!-- Header with Navigation -->
    <div class="bg-card">
      <div class="container mx-auto px-6 py-4">
        <!-- Document Header -->
        <div class="flex items-center justify-between mb-6">
          <div class="flex items-center space-x-4">
            <button 
              @click="router.push(`/datasets/${props.datasetId}`)"
              class="p-2 hover:bg-accent rounded-lg transition-colors"
            >
              <ArrowLeft class="h-5 w-5" />
            </button>
            
            <div class="flex items-center space-x-3">
              <div class="p-2 bg-primary/10 rounded-md">
                <FileText class="h-5 w-5 text-primary" />
              </div>
                <div>
                  <h1 class="text-xl font-semibold text-foreground">{{ selectedDocument?.name }}</h1>
                  <p v-if="selectedDocument?.type" class="text-sm text-muted-foreground">{{ selectedDocument?.type }}</p>
                </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content Area -->
    <div class="flex-1 grid grid-cols-1 lg:grid-cols-3 gap-4 min-h-0">
      <!-- Left Panel: Document Preview (1/3 width) -->
      <div class="bg-card border border-border rounded-lg overflow-hidden lg:col-span-1">
        <div class="border-b border-border p-4">
          <h2 class="text-lg font-semibold text-foreground">Document Preview</h2>
          <p class="text-sm text-muted-foreground">
            Interactive document with extraction highlights
          </p>
        </div>
        
        <div class="relative h-full overflow-auto">
          <DocumentPreview
            v-if="selectedDocument"
            :document="selectedDocument"
          />
          
          <div v-else class="flex items-center justify-center h-full text-muted-foreground">
            <div class="text-center">
              <FileText class="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No document selected</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Panel: Extracted Data (2/3 width) -->
      <DocumentExtractions
    
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ArrowLeft, FileText, CheckCircle2, XCircle, Play, RotateCcw, Loader2, X, ArrowRight } from 'lucide-vue-next'
import DocumentPreview from '@/components/DocumentPreview.vue'
import DocumentExtractions from '@/components/DocumentExtractionsPanel.vue'
import { calculateCorrectness } from '@/utils/correctness'
import { useResourcePool } from '@/services/resourcePool.js'
import { useWebSocketSync } from '@/composables/useWebSocketSync.js'
import { trpc } from '@/lib/trpc.js'
import { ExtractedValue, Extraction, GroundTruth } from '@/types'

// --- Props ---
interface Props {
  datasetId: string
  documentId: string
}

const props = defineProps<Props>()

// --- Setup ---
const route = useRoute()
const router = useRouter()

// Get ResourcePool composable - ALL data comes from here
const {
  documents, datasets, extractionConfigs, extractions, groundTruths
} = useResourcePool()

// Initialize WebSocket for real-time updates
const { isConnected: wsConnected, error: wsError } = useWebSocketSync(props.documentId)
  
  
// Show WebSocket connection status in dev mode
if (process.env.NODE_ENV === 'development') {
  watch(wsConnected, (connected) => {
    console.log(`🔌 WebSocket ${connected ? 'connected' : 'disconnected'}`)
  })
  
  watch(wsError, (error) => {
    if (error) {
      console.error('🔌 WebSocket error:', error)
    }
  })
}

// Navigation State (simplified)
const navigationData = ref<any>(null)

// --- Computed Properties ---
const selectedDocument = computed(() => {
  return documents.value.find(doc => doc.id === props.documentId) || null;
});

const computedDataset = computed(() => {
  return datasets.value.find(d => d.id === props.datasetId) || null;
});

const activeConfig = computed(() => {
  if (!computedDataset.value?.extractionConfigId) return null;
  return extractionConfigs.value.find(config => config.id === computedDataset.value.extractionConfigId) || null;
});

const documentExtractions = computed(() => {
  return extractions.value.filter(ext => ext.documentId === props.documentId);
});

const extractionsByModel = computed<Record<string, Extraction | null>>(() => {
  const results: Record<string, Extraction | null> = {};
  documentExtractions.value.forEach(extraction => {
    const modelKey = extraction.model;
    results[modelKey] = {
      id: extraction.id,
      documentId: extraction.documentId,
      provider: extraction.provider,
      model: extraction.model,
      processingTime: extraction.processingTime,
      data: extraction.data || {},
      createdAt: extraction.createdAt,
      status: extraction.status
    };
  });
  return results;
});

const groundTruthData = computed<Record<string, GroundTruth>>(() => {
  const formattedGroundTruth: Record<string, GroundTruth> = {};
  groundTruths.value.filter(gt => gt.documentId === props.documentId).forEach(gt => {
    formattedGroundTruth[gt.fieldName] = {
      extractedValue: gt.extractedValue,
      userAnnotation: gt.userAnnotation,
      fieldType: gt.fieldType
    };
  });
  return formattedGroundTruth;
});

const computedAvailableExtractors = computed(() => {
  return Object.values(extractionsByModel.value).map(extraction => extraction?.model).filter((model): model is string => model !== undefined);
});
  
const configFields = computed(() => {
  if (!activeConfig.value?.data?.fields) return [];
  return activeConfig.value.data.fields;
});

const hasAnyAnnotations = computed(() =>
  Object.values(groundTruthData.value).some(
    (gt) => gt.userAnnotation !== null && gt.userAnnotation !== undefined
  )
);

const allFieldNames = computed(() => {
  const fieldNames = new Set<string>();

  return configFields.value.map(field => field.name);
});

const failedExtractions = computed(() => {
  const failed: Array<{extractor: any, error: string}> = [];
  
  for (const extractor of computedAvailableExtractors.value) { // Use computedAvailableExtractors
    const extractorResult = extractionResults.value[extractor.id];
    
    // Check if extraction failed or has error status
    if (extractorStatus.value[extractor.id] === 'error' || extractorResult?.status === 'failed' || extractorResult?.status === 'error') {
      // Try to get error from various possible locations
      let error = 'Extraction failed with unknown error';
      
      if (extractorResult?.error) {
        error = extractorResult.error;
      } else if (extractorResult?.data?.error) {
        // Handle case where error might be an object with value property
        const errorData = extractorResult.data.error;
        error = typeof errorData === 'string' ? errorData : (errorData?.value || JSON.stringify(errorData));
      } else {
        // Get original extraction from extractions array to access full data
        const fullExtraction = extractions.value.find(ext => 
          ext.model === extractor.id && 
          ext.documentId === props.documentId
        );
        
        if (fullExtraction?.error) {
          error = fullExtraction.error;
        }
      }
      
      failed.push({ extractor, error });
    }
  }
  
  return failed;
});

const comparedFields = computed(() => {
  const fields: any[] = [];

  for (const fieldName of allFieldNames.value) {
    const results: Record<string, ExtractedValue> = {};
    
    for (const extractor of computedAvailableExtractors.value) { // Use computedAvailableExtractors
      const extractorResult = extractionResults.value[extractor.id];
      
      // Handle extraction errors
      if (extractorResult?.failed) {
        results[extractor.id] = {
          name: fieldName,
          value: `Error: ${extractorResult.error || 'Extraction failed'}`,
          type: 'text',
          error: true,
          noExtractionYet: false
        } as FieldData;
        continue;
      }
      
      // Check for errors in the extracted data
      const extractedDataError = extractorResult?.data?.error?.value || 
                                 (extractorResult as any)?.extracted_data?.error ||
                                 (extractorResult as any)?.raw_response?.error;
      
      if (extractedDataError) {
        let errorMessage = extractedDataError;
        if (typeof extractedDataError === 'object') {
          errorMessage = JSON.stringify(extractedDataError);
        }
        
        results[extractor.id] = {
          name: fieldName,
          value: `Error: ${errorMessage}`,
          type: 'text',
          error: true,
          noExtractionYet: false
        } as FieldData;
        continue;
      }
      
      const fieldData = extractorResult?.data?.[fieldName];
      
      let fieldType: "text" | "number" | "date" | "boolean" = 'text';
      
      if (activeConfig.value?.data?.fields) {
        const fieldConfig = activeConfig.value.data.fields.find(f => f.name === fieldName)
        if (fieldConfig) {
          fieldType = fieldConfig.fieldType as any;
        }
      }

      // Extract actual ground truth value from detailed structure
      const groundTruth = groundTruthData.value[fieldName]?.hasOwnProperty('user_annotation') 
        ? groundTruthData.value[fieldName].user_annotation 
        : (groundTruthData.value[fieldName]?.extracted_value ?? undefined)
      
      // Determine if no extraction has been run yet
      const noExtractionYet = extractorResult === null || extractorResult === undefined;
      
      // Check if this extractor is currently extracting
      const isCurrentlyExtracting = extractorStatus.value[extractor.id] === 'loading';
      
      // Build the result object
      const result: FieldData = {
        name: fieldName,
        value: noExtractionYet ? null : (fieldData?.value ?? null),
        type: fieldType || 'text',
        groundTruth,
        noExtractionYet,
        isExtracting: isCurrentlyExtracting
      };
      
      // Set correctness score based on ground truth availability
      if (!noExtractionYet && fieldData?.value !== undefined) {
        result.correctnessScore = calculateCorrectness(fieldData.value, groundTruth, fieldType);
      } else {
        result.correctnessScore = null;
      }
      
      results[extractor.id] = result;
    }
    
    fields.push({
      name: fieldName,
      results
    });
  }

  // Sort fields alphabetically by name
  fields.sort((a, b) => a.name.localeCompare(b.name));
  
  return fields;
});

// --- Methods ---
const initializeExtractorStatus = () => {
  const status: Record<string, 'idle' | 'loading' | 'success' | 'error'> = {};
  computedAvailableExtractors.value.forEach(extractor => {
    status[extractor.id] = 'idle';
  });
  extractorStatus.value = status;
};

// Simplified methods - now using ResourcePool async extraction workflow
const runAllExtractions = async () => {
  if (!props.documentId || !activeConfig.value?.id) return;

  loading.value = true;
  initializeExtractorStatus();

  try {
    // Set all extractors to loading state
    computedAvailableExtractors.value.forEach(extractor => {
      extractorStatus.value[extractor.id] = 'loading';
    });

    // Trigger all extractions via tRPC (async + Redis queue)
    const extractorModels = computedAvailableExtractors.value.map(ext => ext.model);
    
    const result = await trpc.resources.extractions.runBatchAsync.mutate({
      documentIds: [props.documentId],
      extractionConfigId: activeConfig.value.id,
      extractorModels: extractorModels
    });

    console.log('✅ All extractions queued:', result);
    
    // Note: Status updates will come via WebSocket and update the UI reactively
    
  } catch (error) {
    console.error('❌ Failed to queue all extractions:', error);
    // Reset status on error
    computedAvailableExtractors.value.forEach(extractor => {
      extractorStatus.value[extractor.id] = 'error';
    });
  } finally {
    loading.value = false;
  }
};

const runSingleExtraction = async (extractorId: string) => {
  if (!props.documentId || !activeConfig.value?.id) {
    console.error('Missing documentId or activeConfig for extraction');
    return;
  }

  console.log(`🚀 Queuing extraction for ${extractorId}`);
  extractorStatus.value[extractorId] = 'loading';

  try {
    // Trigger single extraction via tRPC (async + Redis queue)
    const result = await trpc.resources.extractions.runAsync.mutate({
      documentId: props.documentId,
      extractionConfigId: activeConfig.value.id,
      extractorModel: extractorId
    });

    console.log(`✅ Extraction queued for ${extractorId}:`, result);
    
    // Note: Completion updates will come via WebSocket and update the UI reactively
    
  } catch (error) {
    console.error(`❌ Failed to queue extraction for ${extractorId}:`, error);
    extractorStatus.value[extractorId] = 'error';
    
    // Show user-friendly error message
    let errorMessage = 'Failed to queue extraction';
    if (error instanceof Error) {
      errorMessage = error.message;
    }
    
    // Store error info for display
    // This part needs to be handled by the watch on extractions.value
    // extractionResults.value[extractorId] = {
    //   error: errorMessage,
    //   failed: true
    // } as any;
  }
};
// --- Lifecycle ---
onMounted(async () => {
  // Initialize extractor status when component mounts
  initializeExtractorStatus();
});


</script>


{"name": "cula-extractor-frontend", "private": true, "version": "1.0.0", "type": "module", "packageManager": "pnpm@8.0.0", "scripts": {"dev": "vite --host 0.0.0.0", "build": "vue-tsc --noEmit && vite build", "preview": "vite preview --host 0.0.0.0", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@codemirror/lang-sql": "^6.9.0", "@codemirror/state": "^6.5.2", "@codemirror/theme-one-dark": "^6.1.3", "@codemirror/view": "^6.37.2", "@guolao/vue-monaco-editor": "^1.5.5", "@tailwindcss/vite": "^4.1.11", "@tato30/vue-pdf": "^1.11.3", "@trpc/client": "^10.45.2", "@vueuse/core": "^13.6.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "codemirror": "^6.0.2", "echarts": "^5.6.0", "lucide-vue-next": "^0.294.0", "monaco-editor": "^0.52.2", "radix-vue": "^1.2.6", "reka-ui": "^2.4.0", "shadcn-vue": "^2.2.0", "sql-formatter": "^15.6.5", "tailwind-merge": "^2.2.0", "tailwindcss-animate": "^1.0.7", "vue": "^3.4.0", "vue-codemirror": "^6.1.1", "vue-echarts": "^7.0.3", "vue-router": "^4.2.5", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20.19.9", "@vitejs/plugin-vue": "^6.0.0", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.4.0", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-vue": "^9.18.1", "prettier": "^3.0.3", "tailwindcss": "^4.1.11", "typescript": "~5.2.0", "vite": "^6.3.5", "vite-plugin-vue-devtools": "^7.7.7", "vue-tsc": "^1.8.19"}}
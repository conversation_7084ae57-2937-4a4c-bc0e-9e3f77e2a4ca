# Shadcn Components Guide for Vue.js Monorepo

## Overview

This guide explains how to add and use shadcn components in your Vue.js monorepo. The project is already configured with `shadcn-vue` and has the necessary setup.

## Current Setup ✅

- ✅ `shadcn-vue` installed in `frontend/package.json`
- ✅ `components.json` configured with "new-york" style
- ✅ CSS variables properly set up in `src/index.css`
- ✅ Utils function available in `src/lib/utils.ts`
- ✅ Existing components: Card, Table, Skeleton, Button, Input, Badge

## How to Add New Components

### Method 1: Manual Creation (Recommended)

Since the CLI has pnpm store issues, manually creating components is the most reliable approach:

1. **Create the component directory:**
   ```bash
   mkdir -p src/components/ui/[component-name]
   ```

2. **Create the component file:**
   ```vue
   <!-- src/components/ui/[component-name]/[ComponentName].vue -->
   <script setup lang="ts">
   import { cn } from '@/lib/utils'
   
   interface Props {
     // Define your props here
   }
   
   const props = withDefaults(defineProps<Props>(), {
     // Default values
   })
   </script>
   
   <template>
     <div :class="cn('your-tailwind-classes')">
       <slot />
     </div>
   </template>
   ```

3. **Create an index file:**
   ```typescript
   // src/components/ui/[component-name]/index.ts
   export { default as ComponentName } from './ComponentName.vue'
   ```

4. **Export from main UI index:**
   ```typescript
   // src/components/ui/index.ts
   export * from './[component-name]'
   ```

### Method 2: Using the CLI (When Fixed)

```bash
cd frontend
npx shadcn-vue@latest add [component-name]
```

## Available Components

### Currently Available
- **Card** - `Card`, `CardContent`, `CardDescription`, `CardHeader`, `CardTitle`
- **Table** - `Table`, `TableBody`, `TableCell`, `TableHead`, `TableHeader`, `TableRow`
- **Skeleton** - `Skeleton`
- **Button** - `Button` (with variants: default, destructive, outline, secondary, ghost, link)
- **Input** - `Input` (with v-model support)
- **Badge** - `Badge` (with variants: default, secondary, destructive, outline)

### Common Components to Add
- **Dialog** - Modal dialogs
- **Dropdown Menu** - Context menus
- **Select** - Dropdown selects
- **Checkbox** - Checkbox inputs
- **Radio Group** - Radio button groups
- **Switch** - Toggle switches
- **Textarea** - Multi-line text inputs
- **Label** - Form labels
- **Alert** - Alert messages
- **Toast** - Notification toasts

## Usage Examples

### Basic Usage
```vue
<script setup lang="ts">
import { Button, Input, Badge } from '@/components/ui'

const inputValue = ref('')
</script>

<template>
  <div class="space-y-4">
    <Input v-model="inputValue" placeholder="Enter text..." />
    <Button @click="handleClick">Click me</Button>
    <Badge variant="secondary">Status</Badge>
  </div>
</template>
```

### With Variants
```vue
<template>
  <!-- Button variants -->
  <Button variant="default">Default</Button>
  <Button variant="destructive">Delete</Button>
  <Button variant="outline">Outline</Button>
  <Button variant="secondary">Secondary</Button>
  <Button variant="ghost">Ghost</Button>
  <Button variant="link">Link</Button>
  
  <!-- Button sizes -->
  <Button size="sm">Small</Button>
  <Button size="default">Default</Button>
  <Button size="lg">Large</Button>
  <Button size="icon">+</Button>
  
  <!-- Badge variants -->
  <Badge>Default</Badge>
  <Badge variant="secondary">Secondary</Badge>
  <Badge variant="destructive">Error</Badge>
  <Badge variant="outline">Outline</Badge>
</template>
```

### Form Example
```vue
<script setup lang="ts">
import { ref } from 'vue'
import { Button, Input, Card, CardContent, CardHeader, CardTitle } from '@/components/ui'

const form = ref({
  name: '',
  email: '',
  message: ''
})

const handleSubmit = () => {
  console.log('Form submitted:', form.value)
}
</script>

<template>
  <Card class="w-full max-w-md">
    <CardHeader>
      <CardTitle>Contact Form</CardTitle>
    </CardHeader>
    <CardContent class="space-y-4">
      <div class="space-y-2">
        <label class="text-sm font-medium">Name</label>
        <Input v-model="form.name" placeholder="Your name" />
      </div>
      <div class="space-y-2">
        <label class="text-sm font-medium">Email</label>
        <Input v-model="form.email" type="email" placeholder="<EMAIL>" />
      </div>
      <div class="space-y-2">
        <label class="text-sm font-medium">Message</label>
        <Input v-model="form.message" placeholder="Your message" />
      </div>
      <Button @click="handleSubmit" class="w-full">
        Send Message
      </Button>
    </CardContent>
  </Card>
</template>
```

## Styling and Customization

### Using the `cn` Utility
```vue
<script setup lang="ts">
import { cn } from '@/lib/utils'

const customClasses = cn(
  'base-classes',
  'conditional-classes',
  {
    'conditional-class': someCondition,
    'another-class': anotherCondition
  }
)
</script>
```

### Custom Variants
```vue
<script setup lang="ts">
import { computed } from 'vue'
import { cn } from '@/lib/utils'

interface Props {
  variant?: 'default' | 'custom'
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'default'
})

const componentClasses = computed(() => {
  return cn(
    'base-classes',
    {
      'default-variant-classes': props.variant === 'default',
      'custom-variant-classes': props.variant === 'custom'
    }
  )
})
</script>
```

## Best Practices

1. **Consistent Naming**: Use PascalCase for component names and kebab-case for files
2. **TypeScript**: Always define proper interfaces for props
3. **Composition**: Use Vue 3 Composition API with `<script setup>`
4. **Accessibility**: Include proper ARIA attributes and keyboard navigation
5. **Responsive**: Use Tailwind's responsive prefixes for mobile-first design
6. **Testing**: Create unit tests for complex components

## Troubleshooting

### Common Issues

1. **CSS Variables Not Working**
   - Ensure CSS variables are defined in `src/index.css`
   - Check that Tailwind is properly configured

2. **Component Not Found**
   - Verify the component is exported from `src/components/ui/index.ts`
   - Check import paths are correct

3. **Styling Issues**
   - Use the `cn` utility for conditional classes
   - Ensure Tailwind classes are properly applied

### Getting Help

- [Shadcn Vue Documentation](https://shadcn-vue.com/)
- [Vue 3 Documentation](https://vuejs.org/)
- [Tailwind CSS Documentation](https://tailwindcss.com/)

## Example Component: ShadcnExample.vue

See `src/components/ShadcnExample.vue` for a comprehensive example of how to use all the available components together.
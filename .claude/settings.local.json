{"permissions": {"allow": ["Bash(find:*)", "Bash(rg:*)", "Bash(pnpm add:*)", "Bash(sqlite3:*)", "<PERSON><PERSON>(python3:*)", "Bash(uv pip install:*)", "Bash(rm:*)", "<PERSON><PERSON>(python:*)", "Bash(uv sync:*)", "Bash(pnpm dlx:*)", "Bash(pnpm install:*)", "Bash(npx shadcn-vue:*)", "Bash(npx drizzle-kit generate:pg:*)", "Bash(npm run:*)", "Bash(grep:*)", "Bash(psql:*)", "<PERSON><PERSON>(curl:*)", "Bash(npx vue-tsc:*)", "Bash(pnpm run:*)", "Bash(PGPASSWORD=mysecretpassword psql:*)", "Bash(PGPASSWORD=password psql:*)", "Bash(node:*)", "Bash(echo $GCS_BUCKET_NAME)", "Bash(gcloud storage ls:*)", "Bash(ls:*)", "mcp__playwright__browser_navigate", "Bash(-H \"Content-Type: application/json\")"], "deny": []}}
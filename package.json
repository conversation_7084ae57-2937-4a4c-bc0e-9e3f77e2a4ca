{"name": "cula-extractor", "version": "1.0.0", "private": true, "scripts": {"dev:setup": "docker-compose -f docker-compose.yml -f docker-compose.dev.yml up postgres redis -d && pnpm db:migrate", "dev": "pnpm run dev:setup && concurrently \"pnpm run dev:backend\" \"pnpm run dev:frontend\"", "dev:quick": "concurrently \"pnpm run dev:backend\" \"pnpm run dev:frontend\"", "dev:backend": "cd backend && pnpm run dev", "dev:frontend": "cd frontend && pnpm run dev", "build": "pnpm run build:backend && pnpm run build:frontend", "build:backend": "cd backend && pnpm run build", "build:frontend": "cd frontend && pnpm run build", "db:migrate": "cd backend && pnpm run db:migrate", "db:generate": "cd backend && pnpm run db:generate", "worker": "cd backend && pnpm run worker"}, "devDependencies": {"concurrently": "^8.2.2", "typescript": "^5.3.3"}, "dependencies": {"filesize": "^11.0.2"}}
# Development override for docker-compose.yml
services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: cula-extractor-postgres-dev
    environment:
      POSTGRES_DB: extractor_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "6543:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d extractor_db"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - extractor-network

  # Redis for background job processing
  redis:
    image: redis:7-alpine
    container_name: cula-extractor-redis-dev
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    networks:
      - extractor-network

  # Backend with hot reload
  backend:
    build:
      context: .
      dockerfile: ./backend/Dockerfile.dev
    env_file:
      - ./backend/.env.local
    environment:
      NODE_ENV: development
      LOG_LEVEL: DEBUG
      # File Paths
      DOCUMENTS_PATH: /app/data/documents
      EXTRACTIONS_PATH: /app/data/extractions
      # Redis Configuration
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_DB: 0
    ports:
      - "8000:8000"
    volumes:
      - ./backend/src:/app/src:ro
      - documents_data:/app/data/documents
      - extractions_data:/app/data/extractions
    command: ["pnpm", "run", "dev"]
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_started
    networks:
      - extractor-network

  # Frontend with hot reload
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    env_file:
      - ./frontend/.env.local
    environment:
      NODE_ENV: development
    ports:
      - "3000:3000"
    volumes:
      - ./frontend/src:/app/src:ro
      - ./frontend/package.json:/app/package.json:ro
    command: ["pnpm", "run", "dev"]
    networks:
      - extractor-network

# Use existing volumes and network from main compose
volumes:
  documents_data:
    name: cula_extractor_documents_data
  extractions_data:
    name: cula_extractor_extractions_data
  redis_data:
    name: cula_extractor_redis_data
  postgres_data:
    name: cula_extractor_postgres_data

networks:
  extractor-network:
    driver: bridge
    name: cula-extractor-network